import { fileURLToPath, URL } from 'node:url'
import Vue from '@vitejs/plugin-vue'
import { defineConfig } from 'vitest/config'

export default defineConfig({
  plugins: [Vue()],
  test: {
    environment: 'jsdom',
    globals: true,
  },
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('src', import.meta.url)),
    },
  },
  // Mock CSS imports to avoid issues with Vuetify
  css: {
    modules: {
      classNameStrategy: 'non-scoped',
    },
  },
  define: {
    'process.env': {},
  },
})
