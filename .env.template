# AssemblyAI API
# Get your API key from: https://www.assemblyai.com/dashboard/api-keys
VITE_ASSEMBLYAI_API_KEY=$VITE_ASSEMBLYAI_API_KEY

# Deepgram API Configuration
# Get your API key from: https://console.deepgram.com/
VITE_DEEPGRAM_API_KEY=$VITE_DEEPGRAM_API_KEY

# Gemini API Configuration
# Get your API key from: https://aistudio.google.com/app/apikey
VITE_GEMINI_API_KEY=$VITE_GEMINI_API_KEY

# Gemini Model Configuration (optional)
# Available models: gemini-2.5-flash, gemini-1.5-pro, etc.
# Default: gemini-2.5-flash
VITE_GEMINI_MODEL=$VITE_GEMINI_MODEL

# Groq API Configuration
# Get your API key from: https://console.groq.com/
VITE_GROQ_API_KEY=$VITE_GROQ_API_KEY

# Groq Model Configuration (optional)
# Available models: llama-3.3-70b-versatile, llama-3.1-70b-versatile, mistral-8x7b-32768, etc.
# Default: llama-3.3-70b-versatile
VITE_GROQ_MODEL=$VITE_GROQ_MODEL

# Langfuse Configuration
VITE_LANGFUSE_SECRET_KEY=$VITE_LANGFUSE_SECRET_KEY
VITE_LANGFUSE_PUBLIC_KEY=$VITE_LANGFUSE_PUBLIC_KEY
VITE_LANGFUSE_BASE_URL=$VITE_LANGFUSE_BASE_URL