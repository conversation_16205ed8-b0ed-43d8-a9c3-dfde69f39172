{"name": "sentient-ui", "private": true, "type": "module", "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --fix", "test": "pnpm test:unit:run && pnpm test:integration:run && pnpm test:e2e", "test:unit": "vitest --config vitest.config.js tests/unit/", "test:unit:run": "vitest run --config vitest.config.js tests/unit/", "test:integration": "vitest --config vitest.config.js tests/integration/", "test:integration:run": "vitest run --config vitest.config.js tests/integration/", "test:vitest": "vitest --config vitest.config.js", "test:vitest:run": "vitest run --config vitest.config.js", "test:e2e": "playwright test --reporter=line", "test:e2e:ui": "playwright test --ui", "test:e2e:debug": "playwright test --debug", "test:e2e:html": "playwright test --reporter=html"}, "dependencies": {"@deepgram/sdk": "^4.4.0", "@fontsource/roboto": "5.2.5", "@google/genai": "^0.8.0", "@mdi/font": "7.4.47", "assemblyai": "^4.14.2", "buffer": "^6.0.3", "cross-fetch": "^4.1.0", "echarts": "^5.6.0", "groq-sdk": "^0.25.0", "langfuse": "^3.38.4", "localforage": "^1.10.0", "uuid": "^11.1.0", "vue": "^3.5.13", "vuetify": "^3.8.1"}, "devDependencies": {"@playwright/test": "^1.53.1", "@vitejs/plugin-vue": "^5.2.3", "@vue/test-utils": "^2.4.6", "eslint": "^9.23.0", "eslint-config-vuetify": "^4.0.0", "globals": "^16.0.0", "jsdom": "^26.1.0", "sass-embedded": "^1.86.3", "unplugin-fonts": "^1.3.1", "unplugin-vue-components": "^28.4.1", "unplugin-vue-router": "^0.12.0", "vite": "^6.2.2", "vite-plugin-vuetify": "^2.1.1", "vitest": "^3.2.4", "vue-router": "^4.5.0"}, "packageManager": "pnpm@10.14.0+sha512.ad27a79641b49c3e481a16a805baa71817a04bbe06a38d17e60e2eaee83f6a146c6a688125f5792e48dd5ba30e7da52a5cda4c3992b9ccf333f9ce223af84748"}