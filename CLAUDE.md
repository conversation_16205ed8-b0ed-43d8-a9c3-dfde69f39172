# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Technical Summary

Sentient UI is a frontend-only, voice-driven platform (CRM) built using Vue 3, Vuetify, and JavaScript. 
It enables users to control complex application using natural language commands, which are interpreted in real time 
using an always-on speech recognition engine (Deepgram) and a fast LLM reasoning core (e.g., Gemini Flash). 
The system maps user speech to context-aware UI actions using a dual-layer context architecture.

## Development Commands

- **Development server**: `pnpm dev` (starts at http://localhost:3000)
- **Build for production**: `pnpm build`
- **Preview production build**: `pnpm preview`
- **Lint and fix**: `pnpm run lint`

## Architecture Overview

This is a Vue 3 + Vuetify 3 application built with Vite. Key architectural patterns:

- **File-based routing**: Pages in `src/pages/` are automatically registered as routes via `unplugin-vue-router`
- **Auto-imported components**: Components are automatically imported using `unplugin-vue-components`
- **Plugin architecture**: All plugins are registered through `src/plugins/index.js`
- **Vuetify theming**: Custom styles configured in `src/styles/settings.scss`

## Key Configuration

- **Vite config**: `vite.config.mjs` includes Vuetify auto-import, component auto-import, and font optimization
- **Alias**: `@` points to `src/` directory
- **Router**: Uses Vue Router with auto-routes, includes dynamic import error handling workaround
- **Dev server**: Runs on port 3000 by default

## Project Structure

- `src/pages/` - File-based routes (automatically registered)
- `src/components/` - Reusable Vue components (auto-imported)
- `src/plugins/` - Vue plugins registration
- `src/styles/` - SCSS styling and Vuetify theme customization

## Working with repo

- IMPORTANT: Do not mention "Claude Code" in generated commit messages
- Prefer brief commit messages

## Working with user requests

- If the request involves any changes to the code, you should prepare and share a detailed implementation plan of these changes, and explicitly 
  approve the plan with the user before proceeding.
- IMPORTANT: Do not run/use the dev server to verify implementation: you do not have possibility to check the correctness of the most changes this way.
- Do not introduce extra functionality (e.g., extra methods in the class for debugging) if it's not used, not required for the provided requirements, or not asked explicitly.
- Utilize Jetbrains MCP 