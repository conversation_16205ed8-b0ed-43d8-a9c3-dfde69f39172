/**
 * Voice Agent Panel Styles
 * 
 * Comprehensive styling for the VoiceAgentPanel component including
 * layout, animations, responsive design, and state-specific styling.
 */

.voice-agent-panel {
  position: fixed;
  top: 64px; /* Below AppBar */
  right: 0;
  width: 350px;
  height: calc(100vh - 64px);
  background: white;
  border-left: 1px solid #e0e0e0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.panel-state {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Not Started State */
.panel-state--not-started {
  justify-content: flex-start;
  align-items: stretch;
  padding: 0;
}

.upper-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  padding-top: 3rem;
}

.not-started-content {
  text-align: center;
}

.lower-section {
  flex: 0.5;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 2rem;
  padding-top: 1rem;
}

.examples-section {
  width: 100%;
  max-width: 280px;
}

.examples-title {
  font-size: 1.0rem;
  font-weight: 500;
  color: #424242;
  margin-bottom: 0.5rem;
  text-align: center;
}

.examples-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.example-item {
  padding: 0.5rem 0;
  color: #757575;
  font-size: 0.9rem;
  font-style: italic;
  text-align: left;
  border-bottom: 1px solid #f0f0f0;
  transition: color 0.2s ease;
}

.example-item:last-child {
  border-bottom: none;
}

.example-item:hover {
  color: #424242;
}

.panel-title {
  font-size: 1.5rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #424242;
}

.panel-subtitle {
  font-size: 1rem;
  color: #757575;
  margin-bottom: 2rem;
}

.start-button {
  color: white;
  background-color: #C0142A;
  border-radius: 50%;
  width: 80px !important;
  height: 80px !important;
  box-shadow: 0 4px 12px rgba(192, 20, 42, 0.3);
}

/* Button animations */
.pulse-animation {
  animation: pulse-gentle 2s infinite;
}

@keyframes pulse-gentle {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(192, 20, 42, 0.3);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(192, 20, 42, 0.4);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(192, 20, 42, 0.3);
  }
}

/* Listening State */
.panel-state--listening {
  padding: 0;
}

.control-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e0e0e0;
  background: #f5f5f5;
}

.control-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-content {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-text {
  font-weight: 500;
  color: #424242;
}

.animated-dots::after {
  content: '';
  animation: dots 1.5s infinite;
}

@keyframes dots {
  0%, 20% {
    content: '';
  }
  25%, 45% {
    content: '.';
  }
  50%, 70% {
    content: '..';
  }
  75%, 95% {
    content: '...';
  }
}

.microphone-icon {
  transition: all 0.3s ease;
}

.microphone-icon--listening {
  animation: microphone-pulse 1.2s infinite;
}

.microphone-icon--processing {
  animation: pulse-fast 0.8s infinite;
}

/* Conversation Section */
.conversation-section {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.conversation-content {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

/* Empty State Styling */
.empty-state {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-state-message {
  text-align: center;
  max-width: 280px;
}

.empty-state-icon {
  margin-bottom: 1rem;
}

.empty-state-title {
  font-size: 1.25rem;
  font-weight: 500;
  color: #424242;
  margin-bottom: 0.5rem;
}

.empty-state-subtitle {
  font-size: 1rem;
  color: #757575;
  margin: 0;
}

.interaction {
  margin-bottom: 1rem;
}

.message {
  margin-bottom: 1rem;
}

.message-label {
  font-weight: 600;
  font-size: 0.875rem;
  color: #424242;
  margin-bottom: 0.25rem;
}

.message-text {
  color: #616161;
  line-height: 1.4;
}

.user-message .message-label {
  color: #1976d2;
}

.agent-message .message-label {
  color: #388e3c;
}

.plan-message .message-label {
  color: #f57c00;
}

.execution-plan-list {
  margin: 0;
  padding-left: 1rem;
  color: #616161;
}

.plan-step {
  margin-bottom: 0.25rem;
  line-height: 1.4;
}

.processing-indicator,
.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 4px;
  font-size: 0.875rem;
}

.processing-indicator {
  background: #e3f2fd;
  color: #1976d2;
}

.error-message {
  background: #ffebee;
  color: #c62828;
}

.processing-icon {
  animation: pulse-fast 0.8s infinite;
}

.connecting-icon {
  animation: pulse-slow 2s infinite;
}

.pulsing-icon {
  animation: pulse-gentle-icon 2s infinite;
}

.loading-icon {
  animation: rotate 2s linear infinite;
}

/* Animations */
@keyframes microphone-pulse {
  0% {
    transform: scale(1);
    opacity: 1;
    color: #C0142A;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.6;
    color: #ff4569;
  }
  100% {
    transform: scale(1);
    opacity: 1;
    color: #C0142A;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse-fast {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.15);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse-slow {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

@keyframes pulse-gentle-icon {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .voice-agent-panel {
    width: 300px;
  }
}

@media (max-width: 480px) {
  .voice-agent-panel {
    width: 280px;
  }

  .start-button {
    width: 70px !important;
    height: 70px !important;
  }

  .panel-title {
    font-size: 1.25rem;
  }
}