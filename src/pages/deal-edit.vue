<template>
  <v-container class="pa-8">
    <v-row v-if="loading">
      <v-col>
        <v-progress-circular indeterminate />
      </v-col>
    </v-row>

    <v-row v-else-if="!deal">
      <v-col>
        <v-alert type="error" variant="outlined">
          Deal not found
        </v-alert>
      </v-col>
    </v-row>

    <div v-else>
      <h1 class="text-h4 mb-8">Update Deal</h1>

      <v-form ref="form" @submit.prevent="saveDeal">
        <v-row>
          <v-col cols="12" md="6">
            <v-text-field
              v-model="editedDeal.title"
              class="mb-4"
              label="Deal Title"
              required
              :rules="[v => !!v || 'Title is required']"
              variant="underlined"
            />
          </v-col>
          <v-col cols="12" md="6">
            <v-text-field
              v-model.number="editedDeal.budget"
              class="mb-4"
              label="Budget"
              prefix="$"
              required
              :rules="[v => !!v || 'Budget is required', v => v > 0 || 'Budget must be greater than 0']"
              type="number"
              variant="underlined"
            />
          </v-col>
        </v-row>

        <v-row>
          <v-col cols="12" md="6">
            <v-select
              v-model="editedDeal.status"
              class="mb-4"
              :items="statusOptions"
              label="Status"
              required
              :rules="[v => !!v || 'Status is required']"
              variant="underlined"
            >
              <template #selection="{ item }">
                <v-chip
                  :color="getStatusColor(item.value)"
                  size="small"
                  variant="flat"
                >
                  {{ item.title }}
                </v-chip>
              </template>
              <template #item="{ item, props }">
                <v-list-item v-bind="props" class="pa-2">
                  <v-chip
                    :color="getStatusColor(item.value)"
                    size="small"
                    variant="flat"
                  >
                    {{ item.title }}
                  </v-chip>
                </v-list-item>
              </template>
            </v-select>
          </v-col>
          <v-col cols="12" md="6">
            <v-select
              v-model="editedDeal.company"
              class="mb-4"
              item-title="name"
              item-value="id"
              :items="companyOptions"
              label="Company"
              required
              :rules="[v => !!v || 'Company is required']"
              variant="underlined"
            >
              <template #selection="{ item }">
                <div class="d-flex align-center">
                  <v-avatar
                    class="mr-2"
                    size="24"
                  >
                    <v-img
                      :alt="item.title"
                      :src="item.raw.logo"
                    >
                      <template #error>
                        <v-icon size="16">mdi-domain</v-icon>
                      </template>
                    </v-img>
                  </v-avatar>
                  <span>{{ item.title }}</span>
                </div>
              </template>
              <template #item="{ item, props }">
                <v-list-item v-bind="props">
                  <template #prepend>
                    <v-avatar
                      class="mr-2"
                      size="32"
                    >
                      <v-img
                        :alt="item.title"
                        :src="item.raw.logo"
                      >
                        <template #error>
                          <v-icon size="20">mdi-domain</v-icon>
                        </template>
                      </v-img>
                    </v-avatar>
                  </template>
                </v-list-item>
              </template>
            </v-select>
          </v-col>
        </v-row>

        <v-row>
          <v-col cols="12" md="6">
            <v-select
              v-model="editedDeal.priority"
              class="mb-4"
              :items="priorityOptions"
              label="Priority"
              required
              :rules="[v => !!v || 'Priority is required']"
              variant="underlined"
            >
              <template #selection="{ item }">
                <div class="d-flex align-center">
                  <v-icon
                    :color="getPriorityIconColor(item.value)"
                    size="18"
                  >
                    {{ getPriorityIcon(item.value) }}
                  </v-icon>
                  <span class="ml-2 text-body-2" :class="getPriorityTextClass(item.value)">
                    {{ item.title }}
                  </span>
                </div>
              </template>
              <template #item="{ item, props }">
                <v-list-item v-bind="props">
                  <template #prepend>
                    <v-icon
                      :color="getPriorityIconColor(item.value)"
                      size="18"
                    >
                      {{ getPriorityIcon(item.value) }}
                    </v-icon>
                  </template>
                  <template #title>
                    <span :class="getPriorityTextClass(item.value)">
                      {{ item.title }}
                    </span>
                  </template>
                </v-list-item>
              </template>
            </v-select>
          </v-col>
          <v-col cols="12" md="6">
            <v-select
              v-model="editedDeal.dealOwner"
              class="mb-4"
              :items="userOptions"
              label="Deal Owner"
              required
              :rules="[v => !!v || 'Deal Owner is required']"
              variant="underlined"
            >
              <template #selection="{ item }">
                <div class="d-flex align-center">
                  <v-avatar
                    color="secondary"
                    size="24"
                  >
                    <span class="text-white text-caption font-weight-bold">
                      {{ getInitials(item.title) }}
                    </span>
                  </v-avatar>
                  <span class="ml-2">{{ item.title }}</span>
                </div>
              </template>
              <template #item="{ item, props }">
                <v-list-item v-bind="props">
                  <template #prepend>
                    <v-avatar
                      color="secondary"
                      size="32"
                    >
                      <span class="text-white text-caption font-weight-bold">
                        {{ getInitials(item.title) }}
                      </span>
                    </v-avatar>
                  </template>
                </v-list-item>
              </template>
            </v-select>
          </v-col>
        </v-row>
      </v-form>

      <v-row class="mt-8">
        <v-col class="d-flex justify-end">
          <v-btn
            class="mr-4"
            variant="text"
            @click="goBack"
          >
            CANCEL
          </v-btn>
          <v-btn
            color="primary"
            :loading="saving"
            @click="saveDeal"
          >
            SAVE
          </v-btn>
        </v-col>
      </v-row>
    </div>
  </v-container>
</template>

<script setup>
  import { onMounted, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useVoiceAgent } from '@/composables/useVoiceAgent.js'
  import { Deal } from '@/models/Deal'
  import { CompanyRepository } from '@/repositories/CompanyRepository'
  import { DealRepository } from '@/repositories/DealRepository'
  import { UserRepository } from '@/repositories/UserRepository'
  import { EditHistoryService } from '@/services/EditHistoryService.js'

  const route = useRoute()
  const router = useRouter()
  const dealRepository = new DealRepository()
  const companyRepository = new CompanyRepository()
  const userRepository = new UserRepository()
  const editHistory = new EditHistoryService()

  const deal = ref(null)
  const company = ref(null)
  const users = ref([])
  const editedDeal = ref({
    title: '',
    status: '',
    priority: '',
    budget: 0,
    dealOwner: '',
    company: '',
  })
  const loading = ref(true)
  const saving = ref(false)
  const form = ref(null)

  const statusOptions = [
    { value: Deal.STATUS.IDENTIFICATION, title: 'Identification' },
    { value: Deal.STATUS.PROPOSAL, title: 'Proposal' },
    { value: Deal.STATUS.NEGOTIATION, title: 'Negotiation' },
    { value: Deal.STATUS.CLOSED_WON, title: 'Closed Won' },
    { value: Deal.STATUS.CLOSED_LOST, title: 'Closed Lost' },
  ]

  const priorityOptions = [
    { value: Deal.PRIORITY.LOW, title: 'Low' },
    { value: Deal.PRIORITY.MEDIUM, title: 'Medium' },
    { value: Deal.PRIORITY.HIGH, title: 'High' },
  ]

  const userOptions = ref([])
  const companyOptions = ref([])

  const loadDeal = async () => {
    try {
      const dealId = route.query.id
      if (!dealId) {
        return router.replace('/deals')
      }

      await dealRepository.seedData()
      deal.value = await dealRepository.getById(dealId)

      if (!deal.value) {
        console.error('Deal not found')
        return
      }

      // Load related company
      if (deal.value.company) {
        await companyRepository.seedData()
        company.value = await companyRepository.getById(deal.value.company)
      }

      // Load users for deal owner selection
      await userRepository.seedData()
      users.value = await userRepository.getAll()
      userOptions.value = users.value.map(user => ({
        value: user.id,
        title: user.fullName,
      }))

      // Load companies for dropdown
      companyOptions.value = await companyRepository.getAll()

      // Initialize form with deal data
      editedDeal.value = {
        title: deal.value.title,
        status: deal.value.status,
        priority: deal.value.priority,
        budget: deal.value.budget,
        dealOwner: deal.value.dealOwner,
        company: deal.value.company,
      }

      editHistory.setModel(deal.value)
    } catch (error) {
      console.error('Error loading deal:', error)
    } finally {
      loading.value = false
    }
  }

  const getStatusColor = status => {
    const deal = new Deal({ status })
    return deal.statusColor
  }

  const getPriorityIcon = priority => {
    switch (priority) {
      case 'low': { return 'mdi-chevron-down'
      }
      case 'medium': { return 'mdi-drag-horizontal'
      }
      case 'high': { return 'mdi-chevron-up'
      }
      default: { return 'mdi-drag-horizontal'
      }
    }
  }

  const getPriorityIconColor = priority => {
    switch (priority) {
      case 'low': { return 'blue'
      }
      case 'medium': { return 'orange'
      }
      case 'high': { return 'red'
      }
      default: { return 'grey'
      }
    }
  }

  const getPriorityTextClass = priority => {
    switch (priority) {
      case 'low': { return 'text-blue'
      }
      case 'medium': { return 'text-orange'
      }
      case 'high': { return 'text-red'
      }
      default: { return 'text-grey'
      }
    }
  }

  const getInitials = name => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const goBack = () => {
    router.back()
  }

  const saveDeal = async () => {
    if (!form.value) return

    const { valid } = await form.value.validate()
    if (!valid) return

    saving.value = true

    try {
      const updateData = {
        ...editedDeal.value,
      }

      await dealRepository.update(route.query.id, updateData)
      router.back()
    } catch (error) {
      console.error('Error saving deal:', error)
    } finally {
      saving.value = false
    }
  }

  const changeField = async (field, value) => {
    if (field in editedDeal.value) {
      // Special handling for dealOwner and company fields
      if (field === 'dealOwner') {
        value = await searchDealOwner(value)
      } else if (field === 'company') {
        value = await searchCompany(value)
      }

      if (value !== null) {
        editHistory.remember(field, editedDeal.value[field])
        editedDeal.value[field] = value
      }
    }
  }

  const searchDealOwner = async value => {
    if (!value || typeof value !== 'string') {
      console.warn('[deal-edit] Invalid dealOwner value provided:', value)
      return null
    }

    try {
      const searchResults = await userRepository.search(value.trim())

      if (searchResults.length === 0) {
        console.warn('[deal-edit] No users found matching:', value)
        return null
      }

      const selectedUser = searchResults[0]
      console.log(`[deal-edit] Resolved dealOwner "${value}" to user:`, selectedUser.fullName, selectedUser.id)

      return selectedUser.id
    } catch (error) {
      console.error('[deal-edit] Error searching for user:', error)
      return null
    }
  }

  const searchCompany = async value => {
    if (!value || typeof value !== 'string') {
      console.warn('[deal-edit] Invalid company value provided:', value)
      return null
    }

    try {
      const searchResults = await companyRepository.search(value.trim())

      if (searchResults.length === 0) {
        console.warn('[deal-edit] No companies found matching:', value)
        return null
      }

      const selectedCompany = searchResults[0]
      console.log(`[deal-edit] Resolved company "${value}" to:`, selectedCompany.name, selectedCompany.id)

      return selectedCompany.id
    } catch (error) {
      console.error('[deal-edit] Error searching for company:', error)
      return null
    }
  }

  const resetField = field => {
    if (field in editedDeal.value) {
      editedDeal.value[field] = editHistory.restore(field)
    }
  }

  const resetFrom = () => {
    for (const field in editedDeal.value) {
      editedDeal.value[field] = editHistory.restore(field)
    }
  }

  const resetFormTool = {
    name: 'resetFormTool',
    description: 'Reset, restore, revert, undo, cancel ALL changes made in form fields',
    params: [],
    tool: resetFrom,
  }

  const setBudget = {
    name: 'setBudget',
    description: 'Set deal budget',
    params: [
      {
        type: 'number',
        required: true,
      },
    ],
    tool: value => {
      editedDeal.value.budget = Number(value)
    },
  }

  useVoiceAgent({
    description: `Deal edit form:
Form fields: ${Object.keys(editedDeal.value).join(',')};
Status values: ${statusOptions.map(i => i.value).join(',')};
Priority values: ${priorityOptions.map(i => i.value).join(',')};`,
    tools: {
      setBudget,
      changeField,
      resetField,
      resetFormTool,
      cancel: goBack,
      save: saveDeal,
    },
  })

  onMounted(() => {
    loadDeal()
  })
</script>
