<template>
  <v-container>
    <DealsManager
      ref="dealsManager"
      @refresh="handleRefresh"
    />
  </v-container>
</template>

<script setup>
  import { onMounted, ref } from 'vue'
  import { useRouter } from 'vue-router'

  const router = useRouter()
  const dealsManager = ref(null)

  // Initialize the deals manager component on mount
  onMounted(() => {
    if (dealsManager.value) {
      dealsManager.value.initializeFromUrl()
    }
  })

  // Handle refresh events from the deals manager
  const handleRefresh = () => {
    // Force navigation to the same route to refresh state
    router.push('/deals')
  }
</script>
