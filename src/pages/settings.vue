<template>
  <v-container>
    <v-row>
      <v-col>
        <h1>Settings</h1>
      </v-col>
    </v-row>

    <v-row>
      <v-col>
        <v-tabs v-model="activeTab" color="primary">
          <v-tab value="users">Users</v-tab>
        </v-tabs>

        <v-tabs-window v-model="activeTab">
          <v-tabs-window-item value="users">
            <v-card class="mt-4">
              <v-card-title>
                <h2>Users</h2>
              </v-card-title>
              <v-card-text>
                <UserTable />
              </v-card-text>
            </v-card>
          </v-tabs-window-item>
        </v-tabs-window>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
  import { ref } from 'vue'

  const activeTab = ref('users')
</script>
