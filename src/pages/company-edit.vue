<template>
  <v-container>
    <v-row>
      <v-col>
        <v-btn
          class="mb-4"
          prepend-icon="mdi-arrow-left"
          variant="text"
          @click="goBack"
        >
          Back
        </v-btn>
      </v-col>
    </v-row>

    <v-row v-if="loading">
      <v-col>
        <v-progress-circular indeterminate />
      </v-col>
    </v-row>

    <v-row v-else-if="company">
      <v-col>
        <v-card>
          <v-card-title class="d-flex align-center">
            <span class="text-h4">Update Company</span>
          </v-card-title>

          <v-card-text>
            <v-form ref="form" v-model="valid">
              <v-row>
                <v-col cols="12">
                  <v-text-field
                    v-model="editedCompany.name"
                    label="Name"
                    required
                    :rules="[rules.required]"
                  />
                </v-col>
              </v-row>

              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="editedCompany.phoneNumber"
                    label="Phone Number"
                    required
                    :rules="[rules.required]"
                  />
                </v-col>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="editedCompany.email"
                    label="Email"
                    required
                    :rules="[rules.required]"
                    type="email"
                  />
                </v-col>
              </v-row>

              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="editedCompany.location"
                    label="Location"
                    required
                    :rules="[rules.required]"
                  />
                </v-col>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="editedCompany.industry"
                    label="Industry"
                    required
                    :rules="[rules.required]"
                  />
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>

          <v-card-actions>
            <v-spacer />
            <v-btn
              text
              @click="goBack"
            >
              Cancel
            </v-btn>
            <v-btn
              color="primary"
              :disabled="!valid"
              @click="saveCompany"
            >
              Save
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>

    <v-row v-else>
      <v-col>
        <v-alert type="error" variant="outlined">
          Company not found
        </v-alert>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
  import { onMounted, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useVoiceAgent } from '@/composables/useVoiceAgent'
  import { CompanyRepository } from '@/repositories/CompanyRepository'
  import { EditHistoryService } from '@/services/EditHistoryService'

  const route = useRoute()
  const router = useRouter()
  const company = ref(null)
  const loading = ref(true)
  const valid = ref(false)
  const form = ref(null)
  const companyRepository = new CompanyRepository()
  const editHistory = new EditHistoryService()

  const editedCompany = ref({
    name: '',
    email: '',
    phoneNumber: '',
    location: '',
    industry: '',
  })

  const rules = {
    required: value => !!value || 'This field is required',
  }

  const loadCompany = async () => {
    try {
      loading.value = true
      await companyRepository.seedData()
      company.value = await companyRepository.getById(route.query.id)

      if (company.value) {
        editedCompany.value = {
          name: company.value.name,
          email: company.value.email,
          phoneNumber: company.value.phoneNumber,
          location: company.value.location,
          industry: company.value.industry,
        }
        editHistory.setModel(company.value)
      }
    } catch (error) {
      console.error('Error loading company:', error)
    } finally {
      loading.value = false
    }
  }

  const saveCompany = async () => {
    if (form.value) {
      const { valid: isValid } = await form.value.validate()
      if (isValid) {
        try {
          await companyRepository.update(route.query.id, editedCompany.value)
          router.back()
        } catch (error) {
          console.error('Error updating company:', error)
        }
      }
    }
  }

  const changeField = (field, value) => {
    if (field in editedCompany.value) {
      editHistory.remember(field, editedCompany.value[field])
      editedCompany.value[field] = value
    }
  }

  const resetField = field => {
    if (field in editedCompany.value) {
      editedCompany.value[field] = editHistory.restore(field)
    }
  }

  const resetFrom = () => {
    for (const field in editedCompany.value) {
      editedCompany.value[field] = editHistory.restore(field)
    }
  }

  const resetFormTool = {
    name: 'resetFormTool',
    description: 'Reset, restore, revert, undo, cancel ALL changes made in form fields',
    params: [],
    tool: resetFrom,
  }

  const goBack = () => {
    router.back()
  }

  useVoiceAgent({
    description: `Company edit form:
Form fields: ${Object.keys(editedCompany.value).join(',')};`,
    tools: {
      changeField,
      resetField,
      resetFormTool,
      cancel: goBack,
      save: saveCompany,
    },
  })

  onMounted(() => {
    loadCompany()
  })
</script>
