<template>
  <v-container>
    <CompaniesManager
      ref="companiesManager"
      @refresh="handleRefresh"
    />
  </v-container>
</template>

<script setup>
  import { onMounted, ref } from 'vue'
  import { useRouter } from 'vue-router'

  const router = useRouter()
  const companiesManager = ref(null)

  // Initialize the companies manager component on mount
  onMounted(() => {
    if (companiesManager.value) {
      companiesManager.value.initializeFromUrl()
    }
  })

  // Handle refresh events from the companies manager
  const handleRefresh = () => {
    // Force navigation to the same route to refresh state
    router.push('/companies')
  }
</script>
