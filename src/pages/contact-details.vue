<template>
  <v-container>
    <v-row>
      <v-col>
        <v-btn
          class="mb-4"
          prepend-icon="mdi-arrow-left"
          variant="text"
          @click="goBack"
        >
          Back to Contacts
        </v-btn>
      </v-col>
    </v-row>

    <v-row v-if="loading">
      <v-col>
        <v-progress-circular indeterminate />
      </v-col>
    </v-row>

    <v-row v-else-if="contact">
      <v-col>
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-avatar class="mr-4" color="primary" size="48">
              <span class="text-white text-h6">{{ getInitials(contact) }}</span>
            </v-avatar>
            <field-history field="firstName" :model="contact">
              <span class="text-h4">{{ contact.firstName }}</span>
            </field-history>
            <field-history field="lastName" :model="contact">
              <span class="text-h4">{{ contact.lastName }}</span>
            </field-history>
          </v-card-title>

          <v-card-text>
            <v-row>
              <v-col cols="12" md="6">
                <v-list density="compact">
                  <v-list-item>
                    <v-list-item-title class="text-subtitle-2">Email</v-list-item-title>
                    <v-list-item-subtitle opacity="1">
                      <field-history field="email" :model="contact">
                        {{ contact.email }}
                      </field-history>
                    </v-list-item-subtitle>
                  </v-list-item>

                  <v-list-item>
                    <v-list-item-title class="text-subtitle-2">Phone</v-list-item-title>
                    <v-list-item-subtitle opacity="1">
                      <field-history field="phoneNumber" :model="contact">
                        {{ contact.formattedPhone }}
                      </field-history>
                    </v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </v-col>

              <v-col cols="12" md="6">
                <v-list density="compact">
                  <v-list-item>
                    <v-list-item-title class="text-subtitle-2">Company</v-list-item-title>
                    <v-list-item-subtitle opacity="1">
                      <field-history field="company" :format="formatCompany" :model="contact">
                        <div
                          v-if="company"
                          class="d-flex align-center cursor-pointer"
                          @click="navigateToCompany"
                        >
                          <v-avatar
                            class="mr-3"
                            size="32"
                          >
                            <v-img
                              :alt="company.name"
                              :src="company.logo"
                            >
                              <template #error>
                                <v-icon class="mt-1">mdi-domain</v-icon>
                              </template>
                            </v-img>
                          </v-avatar>
                          <div>
                            <div class="font-weight-medium">{{ company.name }}</div>
                            <div class="text-caption text-grey">{{ company.industry }}</div>
                          </div>
                        </div>
                        <span v-else class="text-grey">Company not found</span>
                      </field-history>
                    </v-list-item-subtitle>
                  </v-list-item>

                  <v-list-item>
                    <v-list-item-title class="text-subtitle-2">Contact Owner</v-list-item-title>
                    <v-list-item-subtitle opacity="1">
                      <field-history field="contactOwner" :format="formatContactOwner" :model="contact">
                        <div
                          v-if="contactOwner"
                          class="d-flex align-center"
                        >
                          <v-avatar
                            class="mr-3"
                            color="secondary"
                            size="32"
                          >
                            <span class="text-white text-caption font-weight-bold">
                              {{ getInitials({ firstName: contactOwner.firstName, lastName: contactOwner.lastName }) }}
                            </span>
                          </v-avatar>
                          <div>
                            <div class="font-weight-medium">{{ contactOwner.fullName }}</div>
                            <div class="text-caption text-grey">{{ contactOwner.email }}</div>
                          </div>
                        </div>
                        <span v-else class="text-grey">User not found</span>
                      </field-history>
                    </v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </v-col>
            </v-row>
          </v-card-text>

          <v-card-actions>
            <v-spacer />
            <v-btn
              color="primary"
              @click="editContact"
            >
              Modify
            </v-btn>
          </v-card-actions>
        </v-card>

        <ActivityList
          :contact="contact"
          :contact-id="contact.id"
        />
      </v-col>
    </v-row>

    <v-row v-else>
      <v-col>
        <v-alert type="error" variant="outlined">
          Contact not found
        </v-alert>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
  import { onMounted, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import ActivityList from '@/components/activities/ActivityList.vue'
  import FieldHistory from '@/components/common/FieldHistory'
  import { useVoiceAgent } from '@/composables/useVoiceAgent'
  import { CompanyRepository } from '@/repositories/CompanyRepository'
  import { ContactRepository } from '@/repositories/ContactRepository'
  import { UserRepository } from '@/repositories/UserRepository'

  const route = useRoute()
  const router = useRouter()
  const contact = ref(null)
  const company = ref(null)
  const contactOwner = ref(null)
  const loading = ref(true)
  const contactRepository = new ContactRepository()
  const companyRepository = new CompanyRepository()
  const userRepository = new UserRepository()

  const loadContact = async () => {
    try {
      const contactId = route.query.id
      if (!contactId) {
        router.push('/contacts')
        return
      }

      await contactRepository.seedData()
      contact.value = await contactRepository.getById(contactId)

      if (!contact.value) {
        console.error('Contact not found')
        return
      }

      // Load related company
      if (contact.value.company) {
        await companyRepository.seedData()
        company.value = await companyRepository.getById(contact.value.company)
      }

      // Load contact owner
      if (contact.value.contactOwner) {
        await userRepository.seedData()
        contactOwner.value = await userRepository.getById(contact.value.contactOwner)
      }
    } catch (error) {
      console.error('Error loading contact:', error)
    } finally {
      loading.value = false
    }
  }

  const getInitials = contact => {
    if (!contact.firstName && !contact.lastName) return '?'
    const firstInitial = contact.firstName ? contact.firstName[0].toUpperCase() : ''
    const lastInitial = contact.lastName ? contact.lastName[0].toUpperCase() : ''
    return firstInitial + lastInitial
  }

  const navigateToCompany = () => {
    if (company.value) {
      router.push(`/company-details?id=${company.value.id}`)
    }
  }

  const editContact = () => {
    router.push(`/contact-edit?id=${route.query.id}`)
  }

  const goBack = () => {
    router.push('/contacts')
  }

  const formatCompany = async id => {
    const company = await companyRepository.getById(id)
    return company ? company.name : 'Unknown'
  }

  const formatContactOwner = async id => {
    const user = await userRepository.getById(id)
    return user ? user.fullName : 'Unknown'
  }

  useVoiceAgent({
    description: `Contact details view`,
    tools: {
      goBack,
      goEdit: editContact,
      showCompany: navigateToCompany,
    },
  })

  onMounted(() => {
    loadContact()
  })
</script>

<style scoped>
  .cursor-pointer {
    cursor: pointer;
  }

  .cursor-pointer:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
</style>
