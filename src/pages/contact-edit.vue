<template>
  <v-container>
    <v-row>
      <v-col>
        <v-btn
          class="mb-4"
          prepend-icon="mdi-arrow-left"
          variant="text"
          @click="goBack"
        >
          Back to Details
        </v-btn>
      </v-col>
    </v-row>

    <v-row v-if="loading">
      <v-col>
        <v-progress-circular indeterminate />
      </v-col>
    </v-row>

    <v-row v-else-if="contact">
      <v-col>
        <v-card>
          <v-card-title class="d-flex align-center">
            <span class="text-h4">Update Contact</span>
          </v-card-title>

          <v-card-text>
            <v-form ref="form" v-model="valid">
              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="editedContact.firstName"
                    label="First Name"
                    required
                    :rules="[rules.required]"
                  />
                </v-col>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="editedContact.lastName"
                    label="Last Name"
                    required
                    :rules="[rules.required]"
                  />
                </v-col>
              </v-row>

              <v-row>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="editedContact.email"
                    label="Email"
                    required
                    :rules="[rules.required, rules.email]"
                    type="email"
                  />
                </v-col>
                <v-col cols="12" md="6">
                  <v-text-field
                    v-model="editedContact.phoneNumber"
                    label="Phone Number"
                    required
                    :rules="[rules.required]"
                  />
                </v-col>
              </v-row>

              <v-row>
                <v-col cols="12" md="6">
                  <v-select
                    v-model="editedContact.company"
                    item-title="name"
                    item-value="id"
                    :items="companyOptions"
                    label="Company"
                    required
                    :rules="[rules.required]"
                  />
                </v-col>
                <v-col cols="12" md="6">
                  <v-select
                    v-model="editedContact.contactOwner"
                    item-title="title"
                    item-value="value"
                    :items="userOptions"
                    label="Contact Owner"
                    required
                    :rules="[rules.required]"
                  >
                    <template #selection="{ item }">
                      <div class="d-flex align-center">
                        <v-avatar
                          color="secondary"
                          size="24"
                        >
                          <span class="text-white text-caption font-weight-bold">
                            {{ getInitials(item.title) }}
                          </span>
                        </v-avatar>
                        <span class="ml-2">{{ item.title }}</span>
                      </div>
                    </template>
                    <template #item="{ item, props }">
                      <v-list-item v-bind="props">
                        <template #prepend>
                          <v-avatar
                            color="secondary"
                            size="32"
                          >
                            <span class="text-white text-caption font-weight-bold">
                              {{ getInitials(item.title) }}
                            </span>
                          </v-avatar>
                        </template>
                      </v-list-item>
                    </template>
                  </v-select>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>

          <v-card-actions>
            <v-spacer />
            <v-btn
              text
              @click="goBack"
            >
              Cancel
            </v-btn>
            <v-btn
              color="primary"
              :disabled="!valid"
              @click="saveContact"
            >
              Save
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>

    <v-row v-else>
      <v-col>
        <v-alert type="error" variant="outlined">
          Contact not found
        </v-alert>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
  import { onMounted, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useVoiceAgent } from '@/composables/useVoiceAgent'
  import { CompanyRepository } from '@/repositories/CompanyRepository'
  import { ContactRepository } from '@/repositories/ContactRepository'
  import { UserRepository } from '@/repositories/UserRepository'
  import { EditHistoryService } from '@/services/EditHistoryService'

  const route = useRoute()
  const router = useRouter()
  const contact = ref(null)
  const companyOptions = ref([])
  const userOptions = ref([])
  const loading = ref(true)
  const valid = ref(false)
  const form = ref(null)
  const contactRepository = new ContactRepository()
  const companyRepository = new CompanyRepository()
  const userRepository = new UserRepository()
  const editHistory = new EditHistoryService()

  const editedContact = ref({
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    company: '',
    contactOwner: '',
  })

  const rules = {
    required: value => !!value || 'This field is required',
    email: value => {
      const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return pattern.test(value) || 'Invalid email address'
    },
  }

  const loadContact = async () => {
    try {
      loading.value = true
      await contactRepository.seedData()
      contact.value = await contactRepository.getById(route.query.id)

      if (contact.value) {
        editedContact.value = {
          firstName: contact.value.firstName,
          lastName: contact.value.lastName,
          email: contact.value.email,
          phoneNumber: contact.value.phoneNumber,
          company: contact.value.company,
          contactOwner: contact.value.contactOwner,
        }
        editHistory.setModel(contact.value)
      }
    } catch (error) {
      console.error('Error loading contact:', error)
    } finally {
      loading.value = false
    }
  }

  const loadCompanies = async () => {
    try {
      await companyRepository.seedData()
      const companies = await companyRepository.getAll()
      companyOptions.value = companies
    } catch (error) {
      console.error('Error loading companies:', error)
    }
  }

  const loadUsers = async () => {
    try {
      await userRepository.seedData()
      const users = await userRepository.getAll()
      userOptions.value = users.map(user => ({
        value: user.id,
        title: user.fullName,
      }))
    } catch (error) {
      console.error('Error loading users:', error)
    }
  }

  const saveContact = async () => {
    if (form.value) {
      const { valid: isValid } = await form.value.validate()
      if (isValid) {
        try {
          await contactRepository.update(route.query.id, editedContact.value)
          router.back()
        } catch (error) {
          console.error('Error updating contact:', error)
        }
      }
    }
  }

  const goBack = () => {
    router.back()
  }

  const changeField = async (field, value) => {
    console.log('contact edit changeField:', field, value)
    if (field in editedContact.value) {
      // Special handling for contactOwner and company fields
      if (field === 'contactOwner') {
        value = await searchContactOwner(value)
      } else if (field === 'company') {
        value = await searchCompany(value)
      }

      if (value !== null) {
        editHistory.remember(field, editedContact.value[field])
        editedContact.value[field] = value
      }
    }
  }

  const resetField = field => {
    if (field in editedContact.value) {
      editedContact.value[field] = editHistory.restore(field)
    }
  }

  const resetFrom = () => {
    for (const field in editedContact.value) {
      editedContact.value[field] = editHistory.restore(field)
    }
  }

  const resetFormTool = {
    name: 'resetFormTool',
    description: 'Reset, restore, revert, undo, cancel ALL changes made in form fields',
    params: [],
    tool: resetFrom,
  }

  const getInitials = name => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const searchContactOwner = async value => {
    if (!value || typeof value !== 'string') {
      console.warn('[contact-edit] Invalid contactOwner value provided:', value)
      return null
    }

    try {
      const searchResults = await userRepository.search(value.trim())

      if (searchResults.length === 0) {
        console.warn('[contact-edit] No users found matching:', value)
        return null
      }

      const selectedUser = searchResults[0]
      console.log(`[contact-edit] Resolved contactOwner "${value}" to user:`, selectedUser.fullName, selectedUser.id)

      return selectedUser.id
    } catch (error) {
      console.error('[contact-edit] Error searching for user:', error)
      return null
    }
  }

  const searchCompany = async value => {
    if (!value || typeof value !== 'string') {
      console.warn('[contact-edit] Invalid company value provided:', value)
      return null
    }

    try {
      const searchResults = await companyRepository.search(value.trim())
      console.log('contact edit searchCompany:', searchResults)

      if (searchResults.length === 0) {
        console.warn('[contact-edit] No companies found matching:', value)
        return null
      }

      const selectedCompany = searchResults[0]
      console.log(`[contact-edit] Resolved company "${value}" to:`, selectedCompany.name, selectedCompany.id)

      return selectedCompany.id
    } catch (error) {
      console.error('[contact-edit] Error searching for company:', error)
      return null
    }
  }

  useVoiceAgent({
    description: `Contact edit form:
Form fields: ${Object.keys(editedContact.value).join(',')};`,
    tools: {
      changeField,
      resetField,
      resetFormTool,
      cancel: goBack,
      save: saveContact,
    },
  })

  onMounted(async () => {
    await loadCompanies()
    await loadUsers()
    await loadContact()
  })
</script>
