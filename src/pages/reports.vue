<template>
  <v-container>
    <v-row align="center" justify="space-between">
      <v-col cols="auto">
        <h1 class="text-primary cursor-pointer" @click="refresh">Reports</h1>
      </v-col>
      <v-col cols="auto">
        <v-select
          v-model="selectedPeriod"
          density="compact"
          item-title="label"
          item-value="value"
          :items="periodOptions"
          style="max-width: 200px"
          variant="outlined"
        />
      </v-col>
    </v-row>

    <div v-if="loading" class="text-center pa-4">
      <v-progress-circular indeterminate />
    </div>

    <div v-else>
      <ReportsTotals :deals="filteredDeals" @select-deals="selectedDeals = $event" />

      <v-row class="mt-6">
        <v-col cols="12" md="6">
          <ReportsBudgetChart
            :deals="selectedDeals"
            :period="selectedPeriod"
            title="Budget"
          />
        </v-col>
        <v-col cols="12" md="6">
          <ReportsPriorityChart
            :deals="selectedDeals"
            title="Priority"
          />
        </v-col>
      </v-row>

      <v-row class="mt-6">
        <v-col cols="12" md="6">
          <ReportsCompanies :deals="selectedDeals" />
        </v-col>
        <v-col cols="12" md="6">
          <ReportsUsers :deals="selectedDeals" />
        </v-col>
      </v-row>
    </div>
  </v-container>
</template>

<script setup>
  import { computed, onMounted, ref, watch } from 'vue'

  import ReportsBudgetChart from '@/components/reports/ReportsBudgetChart'
  import ReportsCompanies from '@/components/reports/ReportsCompanies'
  import ReportsPriorityChart from '@/components/reports/ReportsPriorityChart'
  import ReportsTotals from '@/components/reports/ReportsTotals'
  import ReportsUsers from '@/components/reports/ReportsUsers'
  import { useVoiceAgent } from '@/composables/useVoiceAgent'
  import { DealRepository } from '@/repositories/DealRepository'

  const dealRepository = new DealRepository()

  const loading = ref(true)
  const selectedPeriod = ref(30)
  const deals = ref([])
  const selectedDeals = ref([])

  const periodOptions = [
    { value: 1, label: 'Today' },
    { value: 7, label: 'Last 7 Days' },
    { value: 30, label: 'Last month' },
    { value: 90, label: 'Last 3 months' },
    { value: 180, label: 'Last 6 months' },
    { value: -1, label: 'All time' },
  ]

  const filteredDeals = computed(() => {
    if (selectedPeriod.value === -1) {
      return deals.value
    }

    const now = Date.now()
    const periodMs = selectedPeriod.value * 24 * 60 * 60 * 1000
    const cutoffDate = now - periodMs

    return deals.value.filter(deal => {
      return deal?.createdAt && deal.createdAt > cutoffDate
    })
  })

  const loadDeals = async () => {
    try {
      loading.value = true
      await dealRepository.seedData()
      deals.value = await dealRepository.getAll()
    } catch (error) {
      console.error('Error loading deals:', error)
    } finally {
      loading.value = false
    }
  }

  const refresh = () => {
    window.location.reload(true)
  }

  const setPeriod = {
    name: 'setPeriod',
    description: 'Set reports period to show. -1 all time, 1 last day etc',
    params: [
      {
        type: 'number',
        enum: periodOptions.map(item => item.value),
        required: true,
      },
    ],
    tool: value => {
      selectedPeriod.value = Number(value)
    },
  }

  const voiceAgent = useVoiceAgent({
    description: 'Reports page',
    tools: {
      setPeriod,
    },
  })

  watch(() => selectedDeals.value, values => {
    voiceAgent.setDescription(`Reports page.
Deals list: ${JSON.stringify(values)}
`)
  })

  onMounted(() => {
    loadDeals()
  })
</script>
