<template>
  <v-container>
    <ContactsManager
      ref="contactsManager"
      @refresh="handleRefresh"
    />
  </v-container>
</template>

<script setup>
  import { onMounted, ref } from 'vue'
  import { useRouter } from 'vue-router'

  const router = useRouter()
  const contactsManager = ref(null)

  // Initialize the contacts manager component on mount
  onMounted(() => {
    if (contactsManager.value) {
      contactsManager.value.initializeFromUrl()
    }
  })

  // Handle refresh events from the contacts manager
  const handleRefresh = () => {
    // Force navigation to the same route to refresh state
    router.push('/contacts')
  }
</script>
