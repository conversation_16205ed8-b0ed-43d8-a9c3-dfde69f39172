<template>
  <v-container>
    <v-row>
      <v-col>
        <v-btn
          class="mb-4"
          prepend-icon="mdi-arrow-left"
          variant="text"
          @click="goBack"
        >
          Back
        </v-btn>
      </v-col>
    </v-row>

    <v-row v-if="loading">
      <v-col>
        <v-progress-circular indeterminate />
      </v-col>
    </v-row>

    <v-row v-else-if="company">
      <v-col>
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-avatar class="mr-4" size="48">
              <v-img
                :alt="company.name"
                :src="company.logo"
              >
                <template #error>
                  <v-icon class="mt-2">mdi-domain</v-icon>
                </template>
              </v-img>
            </v-avatar>
            <field-history field="name" :model="company">
              <span class="text-h4">{{ company.name }}</span>
            </field-history>
          </v-card-title>

          <v-card-text>
            <v-row>
              <v-col cols="12" md="6">
                <v-list density="compact">
                  <v-list-item>
                    <v-list-item-title class="text-subtitle-2">Phone</v-list-item-title>
                    <v-list-item-subtitle opacity="1">
                      <field-history field="phoneNumber" :model="company">
                        {{ company.formattedPhone }}
                      </field-history>
                    </v-list-item-subtitle>
                  </v-list-item>

                  <v-list-item>
                    <v-list-item-title class="text-subtitle-2">Email</v-list-item-title>
                    <v-list-item-subtitle opacity="1">
                      <field-history field="email" :model="company">
                        {{ company.email }}
                      </field-history>
                    </v-list-item-subtitle>
                  </v-list-item>

                  <v-list-item>
                    <v-list-item-title class="text-subtitle-2">Location</v-list-item-title>
                    <v-list-item-subtitle opacity="1">
                      <field-history field="location" :model="company">
                        {{ company.location }}
                      </field-history>
                    </v-list-item-subtitle>
                  </v-list-item>

                  <v-list-item>
                    <v-list-item-title class="text-subtitle-2">Industry</v-list-item-title>
                    <v-list-item-subtitle opacity="1">
                      <field-history field="industry" :model="company">
                        {{ company.industry }}
                      </field-history>
                    </v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </v-col>
            </v-row>
          </v-card-text>

          <v-card-actions>
            <v-spacer />
            <v-btn
              color="primary"
              @click="editCompany"
            >
              Modify
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>

    <v-row v-else>
      <v-col>
        <v-alert type="error" variant="outlined">
          Company not found
        </v-alert>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
  import { onMounted, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import FieldHistory from '@/components/common/FieldHistory'
  import { useVoiceAgent } from '@/composables/useVoiceAgent'
  import { CompanyRepository } from '@/repositories/CompanyRepository'

  const route = useRoute()
  const router = useRouter()
  const company = ref(null)
  const loading = ref(true)
  const companyRepository = new CompanyRepository()

  const loadCompany = async () => {
    try {
      console.log('Loading company with ID:', route.query.id)
      loading.value = true
      await companyRepository.seedData()
      console.log('Seed data completed')
      company.value = await companyRepository.getById(route.query.id)
      console.log('Company loaded:', company.value)
    } catch (error) {
      console.error('Error loading company:', error)
    } finally {
      loading.value = false
    }
  }

  const editCompany = () => {
    router.push(`/company-edit?id=${route.query.id}`)
  }

  const goBack = () => {
    router.go(-1)
  }

  useVoiceAgent({
    description: `Company details view`,
    tools: {
      goBack,
      goEdit: editCompany,
    },
  })

  onMounted(() => {
    console.log('Company details page mounted, route query:', route.query)
    loadCompany()
  })
</script>
