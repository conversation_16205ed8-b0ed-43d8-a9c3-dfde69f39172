<template>
  <v-container>
    <v-row>
      <v-col>
        <v-btn
          class="mb-4"
          prepend-icon="mdi-arrow-left"
          variant="text"
          @click="goBack"
        >
          Back
        </v-btn>
      </v-col>
    </v-row>

    <v-row v-if="loading">
      <v-col>
        <v-progress-circular indeterminate />
      </v-col>
    </v-row>

    <v-row v-else-if="deal">
      <v-col>
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-icon class="mr-4" color="primary" size="48">mdi-briefcase-account-outline</v-icon>
            <field-history field="title" :model="deal"><span class="text-h4">{{ deal.title }}</span></field-history>
          </v-card-title>

          <v-card-text>
            <v-row>
              <v-col cols="12" md="6">
                <v-list density="compact">
                  <v-list-item>
                    <v-list-item-title class="text-subtitle-2">Status</v-list-item-title>
                    <v-list-item-subtitle opacity="1">
                      <field-history field="status" :format="formatStatus" :model="deal">
                        <v-chip
                          class="text-capitalize"
                          :color="deal.statusColor"
                          size="small"
                          variant="flat"
                        >
                          {{ deal.status }}
                        </v-chip>
                      </field-history>
                    </v-list-item-subtitle>
                  </v-list-item>

                  <v-list-item>
                    <v-list-item-title class="text-subtitle-2">Priority</v-list-item-title>
                    <v-list-item-subtitle opacity="1">
                      <field-history field="priority" :format="formatStatus" :model="deal">
                        <div class="d-flex align-center">
                          <v-icon
                            :color="getPriorityIconColor(deal.priority)"
                            size="18"
                          >
                            {{ getPriorityIcon(deal.priority) }}
                          </v-icon>
                          <span class="ml-2 text-body-2" :class="getPriorityTextClass(deal.priority)">
                            {{ getPriorityLabel(deal.priority) }}
                          </span>
                        </div>
                      </field-history>
                    </v-list-item-subtitle>
                  </v-list-item>

                  <v-list-item>
                    <v-list-item-title class="text-subtitle-2">Budget</v-list-item-title>
                    <v-list-item-subtitle class="font-weight-medium" opacity="1">
                      <field-history field="budget" :format="formatBudget" :model="deal">
                        {{ formatBudget(deal.budget) }}
                      </field-history>
                    </v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </v-col>

              <v-col cols="12" md="6">
                <v-list density="compact">
                  <v-list-item>
                    <v-list-item-title class="text-subtitle-2">Company</v-list-item-title>
                    <v-list-item-subtitle opacity="1">
                      <div
                        v-if="company"
                        class="d-flex align-center cursor-pointer"
                        @click="navigateToCompany"
                      >
                        <v-avatar
                          class="mr-3"
                          size="32"
                        >
                          <v-img
                            :alt="company.name"
                            :src="company.logo"
                          >
                            <template #error>
                              <v-icon class="mt-1">mdi-domain</v-icon>
                            </template>
                          </v-img>
                        </v-avatar>
                        <div>
                          <div class="font-weight-medium">{{ company.name }}</div>
                          <div class="text-caption text-grey">{{ company.industry }}</div>
                        </div>
                      </div>
                      <span v-else class="text-grey">Company not found</span>
                    </v-list-item-subtitle>
                  </v-list-item>

                  <v-list-item>
                    <v-list-item-title class="text-subtitle-2">Deal Owner</v-list-item-title>
                    <v-list-item-subtitle opacity="1">
                      <field-history field="dealOwner" :format="formatOwner" :model="deal">
                        <div
                          v-if="dealOwner"
                          class="d-flex align-center"
                        >
                          <v-avatar class="mr-3" size="32">
                            <v-img
                              :alt="dealOwner.fullName"
                              :src="dealOwner.avatar"
                            >
                              <template #error>
                                <v-icon icon="mdi-account-circle" size="32" />
                              </template>
                            </v-img>
                          </v-avatar>
                          <div>
                            <div class="font-weight-medium">{{ dealOwner.fullName }}</div>
                            <div class="text-caption text-grey">{{ dealOwner.email }}</div>
                          </div>
                        </div>
                        <span v-else class="text-grey">User not found</span>
                      </field-history>
                    </v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </v-col>
            </v-row>
          </v-card-text>

          <v-card-actions>
            <v-spacer />
            <v-btn
              color="primary"
              @click="editDeal"
            >
              Modify
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-col>
    </v-row>

    <v-row v-else>
      <v-col>
        <v-alert type="error" variant="outlined">
          Deal not found
        </v-alert>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
  import { onMounted, ref } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import FieldHistory from '@/components/common/FieldHistory'
  import { useVoiceAgent } from '@/composables/useVoiceAgent'
  import { CompanyRepository } from '@/repositories/CompanyRepository'
  import { DealRepository } from '@/repositories/DealRepository'
  import { UserRepository } from '@/repositories/UserRepository'
  import { formatBudget } from '@/utils/format'

  const route = useRoute()
  const router = useRouter()
  const dealRepository = new DealRepository()
  const companyRepository = new CompanyRepository()
  const userRepository = new UserRepository()

  const deal = ref(null)
  const company = ref(null)
  const dealOwner = ref(null)
  const loading = ref(true)

  const loadDeal = async () => {
    try {
      const dealId = route.query.id
      if (!dealId) {
        return router.push('/deals')
      }

      await dealRepository.seedData()
      deal.value = await dealRepository.getById(dealId)

      if (!deal.value) {
        console.error('Deal not found')
        return
      }

      // Load related company
      if (deal.value.company) {
        await companyRepository.seedData()
        company.value = await companyRepository.getById(deal.value.company)
      }

      // Load deal owner
      if (deal.value.dealOwner) {
        await userRepository.seedData()
        dealOwner.value = await userRepository.getById(deal.value.dealOwner)
      }
    } catch (error) {
      console.error('Error loading deal:', error)
    } finally {
      loading.value = false
    }
  }

  const goBack = () => {
    router.back()
  }

  const editDeal = () => {
    router.push(`/deal-edit?id=${deal.value.id}`)
  }

  const navigateToCompany = () => {
    if (company.value) {
      router.push(`/company-details?id=${company.value.id}`)
    }
  }

  const getPriorityIcon = priority => {
    switch (priority) {
      case 'low': { return 'mdi-chevron-down'
      }
      case 'medium': { return 'mdi-drag-horizontal'
      }
      case 'high': { return 'mdi-chevron-up'
      }
      default: { return 'mdi-drag-horizontal'
      }
    }
  }

  const getPriorityIconColor = priority => {
    switch (priority) {
      case 'low': { return 'blue'
      }
      case 'medium': { return 'orange'
      }
      case 'high': { return 'red'
      }
      default: { return 'grey'
      }
    }
  }

  const getPriorityLabel = priority => {
    switch (priority) {
      case 'low': { return 'Low'
      }
      case 'medium': { return 'Medium'
      }
      case 'high': { return 'High'
      }
      default: { return priority
      }
    }
  }

  const getPriorityTextClass = priority => {
    switch (priority) {
      case 'low': { return 'text-blue'
      }
      case 'medium': { return 'text-orange'
      }
      case 'high': { return 'text-red'
      }
      default: { return 'text-grey'
      }
    }
  }

  const formatStatus = v => `<span class="text-capitalize">${v}</span>`

  const formatOwner = async id => {
    const owner = await userRepository.getById(id)
    return owner ? owner.fullName : 'Unknown'
  }

  useVoiceAgent({
    description: `Deal details view`,
    tools: {
      goBack,
      goEdit: editDeal,
      showCompany: navigateToCompany,
    },
  })

  onMounted(() => {
    loadDeal()
  })
</script>

<style scoped>
  .cursor-pointer {
    cursor: pointer;
  }

  .cursor-pointer:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
</style>
