// Dynamic date generation for activities - ensures even distribution across last 6 months
const generateActivityDates = () => {
  const now = new Date()
  const sixMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 5, 1) // Start of 6 months ago
  const totalDays = Math.floor((now - sixMonthsAgo) / (1000 * 60 * 60 * 24))

  // Generate 52 dates evenly distributed across the period
  const dates = []
  const activityCount = 52

  // Use a simple seeded random number generator for consistency
  let seed = 12_345
  const seededRandom = () => {
    seed = (seed * 9301 + 49_297) % 233_280
    return seed / 233_280
  }

  for (let i = 0; i < activityCount; i++) {
    // Distribute activities evenly across the time period
    const dayOffset = Math.floor((i / activityCount) * totalDays)
    const randomHourOffset = Math.floor(seededRandom() * 9) + 9 // 9 AM to 5 PM
    const randomMinuteOffset = Math.floor(seededRandom() * 60)

    const activityDate = new Date(sixMonthsAgo)
    activityDate.setDate(activityDate.getDate() + dayOffset)
    activityDate.setHours(randomHourOffset, randomMinuteOffset, 0, 0)

    // Add some randomness to prevent exact clustering
    const randomDayAdjustment = Math.floor(seededRandom() * 3) - 1 // -1, 0, or 1 day
    activityDate.setDate(activityDate.getDate() + randomDayAdjustment)

    // Ensure we don't go into the future
    if (activityDate > now) {
      activityDate.setTime(now.getTime() - Math.floor(seededRandom() * 7 * 24 * 60 * 60 * 1000)) // Random time in last week
    }

    dates.push(activityDate)
  }

  // Sort dates chronologically
  return dates.sort((a, b) => a - b)
}

const activityDates = generateActivityDates()

export const activitySeeds = [
  { id: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890', note: 'Initial contact made via LinkedIn. Very interested in our software solution.', creationDateTime: activityDates[0], contact: '550e8400-e29b-41d4-a716-446655440001', activityCreator: '0fbaa79f-dfed-48f2-955c-1f7d678e7d84' },
  { id: 'b2c3d4e5-f6g7-8901-bcde-f23456789012', note: 'Follow-up call scheduled for next week. Client wants to discuss pricing options.', creationDateTime: activityDates[1], contact: '550e8400-e29b-41d4-a716-446655440002', activityCreator: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: 'c3d4e5f6-g7h8-9012-cdef-345678901234', note: 'Demo completed successfully. Client impressed with the reporting features.', creationDateTime: activityDates[2], contact: '550e8400-e29b-41d4-a716-446655440003', activityCreator: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
  { id: 'd4e5f6g7-h8i9-0123-defa-456789012345', note: 'Contract negotiation in progress. Waiting for legal review.', creationDateTime: activityDates[3], contact: '550e8400-e29b-41d4-a716-446655440004', activityCreator: '933173da-ab16-4152-bbba-4ce5edc83e97' },
  { id: 'e5f6g7h8-i9j0-1234-efab-567890123456', note: 'Client requested additional security documentation. Sent compliance packet.', creationDateTime: activityDates[4], contact: '550e8400-e29b-41d4-a716-446655440005', activityCreator: '06c3f277-b839-4ff0-bf4c-3f751ac655cf' },
  { id: 'f6g7h8i9-j0k1-2345-fabc-678901234567', note: 'Meeting rescheduled due to client emergency. New date TBD.', creationDateTime: activityDates[5], contact: '550e8400-e29b-41d4-a716-446655440006', activityCreator: '0fbaa79f-dfed-48f2-955c-1f7d678e7d84' },
  { id: 'g7h8i9j0-k1l2-3456-abcd-789012345678', note: 'Proposal submitted with custom integrations. Awaiting response.', creationDateTime: activityDates[6], contact: '550e8400-e29b-41d4-a716-446655440007', activityCreator: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: 'h8i9j0k1-l2m3-4567-bcde-890123456789', note: 'Technical requirements discussion. Need to involve our dev team.', creationDateTime: activityDates[7], contact: '550e8400-e29b-41d4-a716-446655440008', activityCreator: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
  { id: 'i9j0k1l2-m3n4-5678-cdef-901234567890', note: 'Client approved initial proposal. Moving to implementation phase.', creationDateTime: activityDates[8], contact: '550e8400-e29b-41d4-a716-446655440009', activityCreator: '933173da-ab16-4152-bbba-4ce5edc83e97' },
  { id: 'j0k1l2m3-n4o5-6789-defa-012345678901', note: 'Budget concerns raised. Preparing revised proposal with phased approach.', creationDateTime: activityDates[9], contact: '550e8400-e29b-41d4-a716-446655440010', activityCreator: '06c3f277-b839-4ff0-bf4c-3f751ac655cf' },
  { id: 'k1l2m3n4-o5p6-7890-efab-123456789012', note: 'Excellent feedback from stakeholder meeting. Moving forward with pilot program.', creationDateTime: activityDates[10], contact: '550e8400-e29b-41d4-a716-446655440011', activityCreator: '0fbaa79f-dfed-48f2-955c-1f7d678e7d84' },
  { id: 'l2m3n4o5-p6q7-8901-fabc-234567890123', note: 'Integration testing completed successfully. Ready for production deployment.', creationDateTime: activityDates[11], contact: '550e8400-e29b-41d4-a716-446655440012', activityCreator: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: 'm3n4o5p6-q7r8-9012-abcd-345678901234', note: 'Training session scheduled for end users. Preparing materials.', creationDateTime: activityDates[12], contact: '550e8400-e29b-41d4-a716-446655440013', activityCreator: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
  { id: 'n4o5p6q7-r8s9-0123-bcde-456789012345', note: 'Post-implementation review meeting. Client very satisfied with results.', creationDateTime: activityDates[13], contact: '550e8400-e29b-41d4-a716-446655440014', activityCreator: '933173da-ab16-4152-bbba-4ce5edc83e97' },
  { id: 'o5p6q7r8-s9t0-1234-cdef-567890123456', note: 'Upsell opportunity identified. Client interested in advanced analytics module.', creationDateTime: activityDates[14], contact: '550e8400-e29b-41d4-a716-446655440015', activityCreator: '06c3f277-b839-4ff0-bf4c-3f751ac655cf' },
  { id: 'p6q7r8s9-t0u1-2345-defa-678901234567', note: 'Support ticket resolved. Client appreciates quick response time.', creationDateTime: activityDates[15], contact: '550e8400-e29b-41d4-a716-446655440016', activityCreator: '0fbaa79f-dfed-48f2-955c-1f7d678e7d84' },
  { id: 'q7r8s9t0-u1v2-3456-efab-789012345678', note: 'Renewal discussion initiated. Contract expires in 6 months.', creationDateTime: activityDates[16], contact: '550e8400-e29b-41d4-a716-446655440017', activityCreator: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: 'r8s9t0u1-v2w3-4567-fabc-890123456789', note: 'Feature request submitted to product team. Client wants mobile access.', creationDateTime: activityDates[17], contact: '550e8400-e29b-41d4-a716-446655440018', activityCreator: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
  { id: 's9t0u1v2-w3x4-5678-abcd-901234567890', note: 'Quarterly business review completed. Strong ROI demonstrated.', creationDateTime: activityDates[18], contact: '550e8400-e29b-41d4-a716-446655440019', activityCreator: '933173da-ab16-4152-bbba-4ce5edc83e97' },
  { id: 't0u1v2w3-x4y5-6789-bcde-012345678901', note: 'Referral provided by satisfied client. Warm introduction to new prospect.', creationDateTime: activityDates[19], contact: '550e8400-e29b-41d4-a716-446655440020', activityCreator: '06c3f277-b839-4ff0-bf4c-3f751ac655cf' },
  { id: 'u1v2w3x4-y5z6-7890-cdef-123456789012', note: 'Cold call converted to meeting. Prospect interested in automation features.', creationDateTime: activityDates[20], contact: '550e8400-e29b-41d4-a716-446655440021', activityCreator: '0fbaa79f-dfed-48f2-955c-1f7d678e7d84' },
  { id: 'v2w3x4y5-z6a7-8901-defa-234567890123', note: 'Competitive analysis provided. Our solution outperforms in key areas.', creationDateTime: activityDates[21], contact: '550e8400-e29b-41d4-a716-446655440022', activityCreator: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: 'w3x4y5z6-a7b8-9012-efab-345678901234', note: 'Implementation kickoff meeting scheduled. Project timeline established.', creationDateTime: activityDates[22], contact: '550e8400-e29b-41d4-a716-446655440023', activityCreator: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
  { id: 'x4y5z6a7-b8c9-0123-fabc-456789012345', note: 'Data migration planning session. Client has complex legacy system.', creationDateTime: activityDates[23], contact: '550e8400-e29b-41d4-a716-446655440024', activityCreator: '933173da-ab16-4152-bbba-4ce5edc83e97' },
  { id: 'y5z6a7b8-c9d0-1234-abcd-567890123456', note: 'User acceptance testing feedback received. Minor adjustments needed.', creationDateTime: activityDates[24], contact: '550e8400-e29b-41d4-a716-446655440025', activityCreator: '06c3f277-b839-4ff0-bf4c-3f751ac655cf' },
  { id: 'z6a7b8c9-d0e1-2345-bcde-678901234567', note: 'Executive sponsor meeting. Secured buy-in for expanded rollout.', creationDateTime: activityDates[25], contact: '550e8400-e29b-41d4-a716-446655440026', activityCreator: '0fbaa79f-dfed-48f2-955c-1f7d678e7d84' },
  { id: 'a7b8c9d0-e1f2-3456-cdef-789012345678', note: 'Performance optimization completed. System response time improved by 40%.', creationDateTime: activityDates[26], contact: '550e8400-e29b-41d4-a716-446655440027', activityCreator: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: 'b8c9d0e1-f2g3-4567-defa-890123456789', note: 'Security audit passed with flying colors. Client compliance team approved.', creationDateTime: activityDates[27], contact: '550e8400-e29b-41d4-a716-446655440028', activityCreator: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
  { id: 'c9d0e1f2-g3h4-5678-efab-901234567890', note: 'Change request approved. Additional workflow customization in progress.', creationDateTime: activityDates[28], contact: '550e8400-e29b-41d4-a716-446655440029', activityCreator: '933173da-ab16-4152-bbba-4ce5edc83e97' },
  { id: 'd0e1f2g3-h4i5-6789-fabc-012345678901', note: 'Go-live successful! All systems operational. Client team celebrating.', creationDateTime: activityDates[29], contact: '550e8400-e29b-41d4-a716-446655440030', activityCreator: '06c3f277-b839-4ff0-bf4c-3f751ac655cf' },
  { id: 'e1f2g3h4-i5j6-7890-abcd-123456789012', note: 'Post-go-live support call. No major issues reported. System stable.', creationDateTime: activityDates[30], contact: '550e8400-e29b-41d4-a716-446655440031', activityCreator: '0fbaa79f-dfed-48f2-955c-1f7d678e7d84' },
  { id: 'f2g3h4i5-j6k7-8901-bcde-234567890123', note: 'Customer success check-in. Measuring adoption rates and user satisfaction.', creationDateTime: activityDates[31], contact: '550e8400-e29b-41d4-a716-446655440032', activityCreator: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: 'g3h4i5j6-k7l8-9012-cdef-345678901234', note: 'Feature enhancement deployed. Client requested dashboard improvements.', creationDateTime: activityDates[32], contact: '550e8400-e29b-41d4-a716-446655440033', activityCreator: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
  { id: 'h4i5j6k7-l8m9-0123-defa-456789012345', note: 'Annual contract renewal signed. Increased license count by 25%.', creationDateTime: activityDates[33], contact: '550e8400-e29b-41d4-a716-446655440034', activityCreator: '933173da-ab16-4152-bbba-4ce5edc83e97' },
  { id: 'i5j6k7l8-m9n0-1234-efab-567890123456', note: 'Integration with third-party CRM completed. Data sync working perfectly.', creationDateTime: activityDates[34], contact: '550e8400-e29b-41d4-a716-446655440035', activityCreator: '06c3f277-b839-4ff0-bf4c-3f751ac655cf' },
  { id: 'j6k7l8m9-n0o1-2345-fabc-678901234567', note: 'Client case study approved for marketing use. Great success story.', creationDateTime: activityDates[35], contact: '550e8400-e29b-41d4-a716-446655440036', activityCreator: '0fbaa79f-dfed-48f2-955c-1f7d678e7d84' },
  { id: 'k7l8m9n0-o1p2-3456-abcd-789012345678', note: 'Escalation resolved quickly. Client appreciates proactive communication.', creationDateTime: activityDates[36], contact: '550e8400-e29b-41d4-a716-446655440037', activityCreator: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: 'l8m9n0o1-p2q3-4567-bcde-890123456789', note: 'Advanced training session delivered. Power users now fully certified.', creationDateTime: activityDates[37], contact: '550e8400-e29b-41d4-a716-446655440038', activityCreator: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
  { id: 'm9n0o1p2-q3r4-5678-cdef-901234567890', note: 'ROI analysis completed. Client seeing 300% return on investment.', creationDateTime: activityDates[38], contact: '550e8400-e29b-41d4-a716-446655440039', activityCreator: '933173da-ab16-4152-bbba-4ce5edc83e97' },
  { id: 'n0o1p2q3-r4s5-6789-defa-012345678901', note: 'Expansion opportunity discussed. Client wants to add two more departments.', creationDateTime: activityDates[39], contact: '550e8400-e29b-41d4-a716-446655440040', activityCreator: '06c3f277-b839-4ff0-bf4c-3f751ac655cf' },
  { id: 'o1p2q3r4-s5t6-7890-efab-123456789012', note: 'Second follow-up call with Ava. Discussed implementation timeline and next steps.', creationDateTime: activityDates[40], contact: '550e8400-e29b-41d4-a716-446655440001', activityCreator: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: 'p2q3r4s5-t6u7-8901-fabc-234567890123', note: 'Sent technical documentation as requested. Waiting for review by IT team.', creationDateTime: activityDates[41], contact: '550e8400-e29b-41d4-a716-446655440002', activityCreator: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
  { id: 'q3r4s5t6-u7v8-9012-abcd-345678901234', note: 'Client requested custom reporting module. Scoping requirements with dev team.', creationDateTime: activityDates[42], contact: '550e8400-e29b-41d4-a716-446655440003', activityCreator: '933173da-ab16-4152-bbba-4ce5edc83e97' },
  { id: 'r4s5t6u7-v8w9-0123-bcde-456789012345', note: 'Contract signed! Implementation to begin next month. Team very excited.', creationDateTime: activityDates[43], contact: '550e8400-e29b-41d4-a716-446655440004', activityCreator: '06c3f277-b839-4ff0-bf4c-3f751ac655cf' },
  { id: 's5t6u7v8-w9x0-1234-cdef-567890123456', note: 'Security compliance review completed successfully. All requirements met.', creationDateTime: activityDates[44], contact: '550e8400-e29b-41d4-a716-446655440005', activityCreator: '0fbaa79f-dfed-48f2-955c-1f7d678e7d84' },
  { id: 't6u7v8w9-x0y1-2345-defa-678901234567', note: 'Rescheduled meeting completed. Client ready to move forward with pilot.', creationDateTime: activityDates[45], contact: '550e8400-e29b-41d4-a716-446655440006', activityCreator: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: 'u7v8w9x0-y1z2-3456-efab-789012345678', note: 'Custom integration proposal approved. Development work to start immediately.', creationDateTime: activityDates[46], contact: '550e8400-e29b-41d4-a716-446655440007', activityCreator: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
  { id: 'v8w9x0y1-z2a3-4567-fabc-890123456789', note: 'Dev team meeting scheduled. Technical architecture review next week.', creationDateTime: activityDates[47], contact: '550e8400-e29b-41d4-a716-446655440008', activityCreator: '933173da-ab16-4152-bbba-4ce5edc83e97' },
  { id: 'w9x0y1z2-a3b4-5678-abcd-901234567890', note: 'Implementation milestone reached. System integration 75% complete.', creationDateTime: activityDates[48], contact: '550e8400-e29b-41d4-a716-446655440009', activityCreator: '06c3f277-b839-4ff0-bf4c-3f751ac655cf' },
  { id: 'x0y1z2a3-b4c5-6789-bcde-012345678901', note: 'Revised proposal accepted! Phased approach approved by client management.', creationDateTime: activityDates[49], contact: '550e8400-e29b-41d4-a716-446655440010', activityCreator: '0fbaa79f-dfed-48f2-955c-1f7d678e7d84' },
  { id: 'y1z2a3b4-c5d6-7890-cdef-123456789012', note: 'Final user training session completed. All teams ready for full deployment.', creationDateTime: activityDates[50], contact: '550e8400-e29b-41d4-a716-446655440011', activityCreator: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: 'z2a3b4c5-d6e7-8901-defa-234567890123', note: 'Monthly check-in call. Client reporting excellent system performance and ROI.', creationDateTime: activityDates[51], contact: '550e8400-e29b-41d4-a716-446655440012', activityCreator: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
]
