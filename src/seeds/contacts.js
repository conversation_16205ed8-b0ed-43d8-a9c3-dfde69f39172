export const contactSeeds = [
  { id: '550e8400-e29b-41d4-a716-446655440001', firstName: 'Ava', lastName: '<PERSON>', email: '<EMAIL>', phoneNumber: '******-311-5100', company: '97741b4b-9313-4524-a134-2afb29d0ae1f', contactOwner: '0fbaa79f-dfed-48f2-955c-1f7d678e7d84' },
  { id: '550e8400-e29b-41d4-a716-446655440002', firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', phoneNumber: '******-311-5101', company: 'fa294837-b9e1-4095-bc56-536f1149dd2b', contactOwner: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: '550e8400-e29b-41d4-a716-446655440003', firstName: '<PERSON>', lastName: 'Nguyen', email: '<EMAIL>', phoneNumber: '******-311-5102', company: 'c4da9658-2900-469a-93ee-415143ce67bb', contactOwner: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
  { id: '550e8400-e29b-41d4-a716-446655440004', firstName: 'Noah', lastName: 'Kim', email: '<EMAIL>', phoneNumber: '******-311-5103', company: '77b0921d-0d91-430b-915f-a5ba1ef9df8b', contactOwner: '933173da-ab16-4152-bbba-4ce5edc83e97' },
  { id: '550e8400-e29b-41d4-a716-446655440005', firstName: 'Isabella', lastName: 'Jones', email: '<EMAIL>', phoneNumber: '******-311-5104', company: '18cdd706-0997-4a04-9f28-5dfbc7818531', contactOwner: '06c3f277-b839-4ff0-bf4c-3f751ac655cf' },
  { id: '550e8400-e29b-41d4-a716-446655440006', firstName: 'Jackson', lastName: 'Garcia', email: '<EMAIL>', phoneNumber: '******-311-5105', company: '2eeb807f-c284-4c18-b4eb-a1587e3baaaf', contactOwner: 'c747f84e-4da8-45f0-86d7-354c2bfd37ee' },
  { id: '550e8400-e29b-41d4-a716-446655440007', firstName: 'Mia', lastName: 'Rodriguez', email: '<EMAIL>', phoneNumber: '******-311-5106', company: 'a298413b-7f28-4555-a92f-fd566359eb32', contactOwner: '0fbaa79f-dfed-48f2-955c-1f7d678e7d84' },
  { id: '550e8400-e29b-41d4-a716-446655440008', firstName: 'Oliver', lastName: 'Davis', email: '<EMAIL>', phoneNumber: '******-311-5107', company: '19574f04-df3c-4be2-bb33-559ca7e5be53', contactOwner: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: '550e8400-e29b-41d4-a716-446655440009', firstName: 'Charlotte', lastName: 'Martinez', email: '<EMAIL>', phoneNumber: '******-311-5108', company: '9d81d979-59d0-474c-9cff-76f8161b696e', contactOwner: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
  { id: '550e8400-e29b-41d4-a716-446655440010', firstName: 'Lucas', lastName: 'Hernandez', email: '<EMAIL>', phoneNumber: '******-311-5109', company: 'f955602f-dca8-4e15-b0f7-48122c462650', contactOwner: '933173da-ab16-4152-bbba-4ce5edc83e97' },
  { id: '550e8400-e29b-41d4-a716-446655440011', firstName: 'Amelia', lastName: 'Lopez', email: '<EMAIL>', phoneNumber: '******-311-5110', company: '328f22c6-3b41-4a05-9401-90672145c08f', contactOwner: '06c3f277-b839-4ff0-bf4c-3f751ac655cf' },
  { id: '550e8400-e29b-41d4-a716-446655440012', firstName: 'Elijah', lastName: 'Gonzalez', email: '<EMAIL>', phoneNumber: '******-311-5111', company: 'f28e6c5d-80a8-4129-806a-0c9780e8a76e', contactOwner: 'c747f84e-4da8-45f0-86d7-354c2bfd37ee' },
  { id: '550e8400-e29b-41d4-a716-446655440013', firstName: 'Harper', lastName: 'Wilson', email: '<EMAIL>', phoneNumber: '******-311-5112', company: '238ef80d-9aca-4cd1-9fe6-fa0efbc9d86d', contactOwner: '0fbaa79f-dfed-48f2-955c-1f7d678e7d84' },
  { id: '550e8400-e29b-41d4-a716-446655440014', firstName: 'Ethan', lastName: 'Anderson', email: '<EMAIL>', phoneNumber: '******-311-5113', company: 'de9047a4-0049-4d7d-91fe-e7d8fc465de2', contactOwner: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: '550e8400-e29b-41d4-a716-446655440015', firstName: 'Abigail', lastName: 'Thomas', email: '<EMAIL>', phoneNumber: '******-311-5114', company: 'c6cf5125-2a7b-477c-81a4-5576c8d5662d', contactOwner: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
  { id: '550e8400-e29b-41d4-a716-446655440016', firstName: 'Daniel', lastName: 'Taylor', email: '<EMAIL>', phoneNumber: '******-311-5115', company: 'a928bc07-0043-4929-b9d6-c9e5627e6181', contactOwner: '933173da-ab16-4152-bbba-4ce5edc83e97' },
  { id: '550e8400-e29b-41d4-a716-446655440017', firstName: 'Emily', lastName: 'Moore', email: '<EMAIL>', phoneNumber: '******-311-5116', company: '169d19db-0378-4b9b-85b4-a4b101f80254', contactOwner: '06c3f277-b839-4ff0-bf4c-3f751ac655cf' },
  { id: '550e8400-e29b-41d4-a716-446655440018', firstName: 'Matthew', lastName: 'Jackson', email: '<EMAIL>', phoneNumber: '******-311-5117', company: '3e8d8323-27df-44db-a090-938cb8f83686', contactOwner: 'c747f84e-4da8-45f0-86d7-354c2bfd37ee' },
  { id: '550e8400-e29b-41d4-a716-446655440019', firstName: 'Elizabeth', lastName: 'White', email: '<EMAIL>', phoneNumber: '******-311-5118', company: '42d5ba1d-97c2-4896-bcf0-833f1d6e287d', contactOwner: '0fbaa79f-dfed-48f2-955c-1f7d678e7d84' },
  { id: '550e8400-e29b-41d4-a716-446655440020', firstName: 'William', lastName: 'Harris', email: '<EMAIL>', phoneNumber: '******-311-5119', company: '653b7c5c-3c4c-40a0-a386-9f909bf5adf5', contactOwner: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: '550e8400-e29b-41d4-a716-446655440021', firstName: 'Sofia', lastName: 'Clark', email: '<EMAIL>', phoneNumber: '******-311-5120', company: '97741b4b-9313-4524-a134-2afb29d0ae1f', contactOwner: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
  { id: '550e8400-e29b-41d4-a716-446655440022', firstName: 'James', lastName: 'Lewis', email: '<EMAIL>', phoneNumber: '******-311-5121', company: 'fa294837-b9e1-4095-bc56-536f1149dd2b', contactOwner: '933173da-ab16-4152-bbba-4ce5edc83e97' },
  { id: '550e8400-e29b-41d4-a716-446655440023', firstName: 'Ella', lastName: 'Young', email: '<EMAIL>', phoneNumber: '******-311-5122', company: 'c4da9658-2900-469a-93ee-415143ce67bb', contactOwner: '06c3f277-b839-4ff0-bf4c-3f751ac655cf' },
  { id: '550e8400-e29b-41d4-a716-446655440024', firstName: 'Benjamin', lastName: 'Hall', email: '<EMAIL>', phoneNumber: '******-311-5123', company: '77b0921d-0d91-430b-915f-a5ba1ef9df8b', contactOwner: 'c747f84e-4da8-45f0-86d7-354c2bfd37ee' },
  { id: '550e8400-e29b-41d4-a716-446655440025', firstName: 'Grace', lastName: 'King', email: '<EMAIL>', phoneNumber: '******-311-5124', company: '18cdd706-0997-4a04-9f28-5dfbc7818531', contactOwner: '0fbaa79f-dfed-48f2-955c-1f7d678e7d84' },
  { id: '550e8400-e29b-41d4-a716-446655440026', firstName: 'Alexander', lastName: 'Wright', email: '<EMAIL>', phoneNumber: '******-311-5125', company: '2eeb807f-c284-4c18-b4eb-a1587e3baaaf', contactOwner: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: '550e8400-e29b-41d4-a716-446655440027', firstName: 'Chloe', lastName: 'Scott', email: '<EMAIL>', phoneNumber: '******-311-5126', company: 'a298413b-7f28-4555-a92f-fd566359eb32', contactOwner: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
  { id: '550e8400-e29b-41d4-a716-446655440028', firstName: 'Michael', lastName: 'Green', email: '<EMAIL>', phoneNumber: '******-311-5127', company: '19574f04-df3c-4be2-bb33-559ca7e5be53', contactOwner: '933173da-ab16-4152-bbba-4ce5edc83e97' },
  { id: '550e8400-e29b-41d4-a716-446655440029', firstName: 'Victoria', lastName: 'Baker', email: '<EMAIL>', phoneNumber: '******-311-5128', company: '9d81d979-59d0-474c-9cff-76f8161b696e', contactOwner: '06c3f277-b839-4ff0-bf4c-3f751ac655cf' },
  { id: '550e8400-e29b-41d4-a716-446655440030', firstName: 'Avery', lastName: 'Adams', email: '<EMAIL>', phoneNumber: '******-311-5129', company: 'f955602f-dca8-4e15-b0f7-48122c462650', contactOwner: 'c747f84e-4da8-45f0-86d7-354c2bfd37ee' },
  { id: '550e8400-e29b-41d4-a716-446655440031', firstName: 'Scarlett', lastName: 'Nelson', email: '<EMAIL>', phoneNumber: '******-311-5130', company: '328f22c6-3b41-4a05-9401-90672145c08f', contactOwner: '0fbaa79f-dfed-48f2-955c-1f7d678e7d84' },
  { id: '550e8400-e29b-41d4-a716-446655440032', firstName: 'David', lastName: 'Carter', email: '<EMAIL>', phoneNumber: '******-311-5131', company: 'f28e6c5d-80a8-4129-806a-0c9780e8a76e', contactOwner: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: '550e8400-e29b-41d4-a716-446655440033', firstName: 'Madison', lastName: 'Mitchell', email: '<EMAIL>', phoneNumber: '******-311-5132', company: '238ef80d-9aca-4cd1-9fe6-fa0efbc9d86d', contactOwner: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
  { id: '550e8400-e29b-41d4-a716-446655440034', firstName: 'Joseph', lastName: 'Perez', email: '<EMAIL>', phoneNumber: '******-311-5133', company: 'de9047a4-0049-4d7d-91fe-e7d8fc465de2', contactOwner: '933173da-ab16-4152-bbba-4ce5edc83e97' },
  { id: '550e8400-e29b-41d4-a716-446655440035', firstName: 'Luna', lastName: 'Roberts', email: '<EMAIL>', phoneNumber: '******-311-5134', company: 'c6cf5125-2a7b-477c-81a4-5576c8d5662d', contactOwner: '06c3f277-b839-4ff0-bf4c-3f751ac655cf' },
  { id: '550e8400-e29b-41d4-a716-446655440036', firstName: 'Gabriel', lastName: 'Turner', email: '<EMAIL>', phoneNumber: '******-311-5135', company: 'a928bc07-0043-4929-b9d6-c9e5627e6181', contactOwner: 'c747f84e-4da8-45f0-86d7-354c2bfd37ee' },
  { id: '550e8400-e29b-41d4-a716-446655440037', firstName: 'Penelope', lastName: 'Phillips', email: '<EMAIL>', phoneNumber: '******-311-5136', company: '169d19db-0378-4b9b-85b4-a4b101f80254', contactOwner: '0fbaa79f-dfed-48f2-955c-1f7d678e7d84' },
  { id: '550e8400-e29b-41d4-a716-446655440038', firstName: 'Samuel', lastName: 'Campbell', email: '<EMAIL>', phoneNumber: '******-311-5137', company: '3e8d8323-27df-44db-a090-938cb8f83686', contactOwner: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: '550e8400-e29b-41d4-a716-446655440039', firstName: 'Layla', lastName: 'Parker', email: '<EMAIL>', phoneNumber: '******-311-5138', company: '42d5ba1d-97c2-4896-bcf0-833f1d6e287d', contactOwner: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
  { id: '550e8400-e29b-41d4-a716-446655440040', firstName: 'Anthony', lastName: 'Evans', email: '<EMAIL>', phoneNumber: '******-311-5139', company: '653b7c5c-3c4c-40a0-a386-9f909bf5adf5', contactOwner: '933173da-ab16-4152-bbba-4ce5edc83e97' },
  { id: '550e8400-e29b-41d4-a716-446655440041', firstName: 'Stella', lastName: 'Edwards', email: '<EMAIL>', phoneNumber: '******-311-5140', company: '97741b4b-9313-4524-a134-2afb29d0ae1f', contactOwner: '06c3f277-b839-4ff0-bf4c-3f751ac655cf' },
  { id: '550e8400-e29b-41d4-a716-446655440042', firstName: 'John', lastName: 'Collins', email: '<EMAIL>', phoneNumber: '******-311-5141', company: 'fa294837-b9e1-4095-bc56-536f1149dd2b', contactOwner: 'c747f84e-4da8-45f0-86d7-354c2bfd37ee' },
  { id: '550e8400-e29b-41d4-a716-446655440043', firstName: 'Zoe', lastName: 'Stewart', email: '<EMAIL>', phoneNumber: '******-311-5142', company: 'c4da9658-2900-469a-93ee-415143ce67bb', contactOwner: '0fbaa79f-dfed-48f2-955c-1f7d678e7d84' },
  { id: '550e8400-e29b-41d4-a716-446655440044', firstName: 'Christopher', lastName: 'Flores', email: '<EMAIL>', phoneNumber: '******-311-5143', company: '77b0921d-0d91-430b-915f-a5ba1ef9df8b', contactOwner: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: '550e8400-e29b-41d4-a716-446655440045', firstName: 'Hannah', lastName: 'Ramirez', email: '<EMAIL>', phoneNumber: '******-311-5144', company: '18cdd706-0997-4a04-9f28-5dfbc7818531', contactOwner: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
  { id: '550e8400-e29b-41d4-a716-446655440046', firstName: 'Andrew', lastName: 'Cox', email: '<EMAIL>', phoneNumber: '******-311-5145', company: '2eeb807f-c284-4c18-b4eb-a1587e3baaaf', contactOwner: '933173da-ab16-4152-bbba-4ce5edc83e97' },
  { id: '550e8400-e29b-41d4-a716-446655440047', firstName: 'Nora', lastName: 'Rivera', email: '<EMAIL>', phoneNumber: '******-311-5146', company: 'a298413b-7f28-4555-a92f-fd566359eb32', contactOwner: '06c3f277-b839-4ff0-bf4c-3f751ac655cf' },
  { id: '550e8400-e29b-41d4-a716-446655440048', firstName: 'Ryan', lastName: 'Reed', email: '<EMAIL>', phoneNumber: '******-311-5147', company: '19574f04-df3c-4be2-bb33-559ca7e5be53', contactOwner: 'c747f84e-4da8-45f0-86d7-354c2bfd37ee' },
  { id: '550e8400-e29b-41d4-a716-446655440049', firstName: 'Audrey', lastName: 'Cook', email: '<EMAIL>', phoneNumber: '******-311-5148', company: '9d81d979-59d0-474c-9cff-76f8161b696e', contactOwner: '0fbaa79f-dfed-48f2-955c-1f7d678e7d84' },
  { id: '550e8400-e29b-41d4-a716-446655440050', firstName: 'Leo', lastName: 'Morgan', email: '<EMAIL>', phoneNumber: '******-311-5149', company: 'f955602f-dca8-4e15-b0f7-48122c462650', contactOwner: 'cdda282a-fbea-41a1-a13d-9ba4c00d22c4' },
  { id: '550e8400-e29b-41d4-a716-446655440051', firstName: 'Kinsley', lastName: 'Bell', email: '<EMAIL>', phoneNumber: '******-311-5150', company: '328f22c6-3b41-4a05-9401-90672145c08f', contactOwner: '71f4bf30-b2cf-4fbf-85f8-ac83abbd2753' },
]
