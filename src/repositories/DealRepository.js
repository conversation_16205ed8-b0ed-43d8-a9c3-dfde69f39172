import { Deal } from '@/models/Deal'
import { BaseRepository } from '@/repositories/BaseRepository'
import { LocalStorageService } from '@/services/StorageService'

export class DealRepository extends BaseRepository {
  constructor (storageService = new LocalStorageService()) {
    super(storageService, 'deals', Deal)
    this.companies = new Map()
    this.users = new Map()
  }

  setCompanies (value) {
    this.companies = value
  }

  setUsers (value) {
    this.users = value
  }

  async getAll () {
    const data = await super.getAll()
    return data.map(dealData => Deal.fromJSON(dealData))
  }

  async getById (id) {
    const dealData = await super.getById(id)
    return dealData ? Deal.fromJSON(dealData) : null
  }

  async create (dealData) {
    const deal = new Deal(dealData)
    const result = await super.create(deal.toJSON())
    return Deal.fromJSON(result)
  }

  async findBy (filterOptions = {}) {
    let filteredDeals = await this.getAll()

    // Filter by status
    if (filterOptions.status) {
      filteredDeals = filteredDeals.filter(deal =>
        deal.status === filterOptions.status,
      )
    }

    // Filter by priority
    if (filterOptions.priority) {
      filteredDeals = filteredDeals.filter(deal =>
        deal.priority === filterOptions.priority,
      )
    }

    // Filter by company
    if (filterOptions.company) {
      filteredDeals = filteredDeals.filter(deal =>
        deal.company === filterOptions.company,
      )
    }

    // Filter by deal owner
    if (filterOptions.dealOwner) {
      filteredDeals = filteredDeals.filter(deal =>
        deal.dealOwner === filterOptions.dealOwner,
      )
    }

    // Filter by budget range
    if (filterOptions.budgetFrom !== undefined && filterOptions.budgetFrom !== null) {
      const budgetFrom = Number.parseFloat(filterOptions.budgetFrom)
      if (!Number.isNaN(budgetFrom)) {
        filteredDeals = filteredDeals.filter(deal => {
          const dealBudget = Number.parseFloat(deal.budget) || 0
          return dealBudget >= budgetFrom
        })
      }
    }

    if (filterOptions.budgetTo !== undefined && filterOptions.budgetTo !== null) {
      const budgetTo = Number.parseFloat(filterOptions.budgetTo)
      if (!Number.isNaN(budgetTo)) {
        filteredDeals = filteredDeals.filter(deal => {
          const dealBudget = Number.parseFloat(deal.budget) || 0
          return dealBudget <= budgetTo
        })
      }
    }

    // Filter by title
    if (filterOptions.title) {
      filteredDeals = filteredDeals.filter(deal =>
        deal.title.toLowerCase().includes(filterOptions.title.toLowerCase()),
      )
    }

    return filteredDeals
  }

  async search (query, filters = {}) {
    let deals = await this.getAll()

    // Apply filters first
    if (Object.keys(filters).length > 0) {
      deals = await this.findBy(filters)
    }

    // Then apply search query
    if (!query || query.trim().length < 2) {
      return deals
    }

    const searchTerm = query.toLowerCase().trim()

    return deals.filter(deal => {
      const company = this.companies.get(deal.company)
      const user = this.users.get(deal.dealOwner)
      // Search across all relevant fields
      const searchableFields = [
        deal.title,
        deal.status,
        deal.priority,
        deal.formattedBudget,
        company?.name || '',
        user?.fullName || '',
      ]

      return searchableFields.some(field =>
        field && field.toLowerCase().includes(searchTerm),
      )
    })
  }

  /**
   * Override base paginate to support filters
   * @param {number} page
   * @param {number} perPage
   * @param {string} sortBy
   * @param {string} sortOrder
   * @param {string} searchQuery
   * @param {object} filters
   * @returns {Promise<{total, page, pages: number, perPage, data: object[]}>}
   */
  async paginate (page, perPage, sortBy, sortOrder, searchQuery = '', filters = {}) {
    const offset = (page - 1) * perPage
    const items = await this.search(searchQuery, filters)

    if (sortBy) {
      items.sort((a, b) => {
        // Special handling for priority field to use logical order instead of alphabetical
        if (sortBy === 'priority') {
          const priorityOrder = { low: 1, medium: 2, high: 3 }
          const aValue = priorityOrder[a[sortBy]] || 0
          const bValue = priorityOrder[b[sortBy]] || 0
          if (sortOrder === 'desc') {
            return bValue - aValue // high → medium → low
          } else {
            return aValue - bValue // low → medium → high
          }
        } else {
          // Regular string/numeric sorting for other fields
          if (sortOrder === 'desc') {
            if (b[sortBy] > a[sortBy]) {
              return 1
            }
            if (b[sortBy] < a[sortBy]) {
              return -1
            }
            return 0
          } else {
            if (a[sortBy] > b[sortBy]) {
              return 1
            }
            if (a[sortBy] < b[sortBy]) {
              return -1
            }
            return 0
          }
        }
      })
    }

    const pages = Math.ceil(items.length / perPage)
    const data = (perPage === -1 ? items : items.slice(offset, offset + perPage))
    return {
      total: items.length,
      page,
      pages,
      perPage,
      data,
    }
  }

  async seedData () {
    const existingDeals = await this.getAll()
    if (existingDeals.length > 0) {
      return
    } // Already seeded

    const { dealSeeds } = await import('@/seeds/deals')

    for (const dealData of dealSeeds) {
      await this.create(dealData)
    }
  }
}
