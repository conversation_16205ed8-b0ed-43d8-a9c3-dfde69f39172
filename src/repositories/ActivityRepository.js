import { Activity } from '@/models/Activity'
import { BaseRepository } from '@/repositories/BaseRepository'
import { LocalStorageService } from '@/services/StorageService'

export class ActivityRepository extends BaseRepository {
  constructor (storageService = new LocalStorageService()) {
    super(storageService, 'activities')
  }

  async getAll () {
    const data = await super.getAll()
    return data.map(activityData => Activity.fromJSON(activityData))
  }

  async getById (id) {
    const activityData = await super.getById(id)
    return activityData ? Activity.fromJSON(activityData) : null
  }

  async create (activityData) {
    const activity = new Activity(activityData)
    const result = await super.create(activity.toJSON())
    return Activity.fromJSON(result)
  }

  async update (id, updates) {
    const result = await super.update(id, updates)
    return result ? Activity.fromJSON(result) : null
  }

  async getByContactId (contactId) {
    const activities = await this.getAll()
    return activities
      .filter(activity => activity.contact === contactId)
      .sort((a, b) => new Date(b.creationDateTime) - new Date(a.creationDateTime))
  }

  async findBy (filterOptions = {}) {
    const activities = await this.getAll()
    let filteredActivities = activities

    // Filter by contact
    if (filterOptions.contact) {
      filteredActivities = filteredActivities.filter(activity =>
        activity.contact === filterOptions.contact,
      )
    }

    // Filter by activity creator
    if (filterOptions.activityCreator) {
      filteredActivities = filteredActivities.filter(activity =>
        activity.activityCreator === filterOptions.activityCreator,
      )
    }

    // Filter by note content
    if (filterOptions.note) {
      filteredActivities = filteredActivities.filter(activity =>
        activity.note.toLowerCase().includes(filterOptions.note.toLowerCase()),
      )
    }

    return filteredActivities
  }

  async search (query) {
    if (!query || query.trim().length < 2) {
      return await this.getAll()
    }

    const activities = await this.getAll()
    const searchTerm = query.toLowerCase().trim()

    return activities.filter(activity => {
      // Search in note content
      return activity.note && activity.note.toLowerCase().includes(searchTerm)
    })
  }

  async seedData () {
    const existingActivities = await this.getAll()
    if (existingActivities.length > 0) {
      return
    } // Already seeded

    const { activitySeeds } = await import('@/seeds/activities')

    for (const activityData of activitySeeds) {
      await this.create(activityData)
    }
  }
}
