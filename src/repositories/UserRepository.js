import { User } from '@/models/User'
import { BaseRepository } from '@/repositories/BaseRepository'
import { IStorageService, LocalStorageService } from '@/services/StorageService'

export class UserRepository extends BaseRepository {
  constructor (storageService = new LocalStorageService()) {
    super(storageService, 'users', User)
    this.currentUser = null
    this.initializeCurrentUser()
  }

  async search (query) {
    const items = await this.getAll()

    if (!query || query.trim().length < 2) {
      return items
    }

    const searchTerms = query.toLowerCase().trim().split(' ')

    return items.filter(item => {
      return searchTerms.some(searchTerm =>
        item.fullName.toLowerCase().includes(searchTerm),
      )
    })
  }

  async initializeCurrentUser () {
    // Set <PERSON> as the current user
    const users = await this.getAll()
    this.currentUser = users.find(user =>
      user.firstName === 'John' && user.lastName === 'Smith',
    ) || null
  }

  async getAll () {
    const data = await super.getAll()
    return data.map(userData => User.fromJSON(userData))
  }

  async getById (id) {
    const userData = await super.getById(id)
    return userData ? User.fromJSON(userData) : null
  }

  async create (userData) {
    const user = new User(userData)
    const result = await super.create(user.toJSON())
    return User.fromJSON(result)
  }

  async findBy (filterOptions = {}) {
    const users = await this.getAll()
    let filteredUsers = users

    // Filter by active status
    if (filterOptions.isActive !== undefined) {
      filteredUsers = filteredUsers.filter(user => user.isActive === filterOptions.isActive)
    }

    // Filter by access
    if (filterOptions.access && filterOptions.access.length > 0) {
      filteredUsers = filteredUsers.filter(user =>
        filterOptions.access.some(access => user.access.includes(access)),
      )
    }

    // Filter by email domain
    if (filterOptions.emailDomain) {
      filteredUsers = filteredUsers.filter(user =>
        user.email.includes(filterOptions.emailDomain),
      )
    }

    // Exclude current user if specified
    if (filterOptions.excludeCurrentUser && this.currentUser) {
      filteredUsers = filteredUsers.filter(user => user.id !== this.currentUser.id)
    }

    return filteredUsers
  }

  async getAllExceptCurrent () {
    return this.findBy({ excludeCurrentUser: true })
  }

  getCurrentUser () {
    return this.currentUser
  }

  async seedData () {
    const existingUsers = await this.getAll()
    if (existingUsers.length > 0) {
      return
    } // Already seeded

    const { userSeeds } = await import('@/seeds/users')

    for (const userData of userSeeds) {
      await this.create(userData)
    }

    // Re-initialize current user after seeding
    await this.initializeCurrentUser()
  }
}
