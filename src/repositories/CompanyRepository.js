import { Company } from '@/models/Company'
import { BaseRepository } from '@/repositories/BaseRepository'
import { LocalStorageService } from '@/services/StorageService'

export class CompanyRepository extends BaseRepository {
  constructor (storageService = new LocalStorageService()) {
    super(storageService, 'companies', Company)
  }

  async getAll () {
    const data = await super.getAll()
    return data.map(companyData => Company.fromJSON(companyData))
  }

  async getById (id) {
    const companyData = await super.getById(id)
    return companyData ? Company.fromJSON(companyData) : null
  }

  async create (companyData) {
    const company = new Company(companyData)
    const result = await super.create(company.toJSON())
    return Company.fromJSON(result)
  }

  async findBy (filterOptions = {}) {
    let filteredCompanies = await this.getAll()

    // Filter by industry
    if (filterOptions.industry) {
      filteredCompanies = filteredCompanies.filter(company =>
        company.industry.toLowerCase().includes(filterOptions.industry.toLowerCase()),
      )
    }

    // Filter by location
    if (filterOptions.location) {
      filteredCompanies = filteredCompanies.filter(company =>
        company.location.toLowerCase().includes(filterOptions.location.toLowerCase()),
      )
    }

    // Filter by name
    if (filterOptions.name) {
      filteredCompanies = filteredCompanies.filter(company =>
        company.name.toLowerCase().includes(filterOptions.name.toLowerCase()),
      )
    }

    return filteredCompanies
  }

  async search (query) {
    const companies = await this.getAll()

    if (!query || query.trim().length < 2) {
      return companies
    }

    const searchTerm = query.toLowerCase().trim()

    return companies.filter(company => {
      // Search across all relevant fields
      const searchableFields = [
        company.name,
        company.email,
        company.location,
        company.industry,
        company.phoneNumber,
        company.formattedPhone,
      ]

      return searchableFields.some(field =>
        field && field.toLowerCase().includes(searchTerm),
      )
    })
  }

  async seedData () {
    const existingCompanies = await this.getAll()
    if (existingCompanies.length > 0) {
      return
    } // Already seeded

    const { companySeeds } = await import('@/seeds/companies')

    for (const companyData of companySeeds) {
      await this.create(companyData)
    }
  }
}
