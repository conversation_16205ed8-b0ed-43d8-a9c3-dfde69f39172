import { Contact } from '@/models/Contact'
import { BaseRepository } from '@/repositories/BaseRepository'
import { LocalStorageService } from '@/services/StorageService'

export class ContactRepository extends BaseRepository {
  constructor (storageService = new LocalStorageService()) {
    super(storageService, 'contacts', Contact)
    this.companies = new Map()
  }

  setCompanies (value) {
    this.companies = value
  }

  async getAll () {
    const data = await super.getAll()
    return data.map(contactData => Contact.fromJSON(contactData))
  }

  async getById (id) {
    const contactData = await super.getById(id)
    return contactData ? Contact.fromJSON(contactData) : null
  }

  async create (contactData) {
    const contact = new Contact(contactData)
    const result = await super.create(contact.toJSON())
    return Contact.fromJSON(result)
  }

  async findBy (filterOptions = {}) {
    let filteredContacts = await this.getAll()

    // Filter by company
    if (filterOptions.company) {
      filteredContacts = filteredContacts.filter(contact =>
        contact.company === filterOptions.company,
      )
    }

    // Filter by contact owner
    if (filterOptions.contactOwner) {
      filteredContacts = filteredContacts.filter(contact =>
        contact.contactOwner === filterOptions.contactOwner,
      )
    }

    // Filter by name
    if (filterOptions.name) {
      filteredContacts = filteredContacts.filter(contact =>
        contact.fullName.toLowerCase().includes(filterOptions.name.toLowerCase()),
      )
    }

    // Filter by email
    if (filterOptions.email) {
      filteredContacts = filteredContacts.filter(contact =>
        contact.email.toLowerCase().includes(filterOptions.email.toLowerCase()),
      )
    }

    return filteredContacts
  }

  async search (query) {
    const contacts = await this.getAll()
    if (!query || query.trim().length < 2) {
      return contacts
    }

    const searchTerm = query.toLowerCase().trim()

    return contacts.filter(contact => {
      const company = this.companies.get(contact.company)
      // Search across all relevant fields
      const searchableFields = [
        contact.firstName,
        contact.lastName,
        contact.fullName,
        contact.email,
        contact.phoneNumber,
        contact.formattedPhone,
        company?.name,
      ]

      return searchableFields.some(field =>
        field && field.toLowerCase().includes(searchTerm),
      )
    })
  }

  async seedData () {
    const existingContacts = await this.getAll()
    if (existingContacts.length > 0) {
      return
    } // Already seeded

    const { contactSeeds } = await import('@/seeds/contacts')

    for (const contactData of contactSeeds) {
      await this.create(contactData)
    }
  }
}
