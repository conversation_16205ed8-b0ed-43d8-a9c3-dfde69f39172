// Repository base class
export class BaseRepository {
  constructor (storageService, storageKey, entity) {
    this.storage = storageService
    this.storageKey = storageKey
    this.entity = entity
  }

  async getAll () {
    const data = await this.storage.get(this.storageKey)
    return data || []
  }

  async getById (id) {
    const items = await this.getAll()
    return items.find(item => item.id === id) || null
  }

  async create (item) {
    const items = await this.getAll()
    items.push(item)
    await this.storage.set(this.storageKey, items)
    return item
  }

  async update (id, updates) {
    const items = await this.getAll()
    const model = items.find(item => item.id === id)
    if (!model) {
      return null
    }

    const history = model.history || []
    const historyItem = { createdAt: Date.now(), changes: [] }
    for (const key of Object.keys(model)) {
      if (key in updates && model[key] !== updates[key]) {
        historyItem.changes.push({ field: key, from: model[key], to: updates[key] })
        model[key] = updates[key]
      }
    }

    if (historyItem.changes.length > 0) {
      history.push(historyItem)
      model.history = history
    }
    await this.storage.set(this.storageKey, items)
    return model
  }

  async delete (id) {
    const items = await this.getAll()
    const filteredItems = items.filter(item => item.id !== id)
    await this.storage.set(this.storageKey, filteredItems)
    return true
  }

  async findBy (filterFn) {
    const items = await this.getAll()
    return items.filter(filterFn)
  }

  /**
   * @param {number} page
   * @param {number} perPage
   * @param {string} sortBy
   * @param {string} sortOrder
   * @param {string} searchQuery
   * @returns {Promise<{total, page, pages: number, perPage, data: object[]}>}
   */
  async paginate (page, perPage, sortBy, sortOrder, searchQuery = '', filters = []) {
    const offset = (page - 1) * perPage
    const items = await this.search(searchQuery)

    if (sortBy) {
      items.sort((a, b) => {
        if (sortOrder === 'desc') {
          if (b[sortBy] > a[sortBy]) {
            return 1
          }
          if (b[sortBy] < a[sortBy]) {
            return -1
          }
          return 0
        } else {
          if (a[sortBy] > b[sortBy]) {
            return 1
          }
          if (a[sortBy] < b[sortBy]) {
            return -1
          }
          return 0
        }
      })
    }

    const pages = Math.ceil(items.length / perPage)
    const data = (perPage === -1 ? items : items.slice(offset, offset + perPage)).map(item => this.entity.fromJSON(item))
    return {
      total: items.length,
      page,
      pages,
      perPage,
      data,
    }
  }

  /**
   * @param {string} query
   * @returns {Promise<object[]>}
   */
  async search (query) {
    return this.getAll()
  }
}
