/**
 * Browser polyfills for Node.js modules
 * This file must be imported before any other modules that depend on Node.js APIs
 */

import { <PERSON><PERSON><PERSON> } from 'buffer'

// Make Buffer available globally
if (typeof globalThis !== 'undefined') {
  globalThis.Buffer = Buffer
}
if (typeof window !== 'undefined') {
  window.Buffer = Buffer
}
if (typeof global !== 'undefined') {
  global.Buffer = Buffer
}

// Also ensure process.env is available
if (typeof process === 'undefined') {
  globalThis.process = { env: {} }
}

console.log('[Polyfills] Buffer and process polyfills loaded')