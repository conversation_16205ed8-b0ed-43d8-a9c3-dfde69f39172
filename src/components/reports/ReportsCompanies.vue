<template>
  <v-card>
    <v-card-title>
      <div class="d-flex justify-space-between">
        <span>Companies</span>
        <v-btn density="comfortable" icon="mdi-download-circle-outline" variant="text" @click="exportCompanies" />
      </div>
    </v-card-title>
    <v-card-text>
      <v-data-table-server
        :headers="headers"
        item-value="companyId"
        :items="paginatedItems"
        :items-length="deals.length"
        :items-per-page="pagination.perPage.value"
        :loading="loading"
        :page="pagination.currentPage.value"
        :sort-by="pagination.sortBy.value"
        @update:items-per-page="pagination.setPerPage"
        @update:page="pagination.setCurrentPage"
        @update:sort-by="pagination.setSortBy"
      >
        <template #item.name="{ item }">
          <div class="d-flex align-center cursor-pointer  " @click="viewCompany(item)">
            <v-avatar class="me-3" size="32">
              <v-img v-if="item.logo" :src="item.logo" />
              <v-icon v-else icon="mdi-domain" />
            </v-avatar>
            <span>{{ item.name }}</span>
          </div>
        </template>

        <template #item.totalCount="{ item }">
          <v-tooltip interactive>
            <template #activator="{ props: activatorProps }">
              <div class="d-flex align-center justify-center ga-1">
                <span class="font-weight-medium">{{ item.totalCount }}</span>
                <v-btn
                  density="comfortable"
                  icon="mdi-information-outline"
                  size="x-small"
                  variant="text"
                  v-bind="activatorProps"
                />
              </div>
            </template>
            <div>
              <div class="d-flex ga-1">
                <span>In Progress:</span>
                <span class="flex-grow-1 text-right">{{ item.activeCount }}</span>
              </div>
              <div class="d-flex ga-1">
                <span>Lost:</span>
                <span class="flex-grow-1 text-right">{{ item.lostCount }}</span>
              </div>
              <div class="d-flex ga-1">
                <span>Won:</span>
                <span class="flex-grow-1 text-right">{{ item.wonCount }}</span>
              </div>
              <div class="d-flex ga-1">
                <span>Total:</span>
                <span class="flex-grow-1 text-right">{{ item.totalCount }}</span>
              </div>
            </div>
          </v-tooltip>
        </template>

        <template #item.totalBudget="{ item }">
          <v-tooltip interactive>
            <template #activator="{ props: activatorProps }">
              <div class="d-flex align-center justify-end ga-1">
                <span class="font-weight-medium">{{ formatBudget(item.totalBudget) }}</span>
                <v-btn
                  density="comfortable"
                  icon="mdi-information-outline"
                  size="x-small"
                  variant="text"
                  v-bind="activatorProps"
                />
              </div>
            </template>
            <div>
              <div class="d-flex ga-1">
                <span>In Progress:</span>
                <span class="flex-grow-1 text-right">{{ formatBudget(item.activeBudget) }}</span>
              </div>
              <div class="d-flex ga-1">
                <span>Lost:</span>
                <span class="flex-grow-1 text-right">{{ formatBudget(item.lostBudget) }}</span>
              </div>
              <div class="d-flex ga-1">
                <span>Won:</span>
                <span class="flex-grow-1 text-right">{{ formatBudget(item.wonBudget) }}</span>
              </div>
              <div class="d-flex ga-1">
                <span>Total:</span>
                <span class="flex-grow-1 text-right">{{ formatBudget(item.totalBudget) }}</span>
              </div>
            </div>
          </v-tooltip>
        </template>

        <template #no-data>
          <div class="text-center pa-4">
            <v-icon class="mb-2" color="grey" size="64">mdi-domain</v-icon>
            <p class="text-grey">No companies found</p>
          </div>
        </template>
      </v-data-table-server>
    </v-card-text>
  </v-card>
</template>

<script setup>
  import { computed, onMounted, ref, watch } from 'vue'
  import { useRouter } from 'vue-router'
  import { usePagination } from '@/composables/usePagination'
  import { useVoiceAgent } from '@/composables/useVoiceAgent'
  import { CompanyRepository } from '@/repositories/CompanyRepository'
  import { exportCsvFile } from '@/utils/csv'
  import { formatBudget } from '@/utils/format'

  const props = defineProps({
    deals: {
      type: Array,
      required: true,
    },
  })

  const router = useRouter()
  const companyRepository = new CompanyRepository()
  const loading = ref(false)
  const companies = ref(new Map())

  const headers = [
    {
      title: 'Company',
      align: 'start',
      sortable: true,
      key: 'name',
    },
    {
      title: 'Deals',
      align: 'center',
      sortable: true,
      key: 'totalCount',
    },
    {
      title: 'Budget',
      align: 'end',
      sortable: true,
      key: 'totalBudget',
    },
  ]

  const pagination = usePagination(null, 'totalBudget', 'desc')

  const groupedCompanyData = computed(() => {
    if (!props.deals || props.deals.length === 0) {
      return []
    }

    const companyGroups = new Map()

    for (const deal of props.deals) {
      if (!deal.company) {
        continue
      }

      const companyId = deal.company
      const budget = Number(deal.budget) || 0

      if (!companyGroups.has(companyId)) {
        companyGroups.set(companyId, {
          companyId,
          totalCount: 0,
          totalBudget: 0,
          activeCount: 0,
          activeBudget: 0,
          lostCount: 0,
          lostBudget: 0,
          wonCount: 0,
          wonBudget: 0,
        })
      }

      const group = companyGroups.get(companyId)
      group.totalCount += 1
      group.totalBudget += budget
      group.activeCount += deal.isActive() ? 1 : 0
      group.activeBudget += deal.isActive() ? budget : 0
      group.lostCount += deal.isLost() ? 1 : 0
      group.lostBudget += deal.isLost() ? budget : 0
      group.wonCount += deal.isWon() ? 1 : 0
      group.wonBudget += deal.isWon() ? budget : 0
    }

    return Array.from(companyGroups.values()).map(group => {
      const company = companies.value.get(group.companyId)
      return {
        ...group,
        id: company?.id,
        name: company?.name || 'Unknown Company',
        logo: company?.logo || null,
      }
    })
  })

  const sortedData = computed(() => {
    if (groupedCompanyData.value.length === 0) {
      return []
    }

    const data = [...groupedCompanyData.value]

    if (pagination.sortBy.value.length === 0) {
      return data
    }

    const sortItem = pagination.sortBy.value[0]
    const { key, order } = sortItem

    return data.sort((a, b) => {
      let aValue = a[key]
      let bValue = b[key]

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (order === 'desc') {
        return bValue > aValue ? 1 : (bValue < aValue ? -1 : 0)
      }
      return aValue > bValue ? 1 : (aValue < bValue ? -1 : 0)
    })
  })

  const paginatedItems = computed(() => {
    const start = (pagination.currentPage.value - 1) * pagination.perPage.value
    const end = start + pagination.perPage.value
    return sortedData.value.slice(start, end)
  })

  const loadCompanies = async () => {
    try {
      loading.value = true
      await companyRepository.seedData()
      const allCompanies = await companyRepository.getAll()

      const companiesMap = new Map()
      for (const company of allCompanies) {
        companiesMap.set(company.id, company)
      }
      companies.value = companiesMap
    } catch (error) {
      console.error('Error loading companies:', error)
    } finally {
      loading.value = false
    }
  }

  const loadItems = () => {
    pagination.itemsTotal.value = sortedData.value.length
  }

  const viewCompany = company => {
    router.push(`/company-details?id=${company.id}`)
  }

  const editCompany = company => {
    router.push(`/company-edit?id=${company.id}`)
  }

  const exportCompanies = () => {
    const data = sortedData.value.map(item => {
      return {
        'Company': item.name,
        'Total Deals': item.totalCount,
        'In Progress Deals': item.activeCount,
        'Lost Deals': item.lostCount,
        'Won Deals': item.wonCount,
        'Total Budget': formatBudget(item.totalBudget),
        'In Progress Budget': formatBudget(item.activeBudget),
        'Lost Budget': formatBudget(item.lostBudget),
        'Won Budget': formatBudget(item.wonBudget),
      }
    })
    exportCsvFile(data, 'reports-companies.csv')
  }

  const sortReportsCompaniesBy = {
    name: 'sortReportsCompaniesBy',
    description: 'Sort reports companies by key and order',
    params: [
      {
        type: 'string',
        enum: headers.map(item => item.key),
        required: true,
      },
      {
        type: 'string',
        enum: ['asc', 'desc'],
        required: true,
      },
    ],
    tool: (key, order = 'asc') => {
      pagination.setSortBy([{ key, order }])
    },
  }

  const viewCompanyTool = {
    name: 'viewCompanyTool',
    description: 'Show company details by ID',
    params: [
      {
        type: 'string',
        required: true,
      },
    ],
    tool: id => {
      viewCompany({ id })
    },
  }

  const editCompanyTool = {
    name: 'editCompanyTool',
    description: 'Edit company by ID',
    params: [
      {
        type: 'string',
        required: true,
      },
    ],
    tool: id => {
      editCompany({ id })
    },
  }

  const exportCompaniesTool = {
    name: 'exportCompaniesTool',
    description: 'Export companies table',
    params: [],
    tool: exportCompanies,
  }

  const voiceAgent = useVoiceAgent({
    description: 'Reports companies table',
    tools: {
      sortReportsCompaniesBy,
      viewCompanyTool,
      editCompanyTool,
      exportCompaniesTool,
    },
  })

  watch(() => paginatedItems.value, newItems => {
    voiceAgent.setDescription(`Reports companies table.
Companies list: ${JSON.stringify(newItems.map(({ id, name }) => ({ id, name })))}`)
  })

  watch(sortedData, () => {
    loadItems()
  }, { immediate: true })

  watch(() => props.deals, () => {
    loadItems()
  }, { deep: true })

  onMounted(() => {
    loadCompanies()
  })
</script>
