<template>
  <v-row>
    <v-col cols="12" md="3" sm="6">
      <KPICard
        :active="selected === 'total'"
        :clickable="true"
        format="currency"
        icon="mdi-currency-usd"
        :title="title('Total', deals.length)"
        :value="totalBudget"
        @click="setSelected('total')"
      />
    </v-col>
    <v-col cols="12" md="3" sm="6">
      <KPICard
        :active="selected === 'active'"
        :clickable="true"
        color="warning"
        format="currency"
        icon="mdi-currency-usd"
        :title="title('In Progress', active.length)"
        :value="totalActive"
        @click="setSelected('active')"
      />
    </v-col>
    <v-col cols="12" md="3" sm="6">
      <KPICard
        :active="selected === 'lost'"
        :clickable="true"
        color="error"
        format="currency"
        icon="mdi-currency-usd"
        :title="title('Lost', lost.length)"
        :value="totalLost"
        @click="setSelected('lost')"
      />
    </v-col>
    <v-col cols="12" md="3" sm="6">
      <KPICard
        :active="selected === 'won'"
        :clickable="true"
        color="success"
        format="currency"
        icon="mdi-currency-usd"
        :title="title('Won', won.length)"
        :value="totalWon"
        @click="setSelected('won')"
      />
    </v-col>
  </v-row>
</template>

<script setup>
  import { computed, defineEmits, onMounted, ref, watch } from 'vue'
  import KPICard from '@/components/dashboard/KPICard.vue'
  import { useVoiceAgent } from '@/composables/useVoiceAgent'

  const props = defineProps({
    deals: {
      type: Array,
      default: () => [],
    },
  })

  const emit = defineEmits(['select-deals'])

  const selected = ref('total')

  const title = (text, count) => `${text} ${count} Deals`

  const totalBudget = computed(() => {
    return props.deals.reduce((sum, deal) => sum + deal.budget, 0)
  })

  const won = computed(() => {
    return props.deals.filter(deal => deal.status === 'closed won')
  })

  const totalWon = computed(() => {
    return won.value.reduce((sum, deal) => sum + deal.budget, 0)
  })

  const lost = computed(() => {
    return props.deals
      .filter(deal => deal.status === 'closed lost')
  })

  const totalLost = computed(() => {
    return lost.value.reduce((sum, deal) => sum + deal.budget, 0)
  })

  const active = computed(() => {
    return props.deals
      .filter(deal => !['closed won', 'closed lost'].includes(deal.status))
  })

  const totalActive = computed(() => {
    return active.value.reduce((sum, deal) => sum + deal.budget, 0)
  })

  const setSelected = type => {
    selected.value = type
    switch (type) {
      case 'total': {
        emit('select-deals', props.deals)
        break
      }
      case 'active': {
        emit('select-deals', active.value)
        break
      }
      case 'lost': {
        emit('select-deals', lost.value)
        break
      }
      case 'won': {
        emit('select-deals', won.value)
        break
      }
    }
  }

  const setDealsStatusFilter = {
    name: 'setDealsStatusFilter',
    description: 'Filter reports deals by status: total - all deals, active - in progress deals, lost - closed lost',
    params: [
      {
        type: 'number',
        enum: ['total', 'active', 'lost', 'won'],
        required: true,
      },
    ],
    tool: value => {
      setSelected(value)
    },
  }

  useVoiceAgent({
    description: 'Reports totals component shows selectable cards with info about total deals count and budget',
    tools: {
      setDealsStatusFilter,
    },
  })

  watch(() => props.deals, () => setSelected(selected.value))

  onMounted(() => {
    setSelected('total')
  })
</script>
