<template>
  <v-card>
    <v-card-title>{{ title }}</v-card-title>
    <v-card-text>
      <div ref="chartContainer" style="height: 320px; width: 100%;" />
      <div class="d-flex justify-center">
        <v-btn-group density="compact" divided variant="outlined">
          <v-btn
            v-for="item in chartTypes"
            :key="item.value"
            :active="chartType===item.value"
            size="x-small"
            @click="setChartType(item.value)"
          >
            {{ item.label }}
          </v-btn>
        </v-btn-group>
      </div>
    </v-card-text>
  </v-card>
</template>

<script setup>
  import * as echarts from 'echarts'
  import { computed, nextTick, onMounted, ref, watch } from 'vue'
  import { useVoiceAgent } from '@/composables/useVoiceAgent'

  const props = defineProps({
    title: {
      type: String,
      required: true,
    },
    deals: {
      type: Array,
      required: true,
    },
    period: {
      type: Number,
      required: true,
    },
  })

  const chartTypes = [{ value: 'line', label: 'Line' }, { value: 'bar', label: 'Bar' }]

  const chartContainer = ref(null)
  const chartType = ref(chartTypes[0].value)
  let chart = null

  const groupedData = computed(() => {
    if (!props.deals || props.deals.length === 0) {
      return { categories: [], data: [] }
    }

    const sortedDeals = [...props.deals]
      .filter(deal => deal.createdAt && deal.budget)
      .sort((a, b) => a.createdAt - b.createdAt)

    if (sortedDeals.length === 0) {
      return { categories: [], data: [] }
    }

    // Group by month for period -1 or > 30
    if (props.period === -1 || props.period > 30) {
      const monthlyData = new Map()

      for (const deal of sortedDeals) {
        const date = new Date(deal.createdAt)
        const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`
        const monthLabel = date.toLocaleDateString('en-US', { month: 'short' })

        if (!monthlyData.has(monthKey)) {
          monthlyData.set(monthKey, { label: monthLabel, total: 0 })
        }
        monthlyData.get(monthKey).total += Number(deal.budget) || 0
      }

      const sortedMonthly = Array.from(monthlyData.entries())
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([, value]) => value)

      return {
        categories: sortedMonthly.map(item => item.label),
        data: sortedMonthly.map(item => item.total),
      }
    }

    // Group by hour for period = 1
    if (props.period === 1) {
      const hourlyData = new Map()

      for (const deal of sortedDeals) {
        const date = new Date(deal.createdAt)
        const hour = date.getHours()
        const hourLabel = date.toLocaleTimeString('en-US', {
          hour: 'numeric',
          hour12: true,
        }).replace(':00', '')

        if (!hourlyData.has(hour)) {
          hourlyData.set(hour, { label: hourLabel, total: 0 })
        }
        hourlyData.get(hour).total += Number(deal.budget) || 0
      }

      const sortedHourly = Array.from(hourlyData.entries())
        .sort(([a], [b]) => a - b)
        .map(([, value]) => value)

      return {
        categories: sortedHourly.map(item => item.label),
        data: sortedHourly.map(item => item.total),
      }
    }

    // Group by day for period > 1 and <= 30
    const dailyData = new Map()

    for (const deal of sortedDeals) {
      const date = new Date(deal.createdAt)
      const dayKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`
      const dayLabel = date.toLocaleDateString('en-US', {
        day: 'numeric',
        month: 'short',
      })

      if (!dailyData.has(dayKey)) {
        dailyData.set(dayKey, { label: dayLabel, total: 0 })
      }
      dailyData.get(dayKey).total += Number(deal.budget) || 0
    }

    const sortedDaily = Array.from(dailyData.entries())
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([, value]) => value)

    return {
      categories: sortedDaily.map(item => item.label),
      data: sortedDaily.map(item => item.total),
    }
  })

  const chartOptions = computed(() => ({
    tooltip: {
      trigger: 'axis',
      formatter: params => {
        const value = params[0].value
        const formattedValue = new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        }).format(value)
        return `${params[0].name}: ${formattedValue}`
      },
    },
    xAxis: {
      type: 'category',
      data: groupedData.value.categories,
      nameLocation: 'middle',
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: value => {
          return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
            notation: 'compact',
          }).format(value)
        },
      },
    },
    series: [
      {
        type: chartType.value,
        smooth: true,
        data: groupedData.value.data,
        lineStyle: {
          width: 3,
          color: '#1976d2',
        },
        itemStyle: {
          color: '#1976d2',
        },
        areaStyle: {
          color: 'rgba(25, 118, 210, 0.1)',
        },
      },
    ],
    grid: {
      left: 8,
      right: 8,
      top: 8,
      bottom: 8,
      containLabel: true,
    },
  }))

  const initChart = async () => {
    if (!chartContainer.value) return

    await nextTick()

    if (chart) {
      chart.dispose()
    }

    chart = echarts.init(chartContainer.value)
    chart.setOption(chartOptions.value)
  }

  const resizeChart = () => {
    if (chart) {
      chart.resize()
    }
  }

  const setChartType = type => {
    chartType.value = type
  }

  const setBudgetChartType = {
    name: 'setBudgetChartType',
    description: 'Set reports budget shart type',
    params: [
      {
        type: 'string',
        enum: ['line', 'bar'],
        required: true,
      },
    ],
    tool: value => {
      setChartType(value)
    },
  }

  useVoiceAgent({
    description: 'Reports budget chart',
    tools: {
      setBudgetChartType,
    },
  })

  onMounted(() => {
    initChart()
    window.addEventListener('resize', resizeChart)
  })

  watch([() => props.deals, () => props.period, () => chartType], () => {
    if (chart) {
      chart.setOption(chartOptions.value)
    }
  }, { deep: true })
</script>
