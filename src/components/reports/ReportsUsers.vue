<template>
  <v-card>
    <v-card-title>
      <div class="d-flex justify-space-between">
        <span>Users</span>
        <v-btn density="comfortable" icon="mdi-download-circle-outline" variant="text" @click="exportUsers" />
      </div>
    </v-card-title>
    <v-card-text>
      <v-data-table-server
        :headers="headers"
        item-value="userId"
        :items="paginatedItems"
        :items-length="users.size"
        :items-per-page="pagination.perPage.value"
        :loading="loading"
        :page="pagination.currentPage.value"
        :sort-by="pagination.sortBy.value"
        @update:items-per-page="pagination.setPerPage"
        @update:page="pagination.setCurrentPage"
        @update:sort-by="pagination.setSortBy"
      >
        <template #item.fullName="{ item }">
          <div class="d-flex align-center cursor-pointer" @click="viewUser(item.id)">
            <v-avatar class="me-3" size="32">
              <v-img v-if="item.avatar" :src="item.avatar">
                <template #error>
                  <v-icon icon="mdi-account-circle" />
                </template>
              </v-img>
              <v-icon v-else icon="mdi-account-circle" />
            </v-avatar>
            <span>{{ item.fullName }}</span>
          </div>
        </template>

        <template #item.totalCount="{ item }">
          <v-tooltip interactive>
            <template #activator="{ props: activatorProps }">
              <div class="d-flex align-center justify-center ga-1">
                <span class="font-weight-medium">{{ item.totalCount }}</span>
                <v-btn
                  density="comfortable"
                  icon="mdi-information-outline"
                  size="x-small"
                  variant="text"
                  v-bind="activatorProps"
                />
              </div>
            </template>
            <div>
              <div class="d-flex ga-1">
                <span>In Progress:</span>
                <span class="flex-grow-1 text-right">{{ item.activeCount }}</span>
              </div>
              <div class="d-flex ga-1">
                <span>Lost:</span>
                <span class="flex-grow-1 text-right">{{ item.lostCount }}</span>
              </div>
              <div class="d-flex ga-1">
                <span>Won:</span>
                <span class="flex-grow-1 text-right">{{ item.wonCount }}</span>
              </div>
              <div class="d-flex ga-1">
                <span>Total:</span>
                <span class="flex-grow-1 text-right">{{ item.totalCount }}</span>
              </div>
            </div>
          </v-tooltip>
        </template>

        <template #item.totalBudget="{ item }">
          <v-tooltip interactive>
            <template #activator="{ props: activatorProps }">
              <div class="d-flex align-center justify-end ga-1">
                <span class="font-weight-medium">{{ formatBudget(item.totalBudget) }}</span>
                <v-btn
                  density="comfortable"
                  icon="mdi-information-outline"
                  size="x-small"
                  variant="text"
                  v-bind="activatorProps"
                />
              </div>
            </template>
            <div>
              <div class="d-flex ga-1">
                <span>In Progress:</span>
                <span class="flex-grow-1 text-right">{{ formatBudget(item.activeBudget) }}</span>
              </div>
              <div class="d-flex ga-1">
                <span>Lost:</span>
                <span class="flex-grow-1 text-right">{{ formatBudget(item.lostBudget) }}</span>
              </div>
              <div class="d-flex ga-1">
                <span>Won:</span>
                <span class="flex-grow-1 text-right">{{ formatBudget(item.wonBudget) }}</span>
              </div>
              <div class="d-flex ga-1">
                <span>Total:</span>
                <span class="flex-grow-1 text-right">{{ formatBudget(item.totalBudget) }}</span>
              </div>
            </div>
          </v-tooltip>
        </template>

        <template #no-data>
          <div class="text-center pa-4">
            <v-icon class="mb-2" color="grey" size="64">mdi-account-circle</v-icon>
            <p class="text-grey">No users found</p>
          </div>
        </template>
      </v-data-table-server>
    </v-card-text>
  </v-card>
</template>

<script setup>
  import { computed, onMounted, ref, watch } from 'vue'
  import { useRouter } from 'vue-router'
  import { usePagination } from '@/composables/usePagination'
  import { useVoiceAgent } from '@/composables/useVoiceAgent'
  import { UserRepository } from '@/repositories/UserRepository'
  import { exportCsvFile } from '@/utils/csv'

  const props = defineProps({
    deals: {
      type: Array,
      required: true,
    },
  })

  const router = useRouter()

  const userRepository = new UserRepository()
  const loading = ref(false)
  const users = ref(new Map())

  const headers = [
    {
      title: 'Name',
      align: 'start',
      sortable: true,
      key: 'fullName',
    },
    {
      title: 'Deals',
      align: 'center',
      sortable: true,
      key: 'totalCount',
    },
    {
      title: 'Budget',
      align: 'end',
      sortable: true,
      key: 'totalBudget',
    },
  ]

  const pagination = usePagination(null, 'totalBudget', 'desc')

  const groupedUserData = computed(() => {
    if (!props.deals || props.deals.length === 0) {
      return []
    }

    const userGroups = new Map()

    for (const deal of props.deals) {
      if (!deal.dealOwner) {
        continue
      }

      const userId = deal.dealOwner
      const budget = Number(deal.budget) || 0

      if (!userGroups.has(userId)) {
        userGroups.set(userId, {
          userId,
          totalCount: 0,
          totalBudget: 0,
          activeCount: 0,
          activeBudget: 0,
          lostCount: 0,
          lostBudget: 0,
          wonCount: 0,
          wonBudget: 0,
        })
      }

      const group = userGroups.get(userId)
      group.totalCount += 1
      group.totalBudget += budget
      group.activeCount += deal.isActive() ? 1 : 0
      group.activeBudget += deal.isActive() ? budget : 0
      group.lostCount += deal.isLost() ? 1 : 0
      group.lostBudget += deal.isLost() ? budget : 0
      group.wonCount += deal.isWon() ? 1 : 0
      group.wonBudget += deal.isWon() ? budget : 0
    }

    return Array.from(userGroups.values()).map(group => {
      const user = users.value.get(group.userId)
      return {
        ...group,
        id: user?.id,
        fullName: user?.fullName || 'Unknown User',
        avatar: user?.avatar || null,
      }
    })
  })

  const formatBudget = value => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value)
  }

  const sortedData = computed(() => {
    if (groupedUserData.value.length === 0) {
      return []
    }

    const data = [...groupedUserData.value]

    if (pagination.sortBy.value.length === 0) {
      return data
    }

    const sortItem = pagination.sortBy.value[0] || { key: 'totalBudget', order: 'desc' }
    const { key, order } = sortItem

    return data.sort((a, b) => {
      let aValue = a[key]
      let bValue = b[key]

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (order === 'desc') {
        return bValue > aValue ? 1 : (bValue < aValue ? -1 : 0)
      }
      return aValue > bValue ? 1 : (aValue < bValue ? -1 : 0)
    })
  })

  const paginatedItems = computed(() => {
    const start = (pagination.currentPage.value - 1) * pagination.perPage.value
    const end = start + pagination.perPage.value
    return sortedData.value.slice(start, end)
  })

  const loadUsers = async () => {
    try {
      loading.value = true
      await userRepository.seedData()
      const allUsers = await userRepository.getAll()

      const usersMap = new Map()
      for (const user of allUsers) {
        usersMap.set(user.id, user)
      }
      users.value = usersMap
    } catch (error) {
      console.error('Error loading users:', error)
    } finally {
      loading.value = false
    }
  }

  const loadItems = () => {
    pagination.itemsTotal.value = sortedData.value.length
  }

  const viewUser = id => {
    router.push(`/settings?user_id=${id}`)
  }

  const exportUsers = () => {
    const data = sortedData.value.map(item => {
      return {
        'Name': item.fullName,
        'Total Deals': item.totalCount,
        'In Progress Deals': item.activeCount,
        'Lost Deals': item.lostCount,
        'Won Deals': item.wonCount,
        'Total Budget': formatBudget(item.totalBudget),
        'In Progress Budget': formatBudget(item.activeBudget),
        'Lost Budget': formatBudget(item.lostBudget),
        'Won Budget': formatBudget(item.wonBudget),
      }
    })
    exportCsvFile(data, 'reports-users.csv')
  }

  const sortReportsUsersByTool = {
    name: 'sortReportsUsersByTool',
    description: 'Sort reports users by key and order',
    params: [
      {
        type: 'string',
        enum: headers.map(item => item.key),
        required: true,
      },
      {
        type: 'string',
        enum: ['asc', 'desc'],
        required: true,
      },
    ],
    tool: (key, order = 'asc') => {
      pagination.setSortBy([{ key, order }])
    },
  }

  const viewEditUserTool = {
    name: 'viewEditUserTool',
    description: 'Show/edit user details by ID',
    params: [
      {
        type: 'string',
        required: true,
      },
    ],
    tool: id => {
      viewUser(id)
    },
  }

  const exportUsersTool = {
    name: 'exportUsersTool',
    description: 'Export users table data',
    params: [],
    tool: exportUsers,
  }

  const voiceAgent = useVoiceAgent({
    description: 'Reports users table',
    tools: {
      sortReportsUsersByTool,
      viewEditUserTool,
      exportUsersTool,
    },
  })

  watch(() => paginatedItems.value, newItems => {
    voiceAgent.setDescription(`Reports users table.
Users list: ${JSON.stringify(newItems.map(({ id, fullName }) => ({ id, name: fullName })))}`)
  })

  watch(sortedData, () => {
    loadItems()
  }, { immediate: true })

  watch(() => props.deals, () => {
    loadItems()
  }, { deep: true })

  onMounted(() => {
    loadUsers()
  })
</script>
