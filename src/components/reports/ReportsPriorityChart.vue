<template>
  <v-card>
    <v-card-title>{{ title }}</v-card-title>
    <v-card-text>
      <div ref="chartContainer" style="height: 320px; width: 100%;" />
      <div class="d-flex justify-center">
        <v-btn-group density="compact" divided variant="outlined">
          <v-btn
            v-for="item in chartTypes"
            :key="item.value"
            :active="chartType===item.value"
            size="x-small"
            @click="setChartType(item.value)"
          >
            {{ item.label }}
          </v-btn>
        </v-btn-group>
      </div>
    </v-card-text>
  </v-card>
</template>

<script setup>
  import * as echarts from 'echarts'
  import { computed, nextTick, onMounted, ref, watch } from 'vue'
  import { useVoiceAgent } from '@/composables/useVoiceAgent'
  import { Deal } from '@/models/Deal'

  const props = defineProps({
    title: {
      type: String,
      required: true,
    },
    deals: {
      type: Array,
      required: true,
    },
  })

  const chartTypes = [{ value: 'pie', label: 'Pie' }, { value: 'bar', label: 'Bar' }]

  const chartContainer = ref(null)
  const chartType = ref(chartTypes[0].value)
  let chart = null

  const priorityData = computed(() => {
    if (!props.deals || props.deals.length === 0) {
      return [
        { value: 0, name: 'Low', itemStyle: { color: '#2196F3' } },
        { value: 0, name: 'Medium', itemStyle: { color: '#ff9800' } },
        { value: 0, name: 'High', itemStyle: { color: '#F44336' } },
      ]
    }

    const priorityCounts = {
      [Deal.PRIORITY.LOW]: 0,
      [Deal.PRIORITY.MEDIUM]: 0,
      [Deal.PRIORITY.HIGH]: 0,
    }

    for (const deal of props.deals) {
      if (deal.priority && priorityCounts.hasOwnProperty(deal.priority)) {
        priorityCounts[deal.priority]++
      }
    }

    return [
      { value: priorityCounts[Deal.PRIORITY.LOW], name: 'Low', itemStyle: { color: '#2196F3' } },
      { value: priorityCounts[Deal.PRIORITY.MEDIUM], name: 'Medium', itemStyle: { color: '#ff9800' } },
      { value: priorityCounts[Deal.PRIORITY.HIGH], name: 'High', itemStyle: { color: '#F44336' } },
    ]
  })

  const chartOptions = computed(() => ({
    grid: {
      left: 8,
      right: 8,
      top: 8,
      bottom: 8,
      containLabel: true,
    },
    tooltip: {
      trigger: 'item',
      formatter: params => {
        const total = priorityData.value.reduce((sum, item) => sum + item.value, 0)
        const percentage = total > 0 ? ((params.value / total) * 100).toFixed(1) : 0
        return `${params.name}: ${params.value} (${percentage}%)`
      },
    },
    legend: {
      show: chartType.value === 'pie',
      orient: 'vertical',
      align: 'left',
      top: 'middle',
      right: 0,
    },
    xAxis: chartType.value === 'bar' ? { show: true, type: 'category', data: ['Low', 'Medium', 'High'] } : { show: false },
    yAxis: chartType.value === 'bar' ? { type: 'value' } : undefined,
    series: [
      {
        name: 'Priority',
        type: chartType.value,
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: chartType.value === 'pie' && {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: chartType.value === 'pie',
            fontSize: 32,
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: priorityData.value,
      },
    ],
  }))

  const initChart = async () => {
    if (!chartContainer.value) return

    await nextTick()

    if (chart) {
      chart.dispose()
    }

    chart = echarts.init(chartContainer.value)
    chart.setOption(chartOptions.value)
  }

  const resizeChart = () => {
    if (chart) {
      chart.resize()
    }
  }

  const setChartType = type => {
    chartType.value = type
  }

  const setPriorityChartType = {
    name: 'setPriorityChartType',
    description: 'Set reports priority shart type',
    params: [
      {
        type: 'string',
        enum: ['pie', 'bar'],
        required: true,
      },
    ],
    tool: value => {
      setChartType(value)
    },
  }

  useVoiceAgent({
    description: 'Reports priority chart',
    tools: {
      setPriorityChartType,
    },
  })

  onMounted(() => {
    initChart()
    window.addEventListener('resize', resizeChart)
  })

  watch([() => props.deals, () => chartType], () => {
    if (chart) {
      chart.setOption(chartOptions.value)
    }
  }, { deep: true })
</script>
