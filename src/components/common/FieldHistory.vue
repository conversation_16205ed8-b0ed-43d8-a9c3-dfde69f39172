<template>
  <v-tooltip v-if="isEdited" interactive>
    <template #activator="{ props: activatorProps }">
      <div class="d-flex align-center ga-1">
        <div :class="highlight && 'highlight'">
          <slot />
        </div>
        <v-btn
          class="align-self-start"
          color="warning"
          density="comfortable"
          icon="mdi-history"
          size="x-small"
          variant="text"
          v-bind="activatorProps"
        />
      </div>
    </template>
    <div>
      <div v-for="item in changes" :key="item.createdAt" class="d-flex flex-column">
        <span>{{ item.createdAt }}</span>
        <div class="pl-4">From: <span v-html="item.from" /></div>
        <div class="pl-4">To: <span v-html="item.to" /></div>
      </div>
    </div>
  </v-tooltip>
  <slot v-else />
</template>

<script setup>
  import { computed, ref, watch } from 'vue'
  import { BaseModel } from '@/models/BaseModel'

  const props = defineProps({
    model: {
      type: BaseModel,
      required: true,
    },
    field: {
      type: String,
      required: true,
    },
    format: {
      type: Function,
      required: false,
    },
  })

  const changes = ref([])
  const highlight = ref(false)

  const isEdited = computed(() => {
    return props.model && props.field && props.model.isFieldChanged(props.field)
  })

  const formatDate = value => {
    return new Date(value).toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    })
  }

  const formatValue = value => {
    return props.format ? props.format(value) : value
  }

  watch([() => props.field, () => props.model, () => props.format], async () => {
    const history = props.model.getFieldChanges(props.field)
    const list = []
    for (const change of history) {
      list.push({
        createdAt: formatDate(change.createdAt),
        from: await formatValue(change.from),
        to: await formatValue(change.to),
      })
    }
    changes.value = list

    // Highlight the field value for 5 seconds when the field has recently changed (less than 2 seconds ago).
    const last = history?.at(-1)
    const blinkDuration = 5000
    if (last && last.createdAt >= Date.now() - 2000) {
      highlight.value = true

      setTimeout(() => {
        highlight.value = false
      }, blinkDuration)
    }
  }, { immediate: true })
</script>

<style scoped>
  .highlight {
    padding: 0 10px;
    border-radius: 8px;
    animation: blink 1500ms ease-in-out infinite;
    background-color: rgba(44, 159, 252, 0.5);
  }

  @keyframes blink {
    0% {
      background-color: rgba(225, 245, 254, .1);
    }
    50% {
      background-color: rgba(44, 159, 252, 0.5);
    }
    100% {
      background-color: rgba(225, 245, 254, .1);
    }
  }
</style>
