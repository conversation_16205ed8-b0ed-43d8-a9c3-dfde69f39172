<template>
  <v-navigation-drawer
    v-model="drawer"
    app
    temporary
  >
    <v-list>
      <v-list-item
        prepend-icon="mdi-home"
        title="Home"
        to="/"
      />
      <v-list-item
        prepend-icon="mdi-domain"
        title="Companies"
        to="/companies"
      />
      <v-list-item
        prepend-icon="mdi-account-group-outline"
        title="Contacts"
        to="/contacts"
      />
      <v-list-item
        prepend-icon="mdi-briefcase-account-outline"
        title="Deals"
        to="/deals"
      />
      <v-list-item
        prepend-icon="mdi-chart-line"
        title="Reports"
        to="/reports"
      />
      <v-list-item
        prepend-icon="mdi-cog"
        title="Settings"
        to="/settings"
      />
    </v-list>
  </v-navigation-drawer>
</template>

<script setup>
  import { computed } from 'vue'

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['update:modelValue'])

  const drawer = computed({
    get: () => props.modelValue,
    set: value => emit('update:modelValue', value),
  })
</script>
