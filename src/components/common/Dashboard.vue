<template>
  <v-container class="pa-6" fluid>
    <!-- Loading State -->
    <div v-if="loading" class="text-center pa-8">
      <v-progress-circular color="primary" indeterminate size="64" />
      <div class="text-h6 mt-4">Loading dashboard data...</div>
    </div>

    <!-- Dashboard Content -->
    <div v-else>
      <!-- Top Row - KPI Metrics -->
      <v-row class="mb-6">
        <v-col cols="12" md="3" sm="6">
          <KPICard
            format="currency"
            icon="mdi-currency-usd"
            title="Total Pipeline Value"
            :value="kpiData.totalPipelineValue"
          />
        </v-col>
        <v-col cols="12" md="3" sm="6">
          <KPICard
            format="number"
            icon="mdi-briefcase-account-outline"
            title="This Month Deals"
            :value="kpiData.dealsClosingThisMonth"
          />
        </v-col>
        <v-col cols="12" md="3" sm="6">
          <KPICard
            format="number"
            icon="mdi-account-group"
            title="Active Contacts"
            :value="kpiData.activeContacts"
          />
        </v-col>
        <v-col cols="12" md="3" sm="6">
          <KPICard
            format="number"
            icon="mdi-timeline-clock"
            title="Recent Activities"
            :value="kpiData.recentActivities"
          />
        </v-col>
      </v-row>

      <!-- Middle Section - Visual Analytics -->
      <v-row class="mb-6">
        <v-col cols="12" md="4">
          <DealPipelineChart />
        </v-col>
        <v-col cols="12" md="4">
          <ActivityTimeline />
        </v-col>
        <v-col cols="12" md="4">
          <TopPerformers />
        </v-col>
      </v-row>

      <!-- Bottom Section - Recent Activity Feed -->
      <v-row>
        <v-col cols="12">
          <RecentActivityFeed />
        </v-col>
      </v-row>
    </div>
  </v-container>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue'
  import ActivityTimeline from '@/components/dashboard/ActivityTimeline.vue'
  import DealPipelineChart from '@/components/dashboard/DealPipelineChart.vue'
  import KPICard from '@/components/dashboard/KPICard.vue'
  import RecentActivityFeed from '@/components/dashboard/RecentActivityFeed.vue'
  import TopPerformers from '@/components/dashboard/TopPerformers.vue'
  import { DashboardService } from '@/services/DashboardService'

  const loading = ref(true)
  const kpiData = ref({
    totalPipelineValue: 0,
    dealsClosingThisMonth: 0,
    activeContacts: 0,
    recentActivities: 0,
  })

  const dashboardService = new DashboardService()

  const loadDashboardData = async () => {
    try {
      loading.value = true
      await dashboardService.initializeData()
      kpiData.value = await dashboardService.getKPIMetrics()
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    loadDashboardData()
  })
</script>
