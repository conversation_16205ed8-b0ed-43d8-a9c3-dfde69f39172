<template>
  <v-app-bar
    app
    color="primary"
    dark
    elevation="1"
  >
    <v-app-bar-nav-icon @click="$emit('toggle-drawer')" />
    <v-app-bar-title
      style="cursor: pointer; max-width: 140px"
      @click="goHome"
    >
      Sentient CRM
    </v-app-bar-title>

    <v-spacer />
  </v-app-bar>
</template>

<script setup>
  import { useRouter } from 'vue-router'

  const router = useRouter()

  defineEmits(['toggle-drawer'])

  const goHome = () => {
    router.push('/')
  }
</script>
