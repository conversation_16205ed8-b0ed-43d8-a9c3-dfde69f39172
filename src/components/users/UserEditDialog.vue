<template>
  <v-dialog
    v-model="dialog"
    max-width="500px"
    persistent
  >
    <v-card>
      <v-card-title class="d-flex justify-space-between align-center">
        <span class="text-h5">Update User</span>
        <v-btn
          icon="mdi-close"
          variant="text"
          @click="closeDialog"
        />
      </v-card-title>

      <v-card-text>
        <v-form ref="form" v-model="valid">
          <v-row>
            <v-col cols="12" sm="6">
              <v-text-field
                v-model="editedUser.firstName"
                label="First Name"
                required
                :rules="[rules.required]"
              />
            </v-col>
            <v-col cols="12" sm="6">
              <v-text-field
                v-model="editedUser.lastName"
                label="Last Name"
                required
                :rules="[rules.required]"
              />
            </v-col>
          </v-row>

          <v-row>
            <v-col cols="12">
              <v-text-field
                v-model="editedUser.email"
                label="Email"
                required
                :rules="[rules.required]"
                type="email"
              />
            </v-col>
          </v-row>

          <v-row>
            <v-col cols="12">
              <v-label class="text-subtitle-2 mb-2">Access</v-label>
              <v-row>
                <v-col
                  v-for="access in accessList"
                  :key="access.value"
                  class="pa-2"
                  cols="6"
                  sm="3"
                >
                  <v-checkbox
                    v-model="editedUser.access"
                    class="access-checkbox"
                    hide-details
                    :label="access.name"
                    :value="access.value"
                  />
                </v-col>
              </v-row>
            </v-col>
          </v-row>

          <v-row>
            <v-col cols="12">
              <v-switch
                v-model="editedUser.isActive"
                color="primary"
                label="Is Active"
              />
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>

      <v-card-actions>
        <v-spacer />
        <v-btn
          text
          @click="closeDialog"
        >
          Cancel
        </v-btn>
        <v-btn
          color="primary"
          :disabled="!valid"
          @click="saveUser"
        >
          Save
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
  import { nextTick, ref, watch } from 'vue'
  import { useVoiceAgent } from '@/composables/useVoiceAgent'
  import { accessList } from '@/constants/access'
  import { EditHistoryService } from '@/services/EditHistoryService.js'

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    user: {
      type: Object,
      default: null,
    },
  })

  const emit = defineEmits(['update:modelValue', 'save'])

  let editHistory = new EditHistoryService()

  const dialog = ref(false)
  const valid = ref(false)
  const form = ref(null)

  const editedUser = ref({
    firstName: '',
    lastName: '',
    email: '',
    access: [],
    isActive: true,
  })

  const rules = {
    required: value => !!value || 'This field is required',
  }

  // Watch for dialog changes
  watch(() => props.modelValue, newVal => {
    dialog.value = newVal
    if (newVal && props.user) {
      // Re-register with voice context when dialog opens
      voiceAgent.register()

      // Reset form and populate with user data
      resetForm()
      editedUser.value = {
        ...props.user,
        access: [...props.user.access], // Clone array to avoid mutations
      }
      voiceAgent.setDescription(`You edit selected user form, refer all commands(except navigation) to this form, entity User: ${JSON.stringify(props.user)}`)
      editHistory = new EditHistoryService(props.user)
    }
  })

  watch(dialog, newVal => {
    emit('update:modelValue', newVal)
  })

  const resetForm = async () => {
    if (form.value) {
      await nextTick()
      form.value.resetValidation()
    }
  }

  const closeDialog = () => {
    dialog.value = false
    resetForm()
    // Explicitly deregister from voice context when dialog closes
    voiceAgent.deregister()
  }

  const saveUser = async () => {
    if (form.value) {
      const { valid: isValid } = await form.value.validate()
      if (isValid) {
        emit('save', {
          id: props.user.id,
          updates: {
            firstName: editedUser.value.firstName,
            lastName: editedUser.value.lastName,
            email: editedUser.value.email,
            access: editedUser.value.access,
            isActive: editedUser.value.isActive,
          },
        })
        closeDialog()
      }
    }
  }

  const changeField = {
    name: 'changeField',
    description: 'Set selected user form field',
    params: [
      {
        name: 'field',
        type: 'string',
        enum: Object.keys(editedUser.value).filter(k => k !== 'access'),
        required: true,
      },
      {
        name: 'value',
        type: 'string',
        required: true,
      },
    ],
    tool: (field, value) => {
      if (field in editedUser.value) {
        editHistory.remember(field, editedUser.value[field])
        editedUser.value[field] = value
      }
    },
  }

  const toggleUserIsActiveState = {
    name: 'toggleUserIsActiveState',
    description: 'Set selected user "is active" value; To deactivate/disable user - set boolean value "false"; To activate/enable user - set boolean value "true"',
    params: [
      {
        name: 'value',
        type: 'boolean',
        required: true,
      },
    ],
    tool: value => {
      editHistory.remember('isActive', editedUser.value.isActive)

      // Process string boolean values properly (even though LLM shouldn't pass string, we're extra careful here)
      let booleanValue
      booleanValue = typeof value === 'string' ? value.toLowerCase() === 'true' : !!value

      editedUser.value.isActive = booleanValue
    },
  }

  const setAccess = {
    name: 'setAccess',
    description: 'Set selected user access value.',
    params: [
      {
        type: 'array',
        description: `Array of values, supported values: [${accessList.map(item => item.value).join(',')}]`,
        items: {
          enum: accessList.map(item => item.value),
        },
        required: true,
      },
    ],
    tool: value => {
      editHistory.remember('access', editedUser.value.access)
      editedUser.value.access = value
    },
  }

  const resetField = field => {
    if (field in editedUser.value) {
      editedUser.value[field] = editHistory.restore(field)
    }
  }

  const resetFrom = () => {
    for (const field in editedUser.value) {
      editedUser.value[field] = editHistory.restore(field)
    }
  }

  const resetFormTool = {
    name: 'resetFormTool',
    description: 'Reset, restore, revert, undo, cancel ALL changes made in form fields',
    params: [],
    tool: resetFrom,
  }

  const voiceAgent = useVoiceAgent({
    description: 'Edit user form modal',
    tools: {
      changeField,
      setAccess,
      toggleUserIsActiveState,
      resetField,
      resetFormTool,
      cancel: closeDialog,
      save: saveUser,
    },
  })
</script>

<style scoped>
:deep(.access-checkbox .v-label) {
  font-size: 14px;
}
</style>
