<template>
  <v-data-table-server
    class="elevation-1"
    :headers="headers"
    :items="items"
    :items-length="itemsTotal"
    :items-per-page="perPage"
    :loading="loading"
    :page="currentPage"
    :sort-by="sortBy"
    @update:items-per-page="setPerPage"
    @update:page="setCurrentPage"
    @update:sort-by="setSortBy"
  >
    <template #item.fullName="{ item }">
      <div class="d-flex align-center" style="cursor: pointer;" @click="editUser(item)">
        <v-avatar class="mr-3" size="36">
          <v-img
            :alt="item.fullName"
            :src="item.avatar"
          >
            <template #error>
              <v-icon>mdi-account-circle</v-icon>
            </template>
          </v-img>
        </v-avatar>
        <span>{{ item.fullName }}</span>
      </div>
    </template>

    <template #item.formattedAccess="{ item }">
      {{ item.formattedAccess }}
    </template>

    <template #item.isActive="{ item }">
      <v-icon :color="item.isActive ? 'success' : 'grey'">
        {{ item.isActive ? 'mdi-radiobox-marked' : 'mdi-radiobox-blank' }}
      </v-icon>
    </template>
  </v-data-table-server>

  <UserEditDialog
    v-model="editDialog"
    :user="selectedUser"
    @save="handleSaveUser"
  />
</template>

<script setup>
  import { onMounted, ref, watch } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { usePagination } from '@/composables/usePagination'
  import { useUserSearchTools } from '@/composables/useUserSearchTools'
  import { useVoiceAgent } from '@/composables/useVoiceAgent'
  import { UserRepository } from '@/repositories/UserRepository'
  import UserEditDialog from './UserEditDialog.vue'

  const route = useRoute()
  const router = useRouter()

  const editDialog = ref(false)
  const selectedUser = ref(null)
  const userRepository = new UserRepository()

  const {
    items,
    itemsTotal,
    loading,
    loadingPromise,
    currentPage,
    lastPage,
    perPage,
    sortBy,
    loadItems,
    setCurrentPage,
    setPerPage,
    nextPage,
    previousPage,
    setSortBy,
  } = usePagination(userRepository)

  const headers = [
    {
      title: 'Name',
      align: 'start',
      sortable: true,
      key: 'fullName',
    },
    {
      title: 'Email',
      align: 'start',
      sortable: true,
      key: 'email',
    },
    {
      title: 'Access',
      align: 'start',
      sortable: false,
      key: 'formattedAccess',
    },
    {
      title: 'Active',
      align: 'center',
      sortable: true,
      key: 'isActive',
    },
  ]

  const editUser = user => {
    selectedUser.value = user
    editDialog.value = true
  }

  const handleSaveUser = async ({ id, updates }) => {
    try {
      await userRepository.update(id, updates)
      await loadItems() // Refresh the table
    } catch (error) {
      console.error('Error updating user:', error)
    }
  }

  const getFirstRecord = () => items.value[0] || null
  const getRecordByIndex = index => index in items.value ? items.value[index] : null

  const tableRef = ref({
    loadingPromise,
    headers,
    getFirstRecord,
    getRecordByIndex,
    view: editUser,
    edit: editUser,
    nextPage,
    previousPage,
    setCurrentPage,
    setSortBy,
    setPerPage,
  })
  const tools = useUserSearchTools(tableRef)

  const voiceAgent = useVoiceAgent({
    description: 'You are on the users list',
    tools,
  })

  watch(() => ({
    currentPage: currentPage.value,
    lastPage: lastPage.value,
    items: items.value,
  }), values => {
    if (!values) {
      return
    }

    const list = values?.items ? values.items?.map(item => ({ id: item.id, name: item.fullName })) : []
    voiceAgent.setDescription([
      'You are on the users list, you can view/edit users from the list',
      `Current page: ${values.currentPage}`,
      `Last page: ${values.lastPage}`,
      `Current users list JSON: ${JSON.stringify(list)}`,
    ].join('. '))
  })

  onMounted(async () => {
    await loadItems()
    if (route.query.user_id) {
      const user = await userRepository.getById(route.query.user_id)
      if (user) {
        editUser(user)
      }
      await router.replace('/settings')
    }
  })
</script>
