<template>
  <v-dialog
    v-model="dialog"
    max-width="500px"
    persistent
  >
    <v-card>
      <v-card-title class="d-flex justify-space-between align-center">
        <span class="text-h5">Add Activity</span>
        <v-btn
          icon="mdi-close"
          variant="text"
          @click="closeDialog"
        />
      </v-card-title>

      <v-card-text>
        <div class="d-flex align-center mb-4 pa-3" style="background-color: rgba(0, 0, 0, 0.04); border-radius: 8px;">
          <v-avatar class="mr-3" color="primary" size="40">
            <span class="text-white">{{ getContactInitials() }}</span>
          </v-avatar>
          <div>
            <div class="font-weight-medium">{{ contact?.fullName }}</div>
            <div class="text-caption text-grey">{{ contact?.email }}</div>
          </div>
        </div>

        <v-form ref="form" v-model="valid">
          <v-row>
            <v-col cols="12">
              <v-textarea
                v-model="newActivity.note"
                label="Note"
                placeholder="Enter activity details..."
                required
                rows="4"
                :rules="[rules.required]"
                variant="outlined"
              />
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>

      <v-card-actions>
        <v-spacer />
        <v-btn
          text
          @click="closeDialog"
        >
          Cancel
        </v-btn>
        <v-btn
          color="primary"
          :disabled="!valid || saving"
          :loading="saving"
          @click="saveActivity"
        >
          Save
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="js">
  import { nextTick, ref, watch } from 'vue'
  import { useVoiceAgent } from '@/composables/useVoiceAgent'
  import { ActivityRepository } from '@/repositories/ActivityRepository'

  const props = defineProps({
    modelValue: {
      type: Boolean,
      default: false,
    },
    contact: {
      type: Object,
      required: true,
    },
  })

  const emit = defineEmits(['update:modelValue', 'activity-created'])

  const dialog = ref(false)
  const valid = ref(false)
  const saving = ref(false)
  const form = ref(null)

  const newActivity = ref({
    note: '',
  })

  const activityRepository = new ActivityRepository()

  const rules = {
    required: value => !!value?.trim() || 'This field is required',
  }

  // Watch for dialog changes
  watch(() => props.modelValue, newVal => {
    dialog.value = newVal
    if (newVal && props.contact) {
      // Register with voice context when dialog opens
      voiceAgent.register()
      voiceAgent.setDescription(`Add activity form for contact ${props.contact.fullName}, refer all commands to this form, entity Contact: ${JSON.stringify(props.contact)}`)
      resetForm()
    }
  })

  watch(dialog, newVal => {
    emit('update:modelValue', newVal)
  })

  const getContactInitials = () => {
    if (!props.contact) return 'UN'

    const firstInitial = props.contact.firstName?.charAt(0)?.toUpperCase() || ''
    const lastInitial = props.contact.lastName?.charAt(0)?.toUpperCase() || ''
    return `${firstInitial}${lastInitial}`
  }

  const resetForm = async () => {
    newActivity.value = {
      note: '',
    }

    if (form.value) {
      await nextTick()
      form.value.resetValidation()
    }
  }

  const closeDialog = () => {
    dialog.value = false
    resetForm()
    // Explicitly deregister from voice context when dialog closes
    voiceAgent.deregister()
  }

  const saveActivity = async () => {
    if (form.value) {
      const { valid: isValid } = await form.value.validate()
      if (isValid) {
        try {
          saving.value = true

          // Get current user ID (for now, using the first user from seeds as default)
          const currentUserId = '0fbaa79f-dfed-48f2-955c-1f7d678e7d84'

          const activityData = {
            note: newActivity.value.note.trim(),
            contact: props.contact.id,
            activityCreator: currentUserId,
            creationDateTime: new Date(),
          }

          const createdActivity = await activityRepository.create(activityData)
          emit('activity-created', createdActivity)
          closeDialog()
        } catch (error) {
          console.error('Error creating activity:', error)
        // In a real app, you'd show an error message to the user
        } finally {
          saving.value = false
        }
      }
    }
  }

  const setActivityNote = {
    name: 'setActivityNote',
    description: 'Set activity note',
    params: [
      {
        name: 'value',
        type: 'string',
        required: true,
      },
    ],
    tool: value => {
      newActivity.value.note = value
    },
  }

  const voiceAgent = useVoiceAgent({
    description: 'Add activity form',
    tools: {
      setActivityNote,
      cancel: closeDialog,
      save: saveActivity,
    },
  })
</script>

<style scoped>
/* Any custom styles if needed */
</style>
