<template>
  <v-card class="mt-4">
    <v-card-actions>
      <v-spacer />
      <v-btn
        color="primary"
        @click="showCreateDialog = true"
      >
        ADD ACTIVITY
      </v-btn>
    </v-card-actions>

    <v-card-text>
      <div v-if="loading" class="text-center pa-4">
        <v-progress-circular color="primary" indeterminate />
      </div>

      <div v-else-if="activities.length === 0" class="text-center pa-8">
        <v-icon class="mb-4 text-grey-lighten-1" size="64">
          mdi-note-text-outline
        </v-icon>
        <div class="text-h6 mb-2">No activities available</div>
        <div class="text-body-2 text-grey">
          Add some activities to track interactions with this contact.
        </div>
      </div>

      <div v-else>
        <div
          v-for="activity in activities"
          :key="activity.id"
          class="activity-item"
        >
          <div class="d-flex align-start mb-4">
            <v-avatar class="mr-3" color="primary" size="40">
              <span class="text-white">{{ getUserInitials(activity.activityCreator) }}</span>
            </v-avatar>
            <div class="flex-grow-1">
              <div class="d-flex justify-space-between align-center mb-1">
                <span class="font-weight-medium">{{ getUserName(activity.activityCreator) }}</span>
                <span class="text-caption text-grey">{{ activity.formattedDateTime }}</span>
              </div>
              <div class="text-body-2">{{ activity.note }}</div>
            </div>
          </div>
        </div>
      </div>
    </v-card-text>

    <ActivityCreateDialog
      v-model="showCreateDialog"
      :contact="contact"
      @activity-created="onActivityCreated"
    />
  </v-card>
</template>

<script setup lang="js">
  import { onMounted, ref, watch } from 'vue'
  import { useVoiceAgent } from '@/composables/useVoiceAgent'
  import { ActivityRepository } from '@/repositories/ActivityRepository'
  import { UserRepository } from '@/repositories/UserRepository'
  import ActivityCreateDialog from './ActivityCreateDialog.vue'

  const props = defineProps({
    contactId: {
      type: String,
      required: true,
    },
    contact: {
      type: Object,
      required: true,
    },
  })

  const activities = ref([])
  const users = ref(new Map())
  const loading = ref(true)
  const showCreateDialog = ref(false)

  const activityRepository = new ActivityRepository()
  const userRepository = new UserRepository()

  const loadActivities = async () => {
    try {
      loading.value = true
      await activityRepository.seedData()
      activities.value = await activityRepository.getByContactId(props.contactId)
    } catch (error) {
      console.error('Error loading activities:', error)
    } finally {
      loading.value = false
    }
  }

  const loadUsers = async () => {
    try {
      const allUsers = await userRepository.getAll()
      users.value = new Map(allUsers.map(user => [user.id, user]))
    } catch (error) {
      console.error('Error loading users:', error)
    }
  }

  const getUserName = userId => {
    const user = users.value.get(userId)
    return user ? user.fullName : 'Unknown User'
  }

  const getUserInitials = userId => {
    const user = users.value.get(userId)
    if (!user) return 'UN'

    const firstInitial = user.firstName?.charAt(0)?.toUpperCase() || ''
    const lastInitial = user.lastName?.charAt(0)?.toUpperCase() || ''
    return `${firstInitial}${lastInitial}`
  }

  const onActivityCreated = newActivity => {
    activities.value.unshift(newActivity)
    showCreateDialog.value = false
  }

  const showAddActivity = () => {
    showCreateDialog.value = true
  }

  useVoiceAgent({
    description: 'Contact activity list',
    tools: {
      showAddActivity,
    },
  })

  onMounted(async () => {
    await Promise.all([loadUsers(), loadActivities()])
  })

  watch(() => props.contactId, () => {
    if (props.contactId) {
      loadActivities()
    }
  })
</script>

<style scoped>
.activity-item:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  padding-bottom: 16px;
  margin-bottom: 16px;
}

.activity-item:last-child {
  margin-bottom: 0;
}

.cursor-pointer {
  cursor: pointer;
}
</style>
