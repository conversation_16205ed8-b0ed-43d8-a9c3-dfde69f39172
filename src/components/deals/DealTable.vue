<template>
  <v-data-table-server
    class="elevation-1"
    :headers="headers"
    item-value="id"
    :items="items"
    :items-length="itemsTotal"
    :items-per-page="perPage"
    :loading="loading"
    :page="currentPage"
    :search="searchQuery"
    show-current-page
    :sort-by="sortBy"
    @click:row="handleRowClick"
    @update:items-per-page="setPerPage"
    @update:page="setCurrentPage"
    @update:sort-by="setSortBy"
  >
    <template #top>
      <div v-if="items.length === 0 && !loading && searchQuery">
        <v-alert
          color="info"
          icon="mdi-information"
          text
        >
          No deals found matching "{{ searchQuery }}".
          <v-btn
            color="primary"
            size="small"
            variant="text"
            @click="$emit('clear-search')"
          >
            Clear search
          </v-btn>
        </v-alert>
      </div>
    </template>

    <template #item.title="{ item }">
      <div class="d-flex align-center">
        <strong>{{ item.title }}</strong>
      </div>
    </template>

    <template #item.status="{ item }">
      <v-chip
        class="text-capitalize"
        :color="item.statusColor"
        size="small"
        variant="flat"
      >
        {{ item.status }}
      </v-chip>
    </template>

    <template #item.priority="{ item }">
      <div class="d-flex align-center">
        <v-icon
          :color="getPriorityIconColor(item.priority)"
          size="18"
        >
          {{ getPriorityIcon(item.priority) }}
        </v-icon>
        <span class="ml-2 text-body-2" :class="getPriorityTextClass(item.priority)">
          {{ getPriorityLabel(item.priority) }}
        </span>
      </div>
    </template>

    <template #item.budget="{ item }">
      <span class="font-weight-medium">{{ item.formattedBudget }}</span>
    </template>

    <template #item.company="{ item }">
      <div
        v-if="companies.get(item.company)"
        class="d-flex align-center cursor-pointer"
        @click.stop="navigateToCompany(item.company)"
      >
        <v-avatar
          class="mr-3"
          size="32"
        >
          <v-img
            :alt="companies.get(item.company).name"
            :src="companies.get(item.company).logo"
          >
            <template #error>
              <v-icon class="mt-1">mdi-domain</v-icon>
            </template>
          </v-img>
        </v-avatar>
        <div>
          <div class="font-weight-medium">{{ companies.get(item.company).name }}</div>
        </div>
      </div>
      <span v-else class="text-grey">Company not found</span>
    </template>

    <template #item.dealOwner="{ item }">
      <div
        v-if="users.get(item.dealOwner)"
        class="d-flex align-center"
      >
        <v-avatar
          size="32"
        >
          <v-img
            :src="users.get(item.dealOwner).avatar"
          />
        </v-avatar>
        <span class="ml-3">{{ users.get(item.dealOwner).fullName }}</span>
      </div>
      <span v-else class="text-grey">User not found</span>
    </template>

    <template #item.createdAt="{ item }">
      <span class="text-body-2">{{ item.formattedCreatedAt }}</span>
    </template>

    <template #no-data>
      <div class="text-center pa-4">
        <v-icon
          color="grey"
          size="48"
        >
          mdi-handshake
        </v-icon>
        <p class="text-grey mt-2">
          No deals available
        </p>
      </div>
    </template>
  </v-data-table-server>
</template>

<script setup>
  import { onMounted, ref, watch } from 'vue'
  import { useRouter } from 'vue-router'
  import { usePagination } from '@/composables/usePagination'
  import { CompanyRepository } from '@/repositories/CompanyRepository'
  import { DealRepository } from '@/repositories/DealRepository'
  import { UserRepository } from '@/repositories/UserRepository'

  const props = defineProps({
    searchQuery: {
      type: String,
      default: '',
    },
    searchLoading: {
      type: Boolean,
      default: false,
    },
    filters: {
      type: Object,
      default: () => ({}),
    },
  })

  const emit = defineEmits(['clear-search'])

  const router = useRouter()
  const dealRepository = new DealRepository()
  const companyRepository = new CompanyRepository()
  const userRepository = new UserRepository()

  const {
    items,
    itemsTotal,
    loading,
    loadingPromise,
    currentPage,
    lastPage,
    perPage,
    sortBy,
    loadItems,
    setCurrentPage,
    setPerPage,
    nextPage,
    previousPage,
    setSortBy,
    setSearchQuery,
    setFilters,
  } = usePagination(dealRepository, 'createdAt', 'desc')

  const companies = ref(new Map())
  const users = ref(new Map())

  const headers = [
    { title: 'Deal', key: 'title', sortable: true },
    { title: 'Status', key: 'status', sortable: true },
    { title: 'Priority', key: 'priority', sortable: true },
    { title: 'Budget', key: 'budget', sortable: true },
    { title: 'Company', key: 'company', sortable: false },
    { title: 'Deal Owner', key: 'dealOwner', sortable: false },
    { title: 'Date', key: 'createdAt', sortable: true },
  ]

  const loadCompanies = async () => {
    try {
      await companyRepository.seedData()
      const companiesList = await companyRepository.getAll()
      const companiesMap = new Map()
      for (const company of companiesList) {
        companiesMap.set(company.id, company)
      }
      dealRepository.setCompanies(companiesMap)
      companies.value = companiesMap
    } catch (error) {
      console.error('Error loading companies:', error)
    }
  }

  const loadUsers = async () => {
    try {
      await userRepository.seedData()
      const usersList = await userRepository.getAll()
      const usersMap = new Map()
      for (const user of usersList) {
        usersMap.set(user.id, user)
      }
      dealRepository.setUsers(usersMap)
      users.value = usersMap
    } catch (error) {
      console.error('Error loading users:', error)
    }
  }

  const getInitials = name => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getPriorityIcon = priority => {
    switch (priority) {
      case 'low': { return 'mdi-chevron-down'
      }
      case 'medium': { return 'mdi-drag-horizontal'
      }
      case 'high': { return 'mdi-chevron-up'
      }
      default: { return 'mdi-drag-horizontal'
      }
    }
  }

  const getPriorityIconColor = priority => {
    switch (priority) {
      case 'low': { return 'blue'
      }
      case 'medium': { return 'orange'
      }
      case 'high': { return 'red'
      }
      default: { return 'grey'
      }
    }
  }

  const getPriorityLabel = priority => {
    switch (priority) {
      case 'low': { return 'Low'
      }
      case 'medium': { return 'Medium'
      }
      case 'high': { return 'High'
      }
      default: { return priority
      }
    }
  }

  const getPriorityTextClass = priority => {
    switch (priority) {
      case 'low': { return 'text-blue'
      }
      case 'medium': { return 'text-orange'
      }
      case 'high': { return 'text-red'
      }
      default: { return 'text-grey'
      }
    }
  }

  const handleRowClick = (_, { item }) => {
    viewDeal(item)
  }

  const viewDeal = deal => {
    router.push(`/deal-details?id=${deal.id}`)
  }

  const editDeal = deal => {
    router.push(`/deal-edit?id=${deal.id}`)
  }

  const navigateToCompany = companyId => {
    router.push(`/company-details?id=${companyId}`)
  }

  // Methods for voice agent integration
  const getFirstDeal = () => {
    return items.value.length > 0 ? items.value[0] : null
  }

  // Watch for search query changes
  watch(() => props.searchQuery, async newQuery => {
    setSearchQuery(newQuery)
  })

  // Watch for filter changes
  watch(() => props.filters, async newFilters => {
    setFilters(newFilters)
  }, { deep: true })

  // Expose methods for parent component
  defineExpose({
    loadingPromise,
    headers,
    items,
    currentPage,
    lastPage,
    users,
    companies,
    searchUsers: async query => userRepository.search(query),
    getFirstRecord: getFirstDeal,
    view: viewDeal,
    edit: editDeal,
    setCurrentPage,
    nextPage,
    previousPage,
    setSortBy,
    setPerPage,
    setFilters,
    showCompany: navigateToCompany,
  })

  onMounted(async () => {
    await Promise.all([
      loadItems(),
      loadCompanies(),
      loadUsers(),
    ])
  })
</script>

<style scoped>
  .cursor-pointer {
    cursor: pointer;
  }

  .cursor-pointer:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
</style>
