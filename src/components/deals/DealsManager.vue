<template>
  <div>
    <v-row align="center" justify="space-between">
      <v-col cols="auto">
        <h1
          class="text-primary"
          style="cursor: pointer;"
          @click="refreshData"
        >
          Deals
        </h1>
      </v-col>
      <v-col cols="auto">
        <v-text-field
          v-model="searchQuery"
          :append-inner-icon="searchQuery ? 'mdi-close' : undefined"
          density="compact"
          hide-details
          :loading="searchLoading"
          placeholder="Search deals..."
          prepend-inner-icon="mdi-magnify"
          style="min-width: 400px;"
          variant="outlined"
          @click:append-inner="clearSearch"
        />
      </v-col>
    </v-row>

    <!-- Filters Section -->
    <v-card
      class="mb-4"
      elevation="1"
    >
      <v-card-text>
        <v-row>
          <v-col cols="12" md="3">
            <v-select
              v-model="filters.status"
              clearable
              density="compact"
              :items="statusOptions"
              label="Status"
              placeholder="Select status"
              variant="outlined"
            />
          </v-col>
          <v-col cols="12" md="3">
            <v-select
              v-model="filters.priority"
              clearable
              density="compact"
              :items="priorityOptions"
              label="Priority"
              placeholder="Select priority"
              variant="outlined"
            >
              <template #item="{ props, item }">
                <v-list-item v-bind="props">
                  <template #prepend>
                    <v-icon :color="getPriorityColor(item.value)">
                      {{ getPriorityIcon(item.value) }}
                    </v-icon>
                  </template>
                </v-list-item>
              </template>
            </v-select>
          </v-col>
          <v-col cols="12" md="2">
            <v-text-field
              v-model="filters.budgetFrom"
              density="compact"
              label="Budget From"
              placeholder="0"
              prefix="$"
              type="number"
              variant="outlined"
            />
          </v-col>
          <v-col cols="12" md="2">
            <v-text-field
              v-model="filters.budgetTo"
              density="compact"
              label="Budget To"
              placeholder="100000"
              prefix="$"
              type="number"
              variant="outlined"
            />
          </v-col>
          <v-col cols="12" md="2">
            <v-select
              v-model="filters.dealOwner"
              clearable
              density="compact"
              :items="dealOwnerOptions"
              label="Deal Owner"
              placeholder="Select owner"
              variant="outlined"
            >
              <template #item="{ props, item }">
                <v-list-item v-bind="props">
                  <template #prepend>
                    <v-avatar color="secondary" size="24">
                      <span class="text-white text-caption font-weight-bold">
                        {{ getInitials(item.title) }}
                      </span>
                    </v-avatar>
                  </template>
                </v-list-item>
              </template>
            </v-select>
          </v-col>
        </v-row>
        <v-row v-if="activeFiltersCount > 0">
          <v-col cols="12">
            <div class="d-flex align-center gap-2">
              <span class="text-caption text-grey">Active filters:</span>
              <v-chip
                v-if="filters.status"
                class="text-capitalize"
                closable
                color="primary"
                size="small"
                variant="outlined"
                @click:close="filters.status = null"
              >
                Status: {{ filters.status }}
              </v-chip>
              <v-chip
                v-if="filters.priority"
                class="text-capitalize"
                closable
                color="primary"
                size="small"
                variant="outlined"
                @click:close="filters.priority = null"
              >
                Priority: {{ filters.priority }}
              </v-chip>
              <v-chip
                v-if="filters.budgetFrom || filters.budgetTo"
                closable
                color="primary"
                size="small"
                variant="outlined"
                @click:close="clearBudgetFilter"
              >
                Budget: ${{ filters.budgetFrom || '0' }} - ${{ filters.budgetTo || '∞' }}
              </v-chip>
              <v-chip
                v-if="filters.dealOwner"
                closable
                color="primary"
                size="small"
                variant="outlined"
                @click:close="filters.dealOwner = null"
              >
                Owner: {{ getDealOwnerName(filters.dealOwner) }}
              </v-chip>
              <v-btn
                color="error"
                size="small"
                variant="text"
                @click="clearAllFilters"
              >
                Clear All
              </v-btn>
            </div>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <v-row>
      <v-col>
        <DealTable
          ref="dealTable"
          :filters="filters"
          :search-loading="searchLoading"
          :search-query="searchQuery"
          @clear-search="clearSearch"
        />
      </v-col>
    </v-row>
  </div>
</template>

<script setup>
  import { computed, ref, watch } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useDealSearchTools } from '@/composables/useDealSearchTools'
  import { useVoiceAgent } from '@/composables/useVoiceAgent'

  const router = useRouter()
  const route = useRoute()
  const dealTable = ref(null)
  const searchQuery = ref('')
  const searchLoading = ref(false)
  let searchTimeout = null

  // Filter state
  const filters = ref({
    status: null,
    priority: null,
    budgetFrom: null,
    budgetTo: null,
    dealOwner: null,
  })

  // Filter options
  const statusOptions = [
    { title: 'Identification', value: 'identification' },
    { title: 'Proposal', value: 'proposal' },
    { title: 'Negotiation', value: 'negotiation' },
    { title: 'Closed Won', value: 'closed won' },
    { title: 'Closed Lost', value: 'closed lost' },
  ]

  const priorityOptions = [
    { title: 'Low', value: 'low' },
    { title: 'Medium', value: 'medium' },
    { title: 'High', value: 'high' },
  ]

  const dealOwnerOptions = ref([])

  // Computed properties
  const activeFiltersCount = computed(() => {
    let count = 0
    if (filters.value.status) count++
    if (filters.value.priority) count++
    if (filters.value.budgetFrom || filters.value.budgetTo) count++
    if (filters.value.dealOwner) count++
    return count
  })

  const clearSearch = () => {
    searchQuery.value = ''
  }

  const clearBudgetFilter = () => {
    filters.value.budgetFrom = null
    filters.value.budgetTo = null
  }

  const clearAllFilters = () => {
    filters.value = {
      status: null,
      priority: null,
      budgetFrom: null,
      budgetTo: null,
      dealOwner: null,
    }

    // Treat clearAllFilters as if we asked to clear search as well
    clearSearch()
  }

  const showAllDeals = () => {
    clearAllFilters()
  }

  const getPriorityIcon = priority => {
    switch (priority) {
      case 'low': { return 'mdi-chevron-down'
      }
      case 'medium': { return 'mdi-drag-horizontal'
      }
      case 'high': { return 'mdi-chevron-up'
      }
      default: { return 'mdi-drag-horizontal'
      }
    }
  }

  const getPriorityColor = priority => {
    switch (priority) {
      case 'low': { return 'blue'
      }
      case 'medium': { return 'orange'
      }
      case 'high': { return 'red'
      }
      default: { return 'grey'
      }
    }
  }

  const getInitials = name => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getDealOwnerName = ownerId => {
    const option = dealOwnerOptions.value.find(opt => opt.value === ownerId)
    return option ? option.title : 'Unknown'
  }

  const refreshData = () => {
    // Force refresh by clearing search and reloading
    clearSearch()
    // Emit event for parent to handle refresh if needed
    emit('refresh')
  }

  // Voice agent tools for deal search and navigation
  const tools = useDealSearchTools(dealTable, searchQuery)

  // Voice agent filter tools
  const setStatusFilter = status => {
    console.log('[DealsManager] setStatusFilter called with:', status)
    filters.value.status = status
    return {
      success: true,
      message: status ? `Filtered deals by status: ${status}` : 'Cleared status filter',
    }
  }

  const setPriorityFilter = priority => {
    console.log('[DealsManager] setPriorityFilter called with:', priority)
    filters.value.priority = priority
    return {
      success: true,
      message: priority ? `Filtered deals by priority: ${priority}` : 'Cleared priority filter',
    }
  }

  const setBudgetFilter = (from, to) => {
    console.log('[DealsManager] setBudgetFilter called with:', { from, to })
    filters.value.budgetFrom = from
    filters.value.budgetTo = to
    return {
      success: true,
      message: from || to
        ? `Filtered deals by budget: $${from || '0'} - $${to || '∞'}`
        : 'Cleared budget filter',
    }
  }

  const setDealOwnerFilter = async query => {
    console.log('[DealsManager] setDealOwnerFilter called with:', query)
    const found = await dealTable.value.searchUsers(query)
    if (found.length > 0) {
      filters.value.dealOwner = found[0].id
      return {
        success: true,
        message: `Filtered deals by owner: ${found[0].fullName}`,
      }
    }
    return {
      success: false,
      message: `Failed to filter deals by owner`,
    }
  }

  const showCompanyTool = {
    name: 'showCompanyTool',
    description: 'Show,view,open company by ID',
    params: [
      {
        type: 'string',
        description: 'Company ID',
      },
    ],
    tool: id => {
      dealTable.value.showCompany(id)
    },
  }

  const showUserTool = {
    name: 'showUserTool',
    description: 'Show, view, open, edit user,owner by ID',
    params: [
      {
        type: 'string',
        description: 'User ID',
      },
    ],
    tool: id => {
      router.push('/settings?user_id=' + id)
    },
  }

  // Register with voice agent system
  const voiceAgentDesc = 'Deals list component with search and filtering functionality'
  const voiceAgent = useVoiceAgent({
    description: voiceAgentDesc,
    tools: {
      ...tools,
      clearSearch: refreshData,
      setStatusFilter,
      setPriorityFilter,
      setBudgetFilter,
      setDealOwnerFilter,
      clearAllFilters,
      showAllDeals,
      showCompanyTool,
      showUserTool,
    },
  })

  watch(
    () => ({
      currentPage: dealTable.value?.currentPage,
      lastPage: dealTable.value?.lastPage,
      items: dealTable.value?.items,
      users: dealTable.value?.users,
      companies: dealTable.value?.companies,
    }),
    value => {
      if (!value) {
        return
      }

      // Update deal owner options when users are loaded
      if (value.users && dealOwnerOptions.value.length === 0) {
        dealOwnerOptions.value = Array.from(value.users.values()).map(user => ({
          title: user.fullName,
          value: user.id,
        }))
      }

      const list = value?.items
        ? value.items.map(item => ({
          id: item.id,
          title: item.title,
          companyId: item.company,
          companyName: value.companies.get(item.company)?.name,
          userId: item.dealOwner,
          userName: value.users.get(item.dealOwner)?.fullName,
        }))
        : []
      voiceAgent.setDescription([
        voiceAgentDesc,
        `Current page: ${value.currentPage}`,
        `Last page: ${value.lastPage}`,
        `Current deals list JSON: ${JSON.stringify(list)}`,
        `Active filters: ${JSON.stringify(filters.value)}`,
      ].join('. '))
    },
  )

  // Watch for search query changes and debounce
  watch(searchQuery, newQuery => {
    // Clear existing timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout)
    }

    // Only search if 2+ characters or empty (to clear search)
    if (newQuery.length >= 2 || newQuery.length === 0) {
      searchTimeout = setTimeout(() => {
        updateUrlWithSearch(newQuery)
      }, 300) // 300ms debounce
    }
  })

  // Watch for URL changes to sync search field
  watch(() => route.query.search, newSearch => {
    if (newSearch !== searchQuery.value) {
      searchQuery.value = newSearch || ''
    }
  })

  const updateUrlWithSearch = query => {
    const newQuery = { ...route.query }

    if (query && query.trim()) {
      newQuery.search = query.trim()
    } else {
      delete newQuery.search
    }

    // Update URL without triggering navigation
    router.replace({
      path: route.path,
      query: newQuery,
    })
  }

  // Initialize search from URL query parameter on mount
  const initializeFromUrl = () => {
    if (route.query.search) {
      searchQuery.value = route.query.search
    }
  }

  // Expose initialization function for parent
  defineExpose({
    initializeFromUrl,
    clearSearch,
    refreshData,
  })

  // Define emits for parent communication
  const emit = defineEmits(['refresh'])
</script>
