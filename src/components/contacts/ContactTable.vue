<template>
  <v-data-table-server
    class="elevation-1"
    :headers="headers"
    :items="items"
    :items-length="itemsTotal"
    :items-per-page="perPage"
    :loading="loading || searchLoading"
    :page="currentPage"
    :sort-by="sortBy"
    @update:items-per-page="setPerPage"
    @update:page="setCurrentPage"
    @update:sort-by="setSortBy"
  >
    <template #item.fullName="{ item }">
      <div class="d-flex align-center" style="cursor: pointer;" @click="viewContact(item)">
        <v-avatar class="mr-3" color="primary" size="36">
          <span class="text-white">{{ getInitials(item) }}</span>
        </v-avatar>
        <strong>{{ item.fullName }}</strong>
      </div>
    </template>

    <template #item.formattedPhone="{ item }">
      {{ item.formattedPhone }}
    </template>

    <template #item.company="{ item }">
      <div
        v-if="companies.get(item.company)"
        class="d-flex align-center cursor-pointer"
        @click.stop="navigateToCompany(item.company)"
      >
        <v-avatar
          class="mr-3"
          size="32"
        >
          <v-img
            :alt="companies.get(item.company).name"
            :src="companies.get(item.company).logo"
          >
            <template #error>
              <v-icon class="mt-1">mdi-domain</v-icon>
            </template>
          </v-img>
        </v-avatar>
        <div>
          <div class="font-weight-medium">{{ companies.get(item.company).name }}</div>
        </div>
      </div>
      <span v-else class="text-grey">Company not found</span>
    </template>

    <template #no-data>
      <div class="text-center pa-4">
        <v-icon class="mb-4 text-grey-lighten-1" size="64">
          {{ searchQuery ? 'mdi-magnify-remove-outline' : 'mdi-account-off' }}
        </v-icon>
        <div class="text-h6 mb-2">
          {{ searchQuery ? 'No contacts found' : 'No contacts available' }}
        </div>
        <div class="text-body-2 text-grey">
          {{ searchQuery
            ? `Try adjusting your search terms.`
            : 'Add some contacts to get started.'
          }}
        </div>
        <v-btn
          v-if="searchQuery"
          class="mt-2"
          variant="text"
          @click="clearSearch"
        >
          Clear Search
        </v-btn>
      </div>
    </template>
  </v-data-table-server>
</template>

<script setup>
  import { onMounted, ref, watch } from 'vue'
  import { useRouter } from 'vue-router'
  import { usePagination } from '@/composables/usePagination'
  import { CompanyRepository } from '@/repositories/CompanyRepository'
  import { ContactRepository } from '@/repositories/ContactRepository'

  const props = defineProps({
    searchQuery: {
      type: String,
      default: '',
    },
    searchLoading: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['clear-search'])

  const router = useRouter()
  const companies = ref(new Map())
  const contactRepository = new ContactRepository()
  const companyRepository = new CompanyRepository()

  const {
    items,
    itemsTotal,
    loading,
    loadingPromise,
    currentPage,
    lastPage,
    perPage,
    sortBy,
    loadItems,
    setCurrentPage,
    setPerPage,
    nextPage,
    previousPage,
    setSortBy,
    setSearchQuery,
  } = usePagination(contactRepository)

  const headers = [
    {
      title: 'Name',
      align: 'start',
      sortable: true,
      key: 'fullName',
    },
    {
      title: 'Email',
      align: 'start',
      sortable: true,
      key: 'email',
    },
    {
      title: 'Phone',
      align: 'start',
      sortable: false,
      key: 'formattedPhone',
    },
    {
      title: 'Company',
      align: 'start',
      sortable: true,
      key: 'company',
    },
  ]

  const loadCompanies = async () => {
    try {
      await companyRepository.seedData()
      const companiesList = await companyRepository.getAll()
      const companiesMap = new Map()
      for (const company of companiesList) {
        companiesMap.set(company.id, company)
      }
      contactRepository.setCompanies(companiesMap)
      companies.value = companiesMap
    } catch (error) {
      console.error('Error loading companies:', error)
    }
  }

  const getInitials = contact => {
    if (!contact.firstName && !contact.lastName) return '?'
    const firstInitial = contact.firstName ? contact.firstName[0].toUpperCase() : ''
    const lastInitial = contact.lastName ? contact.lastName[0].toUpperCase() : ''
    return firstInitial + lastInitial
  }

  const navigateToCompany = companyId => {
    router.push(`/company-details?id=${companyId}`)
  }

  // Watch for search query changes
  watch(() => props.searchQuery, async newQuery => {
    setSearchQuery(newQuery)
  })

  const clearSearch = () => {
    emit('clear-search')
  }

  const viewContact = contact => {
    console.log('Clicking on contact:', contact.fullName, 'ID:', contact.id)
    const targetUrl = `/contact-details?id=${contact.id}`
    console.log('Navigating to:', targetUrl)
    router.push(targetUrl)
  }

  const editContact = contact => {
    console.log('Clicking on contact:', contact.fullName, 'ID:', contact.id)
    const targetUrl = `/contact-edit?id=${contact.id}`
    console.log('Navigating to:', targetUrl)
    router.push(targetUrl)
  }

  onMounted(async () => {
    await Promise.all([
      loadItems(),
      loadCompanies(),
    ])
  })

  // Expose functions for parent components (voice agent tools)
  defineExpose({
    loadingPromise,
    headers,
    items,
    currentPage,
    lastPage,
    perPage,
    view: viewContact,
    edit: editContact,
    nextPage,
    previousPage,
    setCurrentPage,
    setSortBy,
    setPerPage,
    getFirstRecord: () => items.value[0] || null,
  })
</script>

<style scoped>
  .cursor-pointer {
    cursor: pointer;
  }

  .cursor-pointer:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }
</style>
