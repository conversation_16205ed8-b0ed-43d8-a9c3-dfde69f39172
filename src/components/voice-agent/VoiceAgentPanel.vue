<template>
  <div class="voice-agent-panel">
    <!-- Not Started State -->
    <div v-if="!isListening" class="panel-state panel-state--not-started">
      <div class="upper-section">
        <div class="not-started-content">
          <h2 class="panel-title">Push and Talk</h2>
          <p class="panel-subtitle">To unlock AI's full potential</p>

          <v-btn
            class="start-button"
            :class="buttonClasses"
            :disabled="!isAvailable"
            :icon="true"
            size="x-large"
            variant="flat"
            @click="startListening"
          >
            <v-icon size="32">mdi-microphone</v-icon>
          </v-btn>
        </div>
      </div>

      <div class="lower-section">
        <div class="examples-section">
          <h3 class="examples-title">Voice command examples:</h3>
          <ul class="examples-list">
            <li class="example-item">"Show me all high priority deals"</li>
            <li class="example-item">"Can you open <PERSON> profile?"</li>
            <li class="example-item">"Please add a note that meeting was postponed to <PERSON> profile"</li>
            <li class="example-item">"What are our recent activities?"</li>
            <li class="example-item">"Can you show me statistics for deals we won last month?"</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Listening State -->
    <div v-else class="panel-state panel-state--listening">
      <!-- Control Status Section -->
      <div class="control-status">
        <div class="status-content">
          <v-icon
            :class="microphoneIconClasses"
            color="primary"
            size="20"
          >
            mdi-microphone
          </v-icon>
          <span class="status-text">
            <span v-if="serviceState === 'listening'">
              Listening<span class="animated-dots" />
            </span>
            <span v-else>{{ statusText }}</span>
          </span>
        </div>

        <div class="control-actions">
          <v-btn
            v-if="hasSpokenBefore"
            color="grey-darken-1"
            density="comfortable"
            icon="mdi-replay"
            size="small"
            title="Replay last command"
            variant="flat"
            @click="replayLastCommand"
          >
            <v-icon>mdi-replay</v-icon>
          </v-btn>

          <v-btn
            color="grey-darken-1"
            density="comfortable"
            icon="mdi-stop"
            size="small"
            title="Stop listening"
            variant="flat"
            @click="stopListening"
          >
            <v-icon>mdi-stop</v-icon>
          </v-btn>
        </div>
      </div>

      <!-- Conversation Section -->
      <div class="conversation-section">
        <div class="conversation-content">
          <!-- Empty State Messages (when no transcription yet) -->
          <div v-if="!currentInteraction.userRequest && serviceState !== 'processing'" class="empty-state">
            <!-- Initializing State -->
            <div v-if="serviceState === 'initializing'" class="empty-state-message">
              <v-icon class="empty-state-icon loading-icon" color="primary" size="48">
                mdi-cog
              </v-icon>
              <h3 class="empty-state-title">Setting up microphone...</h3>
              <p class="empty-state-subtitle">Please wait while we initialize</p>
            </div>

            <!-- Listening State -->
            <div v-else-if="serviceState === 'listening'" class="empty-state-message">
              <v-icon class="empty-state-icon pulsing-icon" color="primary" size="48">
                mdi-microphone
              </v-icon>
              <h3 class="empty-state-title">I'm listening...</h3>
              <p class="empty-state-subtitle">Please speak your request</p>
            </div>

            <!-- Connecting State -->
            <div v-else-if="serviceState === 'connecting'" class="empty-state-message">
              <v-icon class="empty-state-icon loading-icon" color="warning" size="48">
                mdi-microphone-outline
              </v-icon>
              <h3 class="empty-state-title">Connecting...</h3>
              <p class="empty-state-subtitle">Setting up voice connection</p>
            </div>
          </div>

          <!-- Current/Last Interaction -->
          <div v-if="currentInteraction" class="interaction">
            <!-- User Transcription -->
            <div v-if="currentInteraction.userRequest" class="message user-message">
              <div class="message-label">User:</div>
              <div class="message-text">{{ currentInteraction.userRequest }}</div>
            </div>

            <!-- Agent Response -->
            <div v-if="currentInteraction.agentResponse" class="message agent-message">
              <div class="message-label">Agent:</div>
              <div class="message-text">{{ currentInteraction.agentResponse }}</div>
            </div>

            <!-- Execution Plan -->
            <div v-if="currentInteraction.executionPlan && currentInteraction.executionPlan.length > 0" class="message plan-message">
              <div class="message-label">Execution Plan:</div>
              <ul class="execution-plan-list">
                <li v-for="(step, index) in currentInteraction.executionPlan" :key="index" class="plan-step">
                  {{ formatExecutionStep(step) }}
                </li>
              </ul>
            </div>
          </div>

          <!-- Processing States -->
          <div v-if="serviceState === 'processing'" class="processing-indicator">
            <v-icon class="processing-icon" color="info">mdi-brain</v-icon>
            <span>...</span>
          </div>

          <!-- Error State -->
          <div v-if="serviceState === 'error' && lastError" class="error-message">
            <v-icon color="error">mdi-alert-circle</v-icon>
            <span>{{ lastError.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { computed, onMounted, onUnmounted, ref } from 'vue'
  import { useRoute } from 'vue-router'
  import { voiceAgentCoordinator } from '@/services/voice-agent/VoiceAgentCoordinator'

  // Vue router for current route context
  const route = useRoute()

  // Reactive state
  const coordinatorState = ref('idle')
  const isAvailable = ref(false)
  const lastError = ref(null)
  const currentInteraction = ref({
    userRequest: '',
    agentResponse: '',
    executionPlan: [],
  })

  // Computed properties
  const serviceState = computed(() => coordinatorState.value)

  const isListening = computed(() => {
    return serviceState.value === 'initializing' || serviceState.value === 'listening' || serviceState.value === 'processing' || serviceState.value === 'error'
  })

  const statusText = computed(() => {
    switch (serviceState.value) {
      case 'initializing': {
        return 'Initializing...'
      }
      case 'listening': {
        return 'Listening...'
      }
      case 'processing': {
        return 'Processing...'
      }
      default: {
        return 'Ready'
      }
    }
  })

  const buttonClasses = computed(() => {
    return {
      'pulse-animation': isAvailable.value,
    }
  })

  const microphoneIconClasses = computed(() => {
    return {
      'microphone-icon': true,
      'microphone-icon--listening': serviceState.value === 'listening',
      'microphone-icon--processing': serviceState.value === 'processing',
    }
  })

  const hasSpokenBefore = computed(() => {
    return !!(currentInteraction.value && currentInteraction.value.userRequest)
  })

  // Helper function to format execution steps
  const formatExecutionStep = step => {
    if (step.action === 'navigate') {
      return `Navigate to ${step.payload?.path || 'page'}`
    } else if (step.action === 'useTool') {
      const component = step.payload?.targetComponent?.name || 'component'
      const tool = step.payload?.tool || 'action'
      const params = step.payload?.params || []

      return params.length > 0 ? `${tool} on ${component} with: ${params.join(', ')}` : `${tool} on ${component}`
    }
    return `Execute ${step.action}`
  }

  // Event handlers
  const startListening = async () => {
    if (!isAvailable.value) {
      console.warn('[VoiceAgentPanel] Service not available')
      return
    }

    try {
      await voiceAgentCoordinator.startListening(route.path)
    } catch (error) {
      console.error('[VoiceAgentPanel] Error starting listening:', error)
    }
  }

  const stopListening = async () => {
    try {
      await voiceAgentCoordinator.stopListening()
      console.log('[VoiceAgentPanel] Voice agent stopped and cleaned up')
    } catch (error) {
      console.error('[VoiceAgentPanel] Error stopping listening:', error)
    }
  }

  const replayLastCommand = async () => {
    if (!hasSpokenBefore.value) {
      console.warn('[VoiceAgentPanel] No previous command to replay')
      return
    }

    try {
      await voiceAgentCoordinator.replayLastUserRequest()
      console.log('[VoiceAgentPanel] Replaying last command:', currentInteraction.value.userRequest)
    } catch (error) {
      console.error('[VoiceAgentPanel] Error replaying last command:', error)
    }
  }

  // Coordinator event handlers
  const handleStateChange = data => {
    coordinatorState.value = data.newState
    console.log('[VoiceAgentPanel] Coordinator state changed to:', data.newState)
  }

  const handleInteractionUpdate = data => {
    currentInteraction.value = { ...data }
    console.log('[VoiceAgentPanel] Interaction updated:', data)
  }

  const handleError = error => {
    lastError.value = error
    console.error('[VoiceAgentPanel] Coordinator error:', error)
  }

  // Lifecycle hooks
  onMounted(() => {
    // Initialize state from coordinator
    coordinatorState.value = voiceAgentCoordinator.getState()
    isAvailable.value = voiceAgentCoordinator.isAvailable()
    currentInteraction.value = voiceAgentCoordinator.getCurrentInteraction()

    // Set up event listeners for coordinator
    voiceAgentCoordinator.addEventListener('stateChange', handleStateChange)
    voiceAgentCoordinator.addEventListener('interactionUpdate', handleInteractionUpdate)
    voiceAgentCoordinator.addEventListener('error', handleError)

    console.log('[VoiceAgentPanel] Component mounted with coordinator')
  })

  onUnmounted(() => {
    // Clean up event listeners
    voiceAgentCoordinator.removeEventListener('stateChange', handleStateChange)
    voiceAgentCoordinator.removeEventListener('interactionUpdate', handleInteractionUpdate)
    voiceAgentCoordinator.removeEventListener('error', handleError)

    console.log('[VoiceAgentPanel] Component unmounted')
  })
</script>

<style src="@/styles/voice-agent-panel.scss" scoped></style>
