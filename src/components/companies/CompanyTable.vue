<template>
  <v-data-table-server
    class="elevation-1"
    :headers="headers"
    :items="items"
    :items-length="itemsTotal"
    :items-per-page="perPage"
    :loading="loading || searchLoading"
    :page="currentPage"
    :sort-by="sortBy"
    @update:items-per-page="setPerPage"
    @update:page="setCurrentPage"
    @update:sort-by="setSortBy"
  >
    <template #item.name="{ item }">
      <div class="d-flex align-center" style="cursor: pointer;" @click="viewCompany(item)">
        <v-avatar class="mr-3" size="36">
          <v-img
            :alt="item.name"
            :src="item.logo"
          >
            <template #error>
              <v-icon class="mt-2">mdi-domain</v-icon>
            </template>
          </v-img>
        </v-avatar>
        <strong>{{ item.name }}</strong>
      </div>
    </template>

    <template #item.formattedPhone="{ item }">
      {{ item.formattedPhone }}
    </template>

    <template #no-data>
      <div class="text-center pa-4">
        <v-icon class="mb-4 text-grey-lighten-1" size="64">
          {{ searchQuery ? 'mdi-magnify-remove-outline' : 'mdi-domain-off' }}
        </v-icon>
        <div class="text-h6 mb-2">
          {{ searchQuery ? 'No companies found' : 'No companies available' }}
        </div>
        <div class="text-body-2 text-grey">
          {{ searchQuery
            ? `Try adjusting your search terms.`
            : 'Add some companies to get started.'
          }}
        </div>
        <v-btn
          v-if="searchQuery"
          class="mt-2"
          variant="text"
          @click="clearSearch"
        >
          Clear Search
        </v-btn>
      </div>
    </template>
  </v-data-table-server>
</template>

<script setup>
  import { onMounted, ref, watch } from 'vue'
  import { useRouter } from 'vue-router'
  import { usePagination } from '@/composables/usePagination'
  import { CompanyRepository } from '@/repositories/CompanyRepository'

  const props = defineProps({
    searchQuery: {
      type: String,
      default: '',
    },
    searchLoading: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits(['clear-search'])

  const companyRepository = new CompanyRepository()

  const router = useRouter()
  const {
    items,
    itemsTotal,
    loading,
    loadingPromise,
    currentPage,
    lastPage,
    perPage,
    sortBy,
    loadItems,
    setCurrentPage,
    setPerPage,
    nextPage,
    previousPage,
    setSortBy,
    setSearchQuery,
  } = usePagination(companyRepository)

  const headers = [
    {
      title: 'Name',
      align: 'start',
      sortable: true,
      key: 'name',
    },
    {
      title: 'Phone',
      align: 'start',
      sortable: false,
      key: 'formattedPhone',
    },
    {
      title: 'Email',
      align: 'start',
      sortable: true,
      key: 'email',
    },
    {
      title: 'Location',
      align: 'start',
      sortable: true,
      key: 'location',
    },
    {
      title: 'Industry',
      align: 'start',
      sortable: true,
      key: 'industry',
    },
  ]

  // Watch for search query changes
  watch(() => props.searchQuery, async newQuery => {
    setSearchQuery(newQuery)
  })

  const clearSearch = () => {
    emit('clear-search')
  }

  const viewCompany = company => {
    console.log('Clicking on company:', company.name, 'ID:', company.id)
    const targetUrl = `/company-details?id=${company.id}`
    console.log('Navigating to:', targetUrl)
    router.push(targetUrl)
  }

  const editCompany = company => {
    console.log('Clicking on company:', company.name, 'ID:', company.id)
    const targetUrl = `/company-edit?id=${company.id}`
    console.log('Navigating to:', targetUrl)
    router.push(targetUrl)
  }

  onMounted(() => {
    loadItems()
  })

  // Expose functions for parent components (voice agent tools)
  defineExpose({
    loadingPromise,
    headers,
    items,
    currentPage,
    lastPage,
    perPage,
    view: viewCompany,
    edit: editCompany,
    getFirstRecord: () => items.value[0] || null,
    nextPage,
    previousPage,
    setCurrentPage,
    setSortBy,
    setPerPage,
  })
</script>
