<template>
  <div>
    <v-row align="center" justify="space-between">
      <v-col cols="auto">
        <h1
          class="text-primary"
          style="cursor: pointer;"
          @click="refreshData"
        >
          Companies
        </h1>
      </v-col>
      <v-col cols="auto">
        <v-text-field
          v-model="searchQuery"
          :append-inner-icon="searchQuery ? 'mdi-close' : undefined"
          density="compact"
          hide-details
          :loading="searchLoading"
          placeholder="Search companies..."
          prepend-inner-icon="mdi-magnify"
          style="min-width: 300px;"
          variant="outlined"
          @click:append-inner="clearSearch"
        />
      </v-col>
    </v-row>

    <v-row>
      <v-col>
        <CompanyTable
          ref="companyTable"
          :search-loading="searchLoading"
          :search-query="searchQuery"
          @clear-search="clearSearch"
        />
      </v-col>
    </v-row>
  </div>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { useCompanySearchTools } from '@/composables/useCompanySearchTools'
  import { useVoiceAgent } from '@/composables/useVoiceAgent'

  const router = useRouter()
  const route = useRoute()
  const companyTable = ref(null)
  const searchQuery = ref('')
  const searchLoading = ref(false)
  let searchTimeout = null

  const clearSearch = () => {
    searchQuery.value = ''
  }

  const refreshData = () => {
    // Force refresh by clearing search and reloading
    clearSearch()
    // Emit event for parent to handle refresh if needed
    emit('refresh')
  }

  // Voice agent tools for company search and navigation
  const tools = useCompanySearchTools(companyTable, searchQuery)
  const voiceAgentDesc = 'Companies list component with search functionality'
  const voiceAgent = useVoiceAgent({
    description: voiceAgentDesc,
    tools: {
      ...tools,
      clearSearch: refreshData,
    },
  })

  watch(
    () => ({
      currentPage: companyTable.value?.currentPage,
      lastPage: companyTable.value?.lastPage,
      items: companyTable.value?.items,
    }),
    value => {
      if (!value) {
        return
      }

      const list = value?.items ? value.items.map(({ id, name }) => ({ id, name })) : []
      voiceAgent.setDescription([
        voiceAgentDesc,
        `Current page: ${value.currentPage}`,
        `Last page: ${value.lastPage}`,
        `Current companies list JSON: ${JSON.stringify(list)}`,
      ].join('. '))
    },
  )

  // Watch for search query changes and debounce
  watch(searchQuery, newQuery => {
    // Clear existing timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout)
    }

    // Only search if 2+ characters or empty (to clear search)
    if (newQuery.length >= 2 || newQuery.length === 0) {
      searchTimeout = setTimeout(() => {
        updateUrlWithSearch(newQuery)
      }, 300) // 300ms debounce
    }
  })

  // Watch for URL changes to sync search field
  watch(() => route.query.search, newSearch => {
    if (newSearch !== searchQuery.value) {
      searchQuery.value = newSearch || ''
    }
  })

  const updateUrlWithSearch = query => {
    const newQuery = { ...route.query }

    if (query && query.trim()) {
      newQuery.search = query.trim()
    } else {
      delete newQuery.search
    }

    // Update URL without triggering navigation
    router.replace({
      path: route.path,
      query: newQuery,
    })
  }

  // Initialize search from URL query parameter on mount
  const initializeFromUrl = () => {
    if (route.query.search) {
      searchQuery.value = route.query.search
    }
  }

  // Expose initialization function for parent
  defineExpose({
    initializeFromUrl,
    clearSearch,
    refreshData,
  })

  // Define emits for parent communication
  const emit = defineEmits(['refresh'])
</script>
