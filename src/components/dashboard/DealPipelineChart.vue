<template>
  <v-card style="height: 270px;">
    <v-card-title>Deal Pipeline</v-card-title>
    <v-card-text class="pb-0" style="height: 230px; overflow-y: auto;">
      <div v-if="loading" class="text-center pa-4">
        <v-progress-circular color="primary" indeterminate />
      </div>
      <div v-else>
        <div
          v-for="stage in pipelineData"
          :key="stage.status"
          class="mb-3"
        >
          <div class="d-flex justify-space-between align-center mb-1">
            <span class="text-subtitle-2">{{ stage.status }}</span>
            <div class="text-right">
              <div class="text-body-2 font-weight-bold">{{ stage.count }} deals</div>
              <div class="text-caption text-grey">
                ${{ stage.value.toLocaleString() }}
              </div>
            </div>
          </div>
          <v-progress-linear
            :color="getStageColor(stage.status)"
            height="8"
            :model-value="getProgressValue(stage.count)"
            rounded
          />
        </div>
      </div>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue'
  import { DashboardService } from '@/services/DashboardService'

  const loading = ref(true)
  const pipelineData = ref([])
  const dashboardService = new DashboardService()

  const maxDeals = computed(() => {
    return Math.max(...pipelineData.value.map(stage => stage.count), 1)
  })

  const getProgressValue = count => {
    return (count / maxDeals.value) * 100
  }

  const getStageColor = status => {
    const colors = {
      'Identification': 'blue-lighten-2',
      'Proposal': 'orange-lighten-2',
      'Negotiation': 'green-lighten-3',
      'Closed won': 'green',
      'Closed lost': 'red-lighten-2',
    }
    return colors[status] || 'grey'
  }

  const loadData = async () => {
    try {
      loading.value = true
      await dashboardService.initializeData()
      pipelineData.value = await dashboardService.getDealPipelineData()
    } catch (error) {
      console.error('Error loading pipeline data:', error)
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    loadData()
  })
</script>
