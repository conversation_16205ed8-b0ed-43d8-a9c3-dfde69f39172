<template>
  <v-card>
    <v-card-title>Recent Activity</v-card-title>
    <v-card-text>
      <div v-if="loading" class="text-center pa-4">
        <v-progress-circular color="primary" indeterminate />
      </div>
      <div v-else>
        <v-list density="compact" lines="two">
          <template v-for="(item, index) in activityFeed" :key="item.id">
            <v-list-item>
              <template #prepend>
                <v-avatar :color="getItemColor(item.type)" size="40">
                  <v-icon color="white" :icon="item.icon" />
                </v-avatar>
              </template>

              <v-list-item-title class="text-subtitle-2">
                <span>{{ getActivityPrefix(item) }}</span>
                <span
                  v-if="item.contact?.id && item.contact?.fullName"
                  class="contact-link text-primary cursor-pointer"
                  @click="handleContactClick(item.contact.id)"
                >
                  {{ item.contact.fullName }}
                </span>
                <span v-else>Unknown Contact</span>
              </v-list-item-title>

              <v-list-item-subtitle class="text-caption">
                {{ item.subtitle }}
              </v-list-item-subtitle>

              <template #append>
                <div class="text-caption text-grey text-right">
                  {{ formatTimestamp(item.timestamp) }}
                </div>
              </template>
            </v-list-item>

            <v-divider v-if="index < activityFeed.length - 1" />
          </template>
        </v-list>
      </div>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue'
  import { useRouter } from 'vue-router'
  import { DashboardService } from '@/services/DashboardService'

  const router = useRouter()
  const loading = ref(true)
  const activityFeed = ref([])
  const dashboardService = new DashboardService()

  const getItemColor = type => {
    // Only activities now, so always return blue
    return 'blue'
  }

  const formatTimestamp = timestamp => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60))

    if (diffInHours < 1) {
      return 'Just now'
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  const getActivityPrefix = item => {
    // The title from DashboardService already ends with "to "
    return item.title
  }

  const handleContactClick = contactId => {
    router.push(`/contact-details?id=${contactId}`)
  }

  const loadData = async () => {
    try {
      loading.value = true
      await dashboardService.initializeData()
      activityFeed.value = await dashboardService.getRecentActivityFeed()
    } catch (error) {
      console.error('Error loading activity feed:', error)
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    loadData()
  })
</script>

<style scoped>
  :deep(.contact-link) {
    text-decoration: underline;
    font-weight: 500;
  }

  :deep(.contact-link:hover) {
    text-decoration: none;
    opacity: 0.8;
  }
</style>
