<template>
  <v-card style="height: 270px;">
    <v-card-title>Activity Timeline</v-card-title>
    <v-card-text class="pb-0 d-flex flex-column" style="height: 230px; overflow-y: auto;">
      <div v-if="loading" class="text-center pa-4">
        <v-progress-circular color="primary" indeterminate />
      </div>
      <div v-else class="d-flex flex-column flex-grow-1">
        <!-- Spacer to push chart to bottom -->
        <div class="flex-grow-1" />
        <!-- Chart container -->
        <div class="pa-2 pb-5">
          <div class="d-flex align-end justify-space-between" style="height: 160px;">
            <div
              v-for="month in timelineData"
              :key="month.date"
              class="d-flex flex-column align-center"
              style="flex: 1;"
            >
              <div
                class="bg-primary rounded-t"
                :style="{
                  height: `${getBarHeight(month.count)}px`,
                  width: '32px',
                  marginBottom: '4px'
                }"
              />
              <div class="text-caption text-center">
                <div class="font-weight-bold">{{ month.count }}</div>
                <div class="text-grey">{{ month.formattedDate }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue'
  import { DashboardService } from '@/services/DashboardService'

  const loading = ref(true)
  const timelineData = ref([])
  const dashboardService = new DashboardService()

  const maxActivities = computed(() => {
    return Math.max(...timelineData.value.map(month => month.count), 1)
  })

  const getBarHeight = count => {
    const maxHeight = 120
    return Math.max((count / maxActivities.value) * maxHeight, 4)
  }

  const loadData = async () => {
    try {
      loading.value = true
      await dashboardService.initializeData()
      timelineData.value = await dashboardService.getActivityTimelineData()
    } catch (error) {
      console.error('Error loading timeline data:', error)
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    loadData()
  })
</script>
