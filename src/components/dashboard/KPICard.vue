<template>
  <v-card :border="active && 'primary sm opacity-100'" :class="clickable && 'cursor-pointer'">
    <v-card-text class="d-flex align-center">
      <div class="flex-grow-1">
        <div class="text-h5 font-weight-bold mb-1" :class="`text-${color}`">
          {{ formattedValue }}
        </div>
        <div class="text-subtitle-1 text-grey-darken-1">
          {{ title }}
        </div>
      </div>
      <v-avatar class="ml-2" :color="color" size="42">
        <v-icon color="white" :icon="icon" size="26" />
      </v-avatar>
    </v-card-text>
  </v-card>
</template>

<script setup lang="js">
  import { computed } from 'vue'

  const props = defineProps({
    title: {
      type: String,
      required: true,
    },
    value: {
      type: [Number, String],
      required: true,
    },
    icon: {
      type: String,
      required: true,
    },
    format: {
      type: String,
      default: 'number', // 'number', 'currency', 'text'
    },
    color: {
      type: String,
      default: 'primary',
    },
    active: {
      type: Boolean,
      default: false,
    },
    clickable: {
      type: Boolean,
      default: false,
    },
  })

  const formattedValue = computed(() => {
    if (props.format === 'currency') {
      return `$${Number(props.value).toLocaleString()}`
    } else if (props.format === 'number') {
      return Number(props.value).toLocaleString()
    }
    return props.value
  })
</script>
