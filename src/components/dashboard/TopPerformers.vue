<template>
  <v-card style="height: 270px;">
    <v-card-title>Top Performers</v-card-title>
    <v-card-text class="pb-0" style="height: 230px; overflow-y: auto;">
      <div v-if="loading" class="text-center pa-4">
        <v-progress-circular color="primary" indeterminate />
      </div>
      <div v-else>
        <div
          v-for="(performer, index) in performers"
          :key="performer.user.id"
          class="d-flex align-center mb-3"
          :class="{ 'mb-0': index === performers.length - 1 }"
        >
          <div class="text-h6 font-weight-bold text-primary mr-3" style="min-width: 24px;">
            {{ index + 1 }}
          </div>
          <v-avatar class="mr-3" size="40">
            <v-img
              :alt="performer.user.id"
              :src="performer.user.avatar"
            />
          </v-avatar>
          <div class="flex-grow-1">
            <div class="text-subtitle-2 font-weight-medium">
              {{ performer.user.fullName }}
            </div>
            <div class="text-caption text-grey">
              {{ performer.deals }} deals • {{ performer.activities }} activities
            </div>
          </div>
          <div class="text-right">
            <div class="text-body-2 font-weight-bold">
              ${{ performer.value.toLocaleString() }}
            </div>
            <div class="text-caption text-grey">
              Score: {{ performer.score }}
            </div>
          </div>
        </div>
      </div>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
  import { onMounted, ref } from 'vue'
  import { DashboardService } from '@/services/DashboardService'

  const loading = ref(true)
  const performers = ref([])
  const dashboardService = new DashboardService()

  const loadData = async () => {
    try {
      loading.value = true
      await dashboardService.initializeData()
      performers.value = await dashboardService.getTopPerformers()
    } catch (error) {
      console.error('Error loading performers data:', error)
    } finally {
      loading.value = false
    }
  }

  onMounted(() => {
    loadData()
  })
</script>
