import { v4 as uuidV4 } from 'uuid'

export class BaseModel {
  constructor ({ id = null, history = [] } = {}) {
    this.id = id || uuidV4()
    this.history = history
  }

  isFieldChanged (field) {
    for (const historyItem of this.history) {
      for (const change of historyItem.changes) {
        if (change.field === field) {
          return true
        }
      }
    }
    return false
  }

  getFieldChanges (field) {
    const changes = []
    for (const historyItem of this.history) {
      for (const change of historyItem.changes) {
        if (change.field === field) {
          changes.push({ ...change, createdAt: historyItem.createdAt })
        }
      }
    }
    return changes
  }
}
