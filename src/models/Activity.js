import { v4 as uuidv4 } from 'uuid'

export class Activity {
  constructor ({
    id = null,
    note = '',
    creationDateTime = null,
    contact = '', // Contact ID
    activityCreator = '', // User ID
  } = {}) {
    this.id = id || this.generateId()
    this.note = note
    this.creationDateTime = creationDateTime || new Date()
    this.contact = contact
    this.activityCreator = activityCreator
  }

  generateId () {
    return uuidv4()
  }

  get formattedDateTime () {
    if (!this.creationDateTime) {
      return ''
    }
    const date = new Date(this.creationDateTime)
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  toJSON () {
    return {
      id: this.id,
      note: this.note,
      creationDateTime: this.creationDateTime,
      contact: this.contact,
      activityCreator: this.activityCreator,
    }
  }

  static fromJSON (data) {
    return new Activity({
      ...data,
      creationDateTime: data.creationDateTime ? new Date(data.creationDateTime) : new Date(),
    })
  }
}
