import { BaseModel } from '@/models/BaseModel'

export class Contact extends BaseModel {
  constructor ({
    id = null,
    firstName = '',
    lastName = '',
    email = '',
    phoneNumber = '',
    company = '', // Company ID
    contactOwner = '', // User ID
    history = [],
  } = {}) {
    super({ id, history })
    this.firstName = firstName
    this.lastName = lastName
    this.email = email
    this.phoneNumber = phoneNumber
    this.company = company
    this.contactOwner = contactOwner
  }

  get fullName () {
    return `${this.firstName} ${this.lastName}`.trim()
  }

  get formattedPhone () {
    // Format phone number like "******-678-1038"
    const cleaned = this.phoneNumber.replace(/\D/g, '')
    if (cleaned.length === 11 && cleaned.startsWith('1')) {
      return `+1 ${cleaned.slice(1, 4)}-${cleaned.slice(4, 7)}-${cleaned.slice(7)}`
    }
    return this.phoneNumber
  }

  toJSON () {
    return {
      id: this.id,
      firstName: this.firstName,
      lastName: this.lastName,
      email: this.email,
      phoneNumber: this.phoneNumber,
      company: this.company,
      contactOwner: this.contactOwner,
      history: this.history,
    }
  }

  static fromJSON (data) {
    return new Contact(data)
  }
}
