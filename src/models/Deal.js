import { BaseModel } from '@/models/BaseModel'
import { formatBudget } from '@/utils/format'

export class Deal extends BaseModel {
  static PRIORITY = {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
  }

  static STATUS = {
    IDENTIFICATION: 'identification',
    PROPOSAL: 'proposal',
    NEGOTIATION: 'negotiation',
    CLOSED_WON: 'closed won',
    CLOSED_LOST: 'closed lost',
  }

  constructor ({
    id = null,
    title = '',
    priority = Deal.PRIORITY.MEDIUM,
    status = Deal.STATUS.IDENTIFICATION,
    budget = 0,
    company = '', // Company ID
    dealOwner = '', // User ID
    createdAt = null, // Timestamp in milliseconds
    history = [],
  } = {}) {
    super({ id, history })
    this.title = title
    this.priority = priority
    this.status = status
    this.budget = budget
    this.company = company
    this.dealOwner = dealOwner
    this.createdAt = createdAt
  }

  isLost () {
    return this.status === Deal.STATUS.CLOSED_LOST
  }

  isWon () {
    return this.status === Deal.STATUS.CLOSED_WON
  }

  isActive () {
    return !this.isLost() && !this.isWon()
  }

  get formattedBudget () {
    return formatBudget(this.budget)
  }

  get priorityColor () {
    switch (this.priority) {
      case Deal.PRIORITY.LOW: { return 'grey'
      }
      case Deal.PRIORITY.MEDIUM: { return 'orange'
      }
      case Deal.PRIORITY.HIGH: { return 'red'
      }
      default: { return 'grey'
      }
    }
  }

  get statusColor () {
    switch (this.status) {
      case Deal.STATUS.IDENTIFICATION: { return 'blue'
      }
      case Deal.STATUS.PROPOSAL: { return 'purple'
      }
      case Deal.STATUS.NEGOTIATION: { return 'orange'
      }
      case Deal.STATUS.CLOSED_WON: { return 'green'
      }
      case Deal.STATUS.CLOSED_LOST: { return 'red'
      }
      default: { return 'grey'
      }
    }
  }

  get statusOrder () {
    switch (this.status) {
      case Deal.STATUS.IDENTIFICATION: { return 1
      }
      case Deal.STATUS.PROPOSAL: { return 2
      }
      case Deal.STATUS.NEGOTIATION: { return 3
      }
      case Deal.STATUS.CLOSED_WON: { return 4
      }
      case Deal.STATUS.CLOSED_LOST: { return 5
      }
      default: { return 6
      }
    }
  }

  get formattedCreatedAt () {
    if (!this.createdAt) {
      return ''
    }

    const date = new Date(this.createdAt)
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    })
  }

  toJSON () {
    return {
      id: this.id,
      title: this.title,
      priority: this.priority,
      status: this.status,
      budget: this.budget,
      company: this.company,
      dealOwner: this.dealOwner,
      createdAt: this.createdAt,
      history: this.history,
    }
  }

  static fromJSON (data) {
    return new Deal(data)
  }
}
