import { BaseModel } from '@/models/BaseModel'

export class Company extends BaseModel {
  constructor ({
    id = null,
    name = '',
    email = '',
    phoneNumber = '',
    logo = '',
    location = '',
    industry = '',
    history = [],
  } = {}) {
    super({ id, history })
    this.name = name
    this.email = email
    this.phoneNumber = phoneNumber
    this.logo = logo
    this.location = location
    this.industry = industry
  }

  get formattedPhone () {
    // Format phone number like "******-678-1038"
    const cleaned = this.phoneNumber.replace(/\D/g, '')
    if (cleaned.length === 11 && cleaned.startsWith('1')) {
      return `+1 ${cleaned.slice(1, 4)}-${cleaned.slice(4, 7)}-${cleaned.slice(7)}`
    }
    return this.phoneNumber
  }

  toJSON () {
    return {
      id: this.id,
      name: this.name,
      email: this.email,
      phoneNumber: this.phoneNumber,
      logo: this.logo,
      location: this.location,
      industry: this.industry,
      history: this.history,
    }
  }

  static fromJSON (data) {
    return new Company(data)
  }
}
