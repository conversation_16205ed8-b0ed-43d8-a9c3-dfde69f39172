import { BaseModel } from '@/models/BaseModel'

export class User extends BaseModel {
  constructor ({
    id = null,
    firstName = '',
    lastName = '',
    email = '',
    avatar = '',
    access = [],
    isActive = true,
    history = [],
  } = {}) {
    super({ id, history })
    this.firstName = firstName
    this.lastName = lastName
    this.email = email
    this.avatar = avatar
    this.access = access
    this.isActive = isActive
  }

  get fullName () {
    return `${this.firstName} ${this.lastName}`.trim()
  }

  get formattedAccess () {
    return this.access
      .map(access => access.charAt(0).toUpperCase() + access.slice(1))
      .join(' | ')
  }

  toJSON () {
    return {
      id: this.id,
      firstName: this.firstName,
      lastName: this.lastName,
      email: this.email,
      avatar: this.avatar,
      access: this.access,
      isActive: this.isActive,
      history: this.history,
    }
  }

  static fromJSON (data) {
    return new User(data)
  }
}
