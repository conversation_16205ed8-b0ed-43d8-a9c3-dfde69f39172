/**
 * main.js
 *
 * Bootstraps Vuetify and other plugins then mounts the App`
 */

// Browser polyfills - MUST be first
import './polyfills.js'

// Composables
import { createApp } from 'vue'

// Plugins
import { registerPlugins } from '@/plugins'

import { localContextService } from '@/services/voice-agent/context/LocalContextService'
import { deepgramService } from '@/services/voice-agent/DeepgramService'
// Components
import App from './App.vue'

// Styles
import 'unfonts.css'


const app = createApp(App)

registerPlugins(app)

app.mount('#app')

// Expose services to window for development/testing (only in development)
if (import.meta.env.DEV) {
  window.localContextService = localContextService
  window.deepgramService = deepgramService

  // Audio debugging helpers
  window.audioDebug = {
    enable: () => deepgramService.setAudioDebugging(true),
    disable: () => deepgramService.setAudioDebugging(false),
    stats: () => deepgramService.getAudioStats(),
    download: () => deepgramService.downloadRecordedAudio(),
    clear: () => deepgramService.clearAudioDebugData(),
    raw: count => deepgramService.getRawAudioSamples(count),
    resampled: count => deepgramService.getResampledAudioSamples(count),
    pcm: count => deepgramService.getPcmAudioSamples(count),
  }

  console.log('[Dev] Voice agent services exposed to window object for testing')
  console.log('[Dev] Use window.audioDebug to monitor audio pipeline:')
  console.log('  - audioDebug.enable()     - Enable audio debugging')
  console.log('  - audioDebug.stats()      - Get current audio stats')
  console.log('  - audioDebug.download()   - Download recorded audio')
  console.log('  - audioDebug.raw(1000)    - Get raw audio samples')
  console.log('  - audioDebug.pcm(1000)    - Get PCM samples sent to Deepgram')
}
