export class EditHistoryService {
  /**
   * @param {BaseModel | null} model
   */
  constructor (model = null) {
    this.changes = {}
    this.historyChanges = {}
    if (model) {
      this.setModel(model)
    }
  }

  /**
   * @param {BaseModel} model
   */
  setModel (model) {
    this.model = model
    this.historyChanges = {}
    const history = model.history || []

    // Process all history entries from newest to oldest
    for (let i = history.length - 1; i >= 0; i--) {
      const historyEntry = history[i]
      const changes = historyEntry?.changes || []

      for (const { field, from } of changes) {
        // Only set if this field hasn't been set yet (most recent wins)
        if (!(field in this.historyChanges)) {
          this.historyChanges[field] = from
        }
      }
    }
  }

  hasChanges () {
    return Object.keys(this.changes).length > 0
  }

  isChanged (key) {
    return key in this.changes || key in this.historyChanges
  }

  remember (key, value) {
    this.changes[key] = value
    // Prevent restoring from history after editing a form
    this.historyChanges = {}
  }

  restore (key) {
    if (key in this.changes) {
      const value = this.changes[key]
      delete this.changes[key]
      return value
    } else if (key in this.historyChanges) {
      return this.historyChanges[key]
    } else if (this.model && key in this.model) {
      return this.model[key]
    }
  }

  getHistoryChange (key) {
    return (key in this.historyChanges) ? this.historyChanges[key] : null
  }
}
