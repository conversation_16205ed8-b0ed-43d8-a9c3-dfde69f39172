/**
 * AudioDebugger - Audio debugging, analysis, and monitoring utilities
 *
 * This class provides comprehensive audio debugging capabilities for voice processing pipelines.
 * It captures audio data at different stages, analyzes quality metrics, and provides tools
 * for developers to diagnose and optimize audio processing.
 *
 * ## Core Features:
 * - **Audio Pipeline Monitoring**: Captures raw, resampled, and PCM audio data
 * - **Real-time Quality Analysis**: RMS, peak frequency, transmission rate tracking
 * - **Audio File Export**: Download processed audio as WAV files for analysis
 * - **Debug Events**: Real-time debugging information with throttled emission
 * - **Development Tools**: Browser console integration for easy testing
 *
 * ## Usage Examples:
 *
 * ### Basic Usage (Integrated with DeepgramService):
 * ```javascript
 * // AudioDebugger is automatically used by DeepgramService
 * // Access via window.audioDebug in development mode
 *
 * // Enable debugging
 * window.audioDebug.enable()
 *
 * // Get current audio statistics
 * const stats = window.audioDebug.stats()
 * console.log('Audio Stats:', stats)
 *
 * // Download recorded audio for analysis
 * window.audioDebug.download()
 *
 * // Get raw audio samples for custom analysis
 * const samples = window.audioDebug.raw(1000)
 * ```
 *
 * ### Advanced Usage (Direct instantiation):
 * ```javascript
 * import { AudioDebugger } from './utils/AudioDebugger'
 *
 * const debugger = new AudioDebugger()
 *
 * // Listen for debug events
 * debugger.addEventListener((debugInfo) => {
 *   console.log('Audio Quality:', {
 *     rawRMS: debugInfo.rawAudio.rms,
 *     resampledRMS: debugInfo.resampledAudio.rms,
 *     pcmRMS: debugInfo.pcmAudio.rms,
 *     transmissionRate: debugInfo.transmission.bytesPerSecond
 *   })
 * })
 *
 * // Enable debugging
 * debugger.setDebugging(true)
 *
 * // Capture audio data (typically called by audio processor)
 * debugger.captureRawAudio(audioData, sampleRate)
 * debugger.captureResampledAudio(resampledData)
 * debugger.capturePcmAudio(pcmData)
 *
 * // Emit debug information
 * debugger.emitAudioDebugInfo(rawData, resampledData, pcmData)
 * ```
 *
 * ### Quality Assessment:
 * ```javascript
 * // Monitor audio quality in real-time
 * window.audioDebug.enable()
 *
 * // Check for quality issues
 * const stats = window.audioDebug.stats()
 * if (stats.bytesPerSecond < 40000) {
 *   console.warn('Low transmission rate detected')
 * }
 *
 * // Analyze audio samples
 * const rawSamples = window.audioDebug.raw(100)
 * const rms = Math.sqrt(rawSamples.reduce((sum, x) => sum + x*x, 0) / rawSamples.length)
 * if (rms < 0.001) {
 *   console.warn('Very low audio signal detected')
 * }
 * ```
 *
 * ### Audio File Analysis:
 * ```javascript
 * // Download and analyze processed audio
 * window.audioDebug.enable()
 * // Start recording, speak for 10 seconds
 * window.audioDebug.download()
 * // Opens download dialog for WAV file
 *
 * // The downloaded file contains exactly what was sent to Deepgram
 * // You can open it in Audacity or similar tools to:
 * // - Check for clipping or distortion
 * // - Analyze frequency response
 * // - Verify audio quality and clarity
 * ```
 *
 * ## Quality Metrics Guide:
 *
 * ### RMS (Root Mean Square):
 * - **Good**: 0.01 - 0.1 (normal speech levels)
 * - **Low**: < 0.001 (might indicate mic issues)
 * - **High**: > 0.5 (might indicate clipping)
 *
 * ### Transmission Rate:
 * - **Expected**: ~48KB/sec (24kHz × 16-bit × 1 channel)
 * - **Low**: < 40KB/sec (processing issues)
 * - **Inconsistent**: Large variations (network/timing issues)
 *
 * ### Peak Frequency:
 * - **Speech**: Typically 100-4000 Hz
 * - **No signal**: 0 Hz
 * - **Noise**: High frequency components
 *
 * ## Debug Event Format:
 * ```javascript
 * {
 *   timestamp: 1234567890,
 *   rawAudio: { length, rms, peakFrequency, sampleRate },
 *   resampledAudio: { length, rms, peakFrequency, sampleRate },
 *   pcmAudio: { length, rms, bytes },
 *   transmission: { totalBytesSent, bytesPerSecond }
 * }
 * ```
 *
 * @class AudioDebugger
 * @example
 * // See usage examples above for comprehensive implementation patterns
 */

export class AudioDebugger {
  constructor () {
    // Audio debugging state
    this.enabled = import.meta.env.DEV // Only enable in development
    this.rawAudioSamples = []
    this.resampledAudioSamples = []
    this.pcmAudioSamples = []
    this.sentBytes = 0
    this.audioStats = {
      rawSampleRate: 0,
      rawBufferSize: 0,
      resampledLength: 0,
      pcmLength: 0,
      bytesPerSecond: 0,
    }
    this.recordingBuffer = [] // Store last 5 seconds for playback
    this.maxRecordingSeconds = 5

    // Timing for throttled operations
    this.lastStatsTime = 0
    this.lastSentBytes = 0
    this.lastEmitTime = 0
    this.lastLogTime = 0

    // Event listeners for debug events
    this.debugEventListeners = []
  }

  /**
   * Enable/disable audio debugging
   *
   * @param {boolean} enabled - Whether to enable audio debugging
   * @example
   * // Enable debugging for development
   * debugger.setDebugging(true)
   *
   * // Disable to reduce overhead in production
   * debugger.setDebugging(false)
   */
  setDebugging (enabled) {
    this.enabled = enabled
    if (enabled) {
      console.log('[AudioDebugger] 🎛️ Audio debugging enabled')
    } else {
      console.log('[AudioDebugger] 🎛️ Audio debugging disabled')
    }
  }

  /**
   * Check if debugging is enabled
   */
  isEnabled () {
    return this.enabled
  }

  /**
   * Add event listener for debug events
   *
   * @param {Function} callback - Function to call when debug events are emitted
   * @param {Object} callback.debugInfo - Debug information object
   * @param {number} callback.debugInfo.timestamp - Event timestamp
   * @param {Object} callback.debugInfo.rawAudio - Raw audio metrics
   * @param {Object} callback.debugInfo.resampledAudio - Resampled audio metrics
   * @param {Object} callback.debugInfo.pcmAudio - PCM audio metrics
   * @param {Object} callback.debugInfo.transmission - Transmission metrics
   *
   * @example
   * debugger.addEventListener((debugInfo) => {
   *   console.log('Audio Quality Update:', {
   *     rawRMS: debugInfo.rawAudio.rms,
   *     transmissionRate: debugInfo.transmission.bytesPerSecond
   *   })
   * })
   */
  addEventListener (callback) {
    this.debugEventListeners.push(callback)
  }

  /**
   * Remove event listener
   */
  removeEventListener (callback) {
    const index = this.debugEventListeners.indexOf(callback)
    if (index !== -1) {
      this.debugEventListeners.splice(index, 1)
    }
  }

  /**
   * Emit debug event to all listeners
   */
  emit (data) {
    for (const callback of this.debugEventListeners) {
      try {
        callback(data)
      } catch (error) {
        console.error('[AudioDebugger] Error in debug event listener:', error)
      }
    }
  }

  /**
   * Capture raw audio data from microphone for debugging analysis
   *
   * This method is typically called by the audio processing pipeline to capture
   * the original microphone input before any processing is applied.
   *
   * @param {Float32Array} inputData - Raw audio samples from microphone
   * @param {number} sampleRate - Original sample rate (typically 44.1kHz or 48kHz)
   *
   * @example
   * // Typically called by AudioProcessor
   * debugger.captureRawAudio(microphoneData, 48000)
   */
  captureRawAudio (inputData, sampleRate) {
    if (!this.enabled) {
      return
    }

    // Update stats
    this.audioStats.rawSampleRate = sampleRate
    this.audioStats.rawBufferSize = inputData.length

    // Store samples for analysis (keep last 10000)
    this.rawAudioSamples.push(...Array.from(inputData))
    if (this.rawAudioSamples.length > 10_000) {
      this.rawAudioSamples = this.rawAudioSamples.slice(-10_000)
    }

    // Store for recording buffer (keep last 5 seconds)
    this.recordingBuffer.push(new Float32Array(inputData))
    const maxBufferLength = Math.ceil(this.maxRecordingSeconds * sampleRate / inputData.length)
    if (this.recordingBuffer.length > maxBufferLength) {
      this.recordingBuffer = this.recordingBuffer.slice(-maxBufferLength)
    }
  }

  /**
   * Capture resampled audio data for debugging
   */
  captureResampledAudio (resampledData) {
    if (!this.enabled) {
      return
    }

    // Update stats
    this.audioStats.resampledLength = resampledData.length

    // Store samples for analysis (keep last 10000)
    this.resampledAudioSamples.push(...Array.from(resampledData))
    if (this.resampledAudioSamples.length > 10_000) {
      this.resampledAudioSamples = this.resampledAudioSamples.slice(-10_000)
    }
  }

  /**
   * Capture PCM audio data for debugging
   */
  capturePcmAudio (pcmData) {
    if (!this.enabled) {
      return
    }

    // Update stats
    this.audioStats.pcmLength = pcmData.length

    // Store samples for analysis (keep last 10000)
    this.pcmAudioSamples.push(...Array.from(pcmData))
    if (this.pcmAudioSamples.length > 10_000) {
      this.pcmAudioSamples = this.pcmAudioSamples.slice(-10_000)
    }
  }

  /**
   * Track sent bytes and emit debug info
   */
  trackSentBytes (byteLength) {
    if (!this.enabled) {
      return
    }
    this.sentBytes += byteLength
  }

  /**
   * Emit comprehensive audio debug information to listeners
   *
   * Calculates quality metrics and emits debug events with detailed audio analysis.
   * Events are throttled to every 500ms to prevent overwhelming listeners.
   *
   * @param {Float32Array} rawData - Original microphone audio data
   * @param {Float32Array} resampledData - Audio resampled to 24kHz
   * @param {Int16Array} pcmData - Final PCM data sent to Deepgram
   *
   * @example
   * // Typically called by audio processing pipeline
   * debugger.emitAudioDebugInfo(rawAudio, resampledAudio, pcmAudio)
   *
   * // Listen for the emitted events
   * debugger.addEventListener((debugInfo) => {
   *   console.log('Quality metrics:', debugInfo)
   * })
   */
  emitAudioDebugInfo (rawData, resampledData, pcmData) {
    if (!this.enabled) {
      return
    }

    // Calculate audio quality metrics
    const rawRms = this.calculateRms(rawData)
    const resampledRms = this.calculateRms(resampledData)
    const pcmRms = this.calculateRms(Array.from(pcmData).map(x => x / 32_768)) // Normalize PCM to float

    // Calculate frequency content (simplified)
    const rawPeakFreq = this.estimatePeakFrequency(rawData, this.audioStats.rawSampleRate)
    const resampledPeakFreq = this.estimatePeakFrequency(resampledData, 24_000)

    // Update bytes per second calculation
    const now = Date.now()
    if (!this.lastStatsTime) {
      this.lastStatsTime = now
      this.lastSentBytes = this.sentBytes
    } else if (now - this.lastStatsTime >= 1000) {
      const bytesDiff = this.sentBytes - this.lastSentBytes
      const timeDiff = (now - this.lastStatsTime) / 1000
      this.audioStats.bytesPerSecond = Math.round(bytesDiff / timeDiff)
      this.lastStatsTime = now
      this.lastSentBytes = this.sentBytes
    }

    const debugInfo = {
      timestamp: now,
      rawAudio: {
        length: rawData.length,
        rms: rawRms,
        peakFrequency: rawPeakFreq,
        sampleRate: this.audioStats.rawSampleRate,
      },
      resampledAudio: {
        length: resampledData.length,
        rms: resampledRms,
        peakFrequency: resampledPeakFreq,
        sampleRate: 24_000,
      },
      pcmAudio: {
        length: pcmData.length,
        rms: pcmRms,
        bytes: pcmData.buffer.byteLength,
      },
      transmission: {
        totalBytesSent: this.sentBytes,
        bytesPerSecond: this.audioStats.bytesPerSecond,
      },
    }

    // Emit debug event (throttled to every 500ms)
    if (!this.lastEmitTime || now - this.lastEmitTime >= 500) {
      this.emit(debugInfo)
      this.lastEmitTime = now

      // Console log every 2 seconds
      if (!this.lastLogTime || now - this.lastLogTime >= 2000) {
        // console.log('[AudioDebugger] 🎤 Audio Debug:', {
        //   'Raw→Resampled→PCM': `${rawData.length}→${resampledData.length}→${pcmData.length}`,
        //   'RMS': `${rawRms.toFixed(3)}→${resampledRms.toFixed(3)}→${pcmRms.toFixed(3)}`,
        //   'Bytes/sec': debugInfo.transmission.bytesPerSecond,
        //   'Total sent': `${(this.sentBytes / 1024).toFixed(1)}KB`,
        // })
        this.lastLogTime = now
      }
    }
  }

  /**
   * Calculate RMS (Root Mean Square) for audio quality assessment
   *
   * RMS provides a measure of the average signal strength/loudness.
   *
   * @param {Float32Array|number[]} audioData - Audio samples to analyze
   * @returns {number} RMS value (0.0 to 1.0 for normalized audio)
   *
   * @example
   * const rms = debugger.calculateRms(audioSamples)
   *
   * // Interpret RMS values:
   * if (rms < 0.001) console.log('Very quiet or no signal')
   * else if (rms < 0.01) console.log('Quiet speech')
   * else if (rms < 0.1) console.log('Normal speech level')
   * else console.log('Loud signal, possible clipping')
   */
  calculateRms (audioData) {
    let sum = 0
    for (const audioDatum of audioData) {
      sum += audioDatum * audioDatum
    }
    return Math.sqrt(sum / audioData.length)
  }

  /**
   * Estimate peak frequency (simplified FFT-like analysis)
   */
  estimatePeakFrequency (audioData, sampleRate) {
    // Very simplified frequency estimation - in production you'd use FFT
    let maxMagnitude = 0
    let peakFreq = 0
    const numBins = Math.min(128, audioData.length / 2)

    for (let k = 1; k < numBins; k++) {
      let real = 0
      let imag = 0

      for (let n = 0; n < audioData.length; n++) {
        const angle = (-2 * Math.PI * k * n) / audioData.length
        real += audioData[n] * Math.cos(angle)
        imag += audioData[n] * Math.sin(angle)
      }

      const magnitude = Math.hypot(real, imag)
      if (magnitude > maxMagnitude) {
        maxMagnitude = magnitude
        peakFreq = (k * sampleRate) / audioData.length
      }
    }

    return Math.round(peakFreq)
  }

  /**
   * Get comprehensive audio debugging statistics
   *
   * @returns {Object} Audio statistics object
   * @returns {number} returns.rawSampleRate - Original microphone sample rate
   * @returns {number} returns.rawBufferSize - Raw audio buffer size
   * @returns {number} returns.resampledLength - Resampled audio length
   * @returns {number} returns.pcmLength - PCM audio length
   * @returns {number} returns.bytesPerSecond - Current transmission rate
   * @returns {number} returns.sentBytes - Total bytes sent to Deepgram
   * @returns {number} returns.recordingBufferSize - Number of recorded chunks
   * @returns {boolean} returns.enabled - Whether debugging is enabled
   *
   * @example
   * const stats = debugger.getAudioStats()
   * console.log(`Transmission rate: ${stats.bytesPerSecond} bytes/sec`)
   * console.log(`Total sent: ${(stats.sentBytes / 1024).toFixed(1)} KB`)
   *
   * // Check for quality issues
   * if (stats.bytesPerSecond < 40000) {
   *   console.warn('Low transmission rate detected')
   * }
   */
  getAudioStats () {
    return {
      ...this.audioStats,
      sentBytes: this.sentBytes,
      recordingBufferSize: this.recordingBuffer.length,
      enabled: this.enabled,
    }
  }

  /**
   * Get raw audio samples for custom analysis
   *
   * @param {number} [count=1000] - Number of recent samples to return
   * @returns {number[]} Array of raw audio sample values (Float32)
   *
   * @example
   * // Get latest 100 raw samples
   * const samples = debugger.getRawAudioSamples(100)
   *
   * // Calculate custom RMS
   * const rms = Math.sqrt(samples.reduce((sum, x) => sum + x*x, 0) / samples.length)
   * console.log('Custom RMS calculation:', rms)
   *
   * // Find peak amplitude
   * const peak = Math.max(...samples.map(Math.abs))
   * console.log('Peak amplitude:', peak)
   */
  getRawAudioSamples (count = 1000) {
    return this.rawAudioSamples.slice(-count)
  }

  /**
   * Get resampled audio samples for analysis
   */
  getResampledAudioSamples (count = 1000) {
    return this.resampledAudioSamples.slice(-count)
  }

  /**
   * Get PCM audio samples (exactly what's sent to Deepgram)
   *
   * @param {number} [count=1000] - Number of recent samples to return
   * @returns {number[]} Array of PCM sample values (Int16)
   *
   * @example
   * // Get latest PCM samples sent to Deepgram
   * const pcmSamples = debugger.getPcmAudioSamples(500)
   *
   * // Convert back to float for analysis
   * const floatSamples = pcmSamples.map(x => x / 32768)
   *
   * // Check for clipping (values at max/min)
   * const clipped = pcmSamples.filter(x => Math.abs(x) >= 32767)
   * if (clipped.length > 0) {
   *   console.warn(`Audio clipping detected: ${clipped.length} samples`)
   * }
   */
  getPcmAudioSamples (count = 1000) {
    return this.pcmAudioSamples.slice(-count)
  }

  /**
   * Download recorded audio as WAV file for external analysis
   *
   * Creates and downloads a WAV file containing the last 5 seconds of processed audio
   * (exactly what was sent to Deepgram). This is invaluable for debugging audio quality issues.
   *
   * @example
   * // Start recording, speak for 10 seconds, then download
   * debugger.setDebugging(true)
   * // ... speak into microphone ...
   * debugger.downloadRecordedAudio()
   *
   * // The downloaded file can be analyzed in:
   * // - Audacity (free audio editor)
   * // - Any DAW or audio analysis software
   * // - Browser audio visualizers
   *
   * // Look for:
   * // - Frequency response issues
   * // - Clipping or distortion
   * // - Background noise
   * // - Signal-to-noise ratio
   */
  downloadRecordedAudio () {
    if (this.recordingBuffer.length === 0) {
      console.warn('[AudioDebugger] No recorded audio available')
      return
    }

    try {
      // Combine all recorded chunks
      const totalLength = this.recordingBuffer.reduce((sum, chunk) => sum + chunk.length, 0)
      const combinedBuffer = new Float32Array(totalLength)
      let offset = 0

      for (const chunk of this.recordingBuffer) {
        combinedBuffer.set(chunk, offset)
        offset += chunk.length
      }

      // Create WAV file
      const wavBuffer = this.createWavFile(combinedBuffer, 24_000)
      const blob = new Blob([wavBuffer], { type: 'audio/wav' })
      const url = URL.createObjectURL(blob)

      // Download
      const a = document.createElement('a')
      a.href = url
      a.download = `deepgram-audio-${Date.now()}.wav`
      document.body.append(a)
      a.click()
      a.remove()
      URL.revokeObjectURL(url)

      console.log('[AudioDebugger] 📥 Audio file downloaded')
    } catch (error) {
      console.error('[AudioDebugger] Failed to download audio:', error)
    }
  }

  /**
   * Create WAV file from audio data
   */
  createWavFile (audioData, sampleRate) {
    const length = audioData.length
    const arrayBuffer = new ArrayBuffer(44 + length * 2)
    const view = new DataView(arrayBuffer)

    // WAV header
    const writeString = (offset, string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.codePointAt(i))
      }
    }

    writeString(0, 'RIFF')
    view.setUint32(4, 36 + length * 2, true)
    writeString(8, 'WAVE')
    writeString(12, 'fmt ')
    view.setUint32(16, 16, true)
    view.setUint16(20, 1, true)
    view.setUint16(22, 1, true)
    view.setUint32(24, sampleRate, true)
    view.setUint32(28, sampleRate * 2, true)
    view.setUint16(32, 2, true)
    view.setUint16(34, 16, true)
    writeString(36, 'data')
    view.setUint32(40, length * 2, true)

    // Audio data
    let offset = 44
    for (let i = 0; i < length; i++) {
      const sample = Math.max(-1, Math.min(1, audioData[i]))
      view.setInt16(offset, sample < 0 ? sample * 0x80_00 : sample * 0x7F_FF, true)
      offset += 2
    }

    return arrayBuffer
  }

  /**
   * Clear all captured audio debugging data
   *
   * Resets all audio sample buffers, recording buffer, and byte counters.
   * Useful for starting fresh analysis sessions.
   *
   * @example
   * // Clear data between test sessions
   * debugger.clearAudioDebugData()
   *
   * // Start fresh recording session
   * debugger.setDebugging(true)
   * // ... new recording session ...
   */
  clearAudioDebugData () {
    this.rawAudioSamples = []
    this.resampledAudioSamples = []
    this.pcmAudioSamples = []
    this.recordingBuffer = []
    this.sentBytes = 0
    // console.log('[AudioDebugger] 🧹 Audio debug data cleared')
  }

  /**
   * Reset all timing states and clear data for new debugging session
   *
   * This method resets all internal timing counters and clears captured data.
   * Typically called automatically when starting a new voice recording session.
   *
   * @example
   * // Manually reset for new session
   * debugger.resetSession()
   *
   * // Now start fresh debugging
   * debugger.setDebugging(true)
   */
  resetSession () {
    this.lastStatsTime = 0
    this.lastSentBytes = 0
    this.lastEmitTime = 0
    this.lastLogTime = 0
    this.clearAudioDebugData()
  }
}
