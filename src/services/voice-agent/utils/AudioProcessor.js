/**
 * AudioProcessor - High-quality audio processing for voice transcription
 *
 * This class handles:
 * - Modern AudioWorklet processing with ScriptProcessor fallback
 * - Professional anti-aliasing resampling to 24kHz
 * - High-quality PCM conversion with dithering
 * - Real-time audio quality monitoring and debugging
 */

export class AudioProcessor {
  constructor () {
    // Audio processing state
    this.audioContext = null
    this.source = null
    this.processor = null // ScriptProcessor fallback for older browsers
    this.workletNode = null // Modern AudioWorklet for real-time, low-latency processing

    // Processing configuration
    this.targetSampleRate = 24_000
    this.bufferSize = 1024
    this.sendInterval = 50 // ms

    // Unique processor name to avoid conflicts
    this.processorName = `audio-processor-${Date.now()}-${Math.random().toString(36).slice(2, 9)}`

    // Callbacks
    this.onAudioData = null
    this.onError = null

    // Timing
    this.lastSendTime = 0
    this.lastDebugLog = 0

    // Bound handlers for event listeners (needed for proper cleanup)
    this.boundHandleMessage = null

    // Audio quality metrics
    this.metrics = {
      totalSamples: 0,
      avgRms: 0,
      peakFreq: 0,
    }
  }

  /**
   * Initialize audio context
   */
  async initializeAudioContext () {
    try {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)()
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume()
      }
      console.log('[AudioProcessor] Audio context initialized, sample rate:', this.audioContext.sampleRate)
    } catch (error) {
      console.error('[AudioProcessor] Failed to initialize audio context:', error)
      throw error
    }
  }

  /**
   * Get optimized microphone constraints
   */
  getMicrophoneConstraints () {
    return {
      audio: {
        channelCount: 1,
        echoCancellation: true, // Enable for better voice clarity
        noiseSuppression: true, // Enable to reduce background noise
        autoGainControl: true, // Enable for consistent volume
        sampleRate: { ideal: 48_000 }, // Request high sample rate
        sampleSize: { ideal: 16 }, // 16-bit samples
      },
    }
  }

  /**
   * Start processing audio stream
   */
  async startProcessing (mediaStream, onAudioData, onError) {
    this.onAudioData = onAudioData
    this.onError = onError

    if (!this.audioContext) {
      await this.initializeAudioContext()
    }

    try {
      // console.log('[AudioProcessor] Starting high-quality audio processing...')

      // Create audio source from media stream
      this.source = this.audioContext.createMediaStreamSource(mediaStream)

      // Use AudioWorklet for modern, low-latency audio processing
      // Fall back to ScriptProcessor if AudioWorklet is not supported
      if (this.audioContext.audioWorklet) {
        await this.setupAudioWorklet()
      } else {
        console.warn('[AudioProcessor] AudioWorklet not supported, falling back to ScriptProcessor')
        this.setupScriptProcessor()
      }

      // console.log('[AudioProcessor] High-quality audio processing started')
    } catch (error) {
      console.error('[AudioProcessor] Failed to start audio processing:', error)
      if (this.onError) {
        this.onError(error)
      }
    }
  }

  /**
   * Setup modern AudioWorklet for high-quality processing
   */
  async setupAudioWorklet () {
    try {
      // Register audio worklet processor
      const workletCode = this.getAudioWorkletCode()
      const workletBlob = new Blob([workletCode], { type: 'application/javascript' })
      const workletUrl = URL.createObjectURL(workletBlob)

      await this.audioContext.audioWorklet.addModule(workletUrl)

      // Create worklet node with unique processor name
      this.workletNode = new AudioWorkletNode(this.audioContext, this.processorName, {
        numberOfInputs: 1,
        numberOfOutputs: 1,
        outputChannelCount: [1],
      })

      // Handle processed audio data
      this.boundHandleMessage = event => {
        this.handleProcessedAudio(event.data)
      }
      this.workletNode.port.addEventListener('message', this.boundHandleMessage)

      // Start the port to enable message delivery
      this.workletNode.port.start()

      // Connect audio graph
      this.source.connect(this.workletNode)
      this.workletNode.connect(this.audioContext.destination)

      // Clean up URL
      URL.revokeObjectURL(workletUrl)

      console.log(`[AudioProcessor] ✅ AudioWorklet setup complete with processor: ${this.processorName}`)
    } catch (error) {
      console.warn('[AudioProcessor] AudioWorklet setup failed, falling back to ScriptProcessor:', error)
      this.setupScriptProcessor()
    }
  }

  /**
   * Fallback to ScriptProcessor with improved processing
   */
  setupScriptProcessor () {
    // Create script processor with smaller buffer for lower latency
    this.processor = this.audioContext.createScriptProcessor(this.bufferSize, 1, 1)

    // Process audio data with improved timing
    this.processor.onaudioprocess = event => {
      const now = Date.now()
      if (now - this.lastSendTime >= this.sendInterval) {
        const inputData = event.inputBuffer.getChannelData(0)
        this.handleProcessedAudio({
          audioData: Array.from(inputData),
          sampleRate: this.audioContext.sampleRate,
        })
        this.lastSendTime = now
      }
    }

    // Connect audio nodes
    this.source.connect(this.processor)
    this.processor.connect(this.audioContext.destination)

    console.log('[AudioProcessor] ✅ ScriptProcessor setup complete (fallback mode)')
  }

  /**
   * Handle processed audio data from either AudioWorklet or ScriptProcessor
   */
  handleProcessedAudio (data) {
    // Check if processor has been stopped
    if (!this.audioContext || this.audioContext.state === 'closed') {
      return
    }

    try {
      const inputData = new Float32Array(data.audioData)
      const inputSampleRate = data.sampleRate

      // Debug: Log audio processing (throttled every 2 seconds)
      this.throttledLog('🎤 Processing audio', `${inputData.length} samples at ${inputSampleRate}Hz`, 2000)

      // High-quality resample to target rate
      const resampledData = this.highQualityResample(inputData, inputSampleRate, this.targetSampleRate)

      // Convert to high-quality PCM format
      const pcmData = this.convertFloatToPcmHighQuality(resampledData)

      // Update metrics
      this.updateMetrics(inputData, resampledData, pcmData, inputSampleRate)

      // Send processed audio data
      if (this.onAudioData) {
        this.onAudioData({
          original: inputData,
          resampled: resampledData,
          pcm: pcmData,
          inputSampleRate,
          outputSampleRate: this.targetSampleRate,
          metrics: { ...this.metrics },
        })
      } else {
        console.warn('[AudioProcessor] No onAudioData callback set')
      }
    } catch (error) {
      console.error('[AudioProcessor] Error processing audio:', error)
      if (this.onError) {
        this.onError(error)
      }
    }
  }

  /**
   * High-quality resampling with anti-aliasing filter
   */
  highQualityResample (inputData, inputSampleRate, outputSampleRate) {
    if (inputSampleRate === outputSampleRate) {
      return inputData
    }

    const ratio = inputSampleRate / outputSampleRate
    const outputLength = Math.floor(inputData.length / ratio)
    const outputData = new Float32Array(outputLength)

    // Anti-aliasing filter parameters
    const filterLength = 127 // Should be odd
    const halfFilter = Math.floor(filterLength / 2)

    // Create Lanczos anti-aliasing filter
    const filter = new Float32Array(filterLength)
    const fc = Math.min(0.5, 0.5 / ratio) // Cutoff frequency

    for (let i = 0; i < filterLength; i++) {
      const x = i - halfFilter
      if (x === 0) {
        filter[i] = 2 * fc
      } else {
        const px = Math.PI * x
        filter[i] = Math.sin(2 * fc * px) / px
        // Apply Lanczos window
        if (Math.abs(x) < halfFilter) {
          filter[i] *= Math.sin(px / halfFilter) / (px / halfFilter)
        }
      }
    }

    // Normalize filter
    const filterSum = filter.reduce((sum, val) => sum + val, 0)
    for (let i = 0; i < filterLength; i++) {
      filter[i] /= filterSum
    }

    // Apply filter and resample
    for (let i = 0; i < outputLength; i++) {
      const center = i * ratio
      let sum = 0

      for (let j = 0; j < filterLength; j++) {
        const inputIndex = Math.round(center) - halfFilter + j
        if (inputIndex >= 0 && inputIndex < inputData.length) {
          sum += inputData[inputIndex] * filter[j]
        }
      }

      outputData[i] = sum
    }

    return outputData
  }

  /**
   * High-quality float to PCM conversion with dithering
   */
  convertFloatToPcmHighQuality (floatData) {
    const pcmData = new Int16Array(floatData.length)

    for (const [i, floatDatum] of floatData.entries()) {
      // Clamp to valid range
      let sample = Math.max(-1, Math.min(1, floatDatum))

      // Add triangular dithering to reduce quantization noise
      const dither = (Math.random() - Math.random()) * (1 / 32_768)
      sample += dither

      // Convert to 16-bit PCM
      pcmData[i] = sample < 0 ? sample * 0x80_00 : sample * 0x7F_FF
    }

    return pcmData
  }

  /**
   * Update audio quality metrics
   */
  updateMetrics (rawData, resampledData, pcmData, inputSampleRate) {
    // Check if processor has been stopped
    if (!this.audioContext) {
      return
    }

    // Calculate RMS for quality assessment
    const rawRms = this.calculateRms(rawData)

    // Update running averages
    this.metrics.totalSamples++
    this.metrics.avgRms = (this.metrics.avgRms * (this.metrics.totalSamples - 1) + rawRms) / this.metrics.totalSamples

    // Estimate peak frequency (simplified) - use provided sample rate instead of audioContext
    this.metrics.peakFreq = this.estimatePeakFrequency(rawData, inputSampleRate)
  }

  /**
   * Calculate RMS (Root Mean Square) for audio quality assessment
   */
  calculateRms (audioData) {
    let sum = 0
    for (const audioDatum of audioData) {
      sum += audioDatum * audioDatum
    }
    return Math.sqrt(sum / audioData.length)
  }

  /**
   * Estimate peak frequency (simplified FFT-like analysis)
   */
  estimatePeakFrequency (audioData, sampleRate) {
    let maxMagnitude = 0
    let peakFreq = 0
    const numBins = Math.min(128, audioData.length / 2)

    for (let k = 1; k < numBins; k++) {
      let real = 0
      let imag = 0

      for (let n = 0; n < audioData.length; n++) {
        const angle = (-2 * Math.PI * k * n) / audioData.length
        real += audioData[n] * Math.cos(angle)
        imag += audioData[n] * Math.sin(angle)
      }

      const magnitude = Math.hypot(real, imag)
      if (magnitude > maxMagnitude) {
        maxMagnitude = magnitude
        peakFreq = (k * sampleRate) / audioData.length
      }
    }

    return Math.round(peakFreq)
  }

  /**
   * Get AudioWorklet processor code with unique name
   */
  getAudioWorkletCode () {
    return `
      class AudioProcessor extends AudioWorkletProcessor {
        process(inputs, outputs, parameters) {
          const input = inputs[0];
          if (input.length > 0) {
            const audioData = input[0];
            this.port.postMessage({
              audioData: Array.from(audioData),
              sampleRate: sampleRate
            });
          }
          return true;
        }
      }

      // Use unique processor name to avoid conflicts
      try {
        registerProcessor('${this.processorName}', AudioProcessor);
      } catch (error) {
        // Processor already registered, ignore
        console.warn('AudioWorklet processor already registered:', error.message);
      }
    `
  }

  /**
   * Stop audio processing
   */
  stop () {
    // Clear callbacks to prevent further processing
    this.onAudioData = null
    this.onError = null

    // Disconnect audio nodes in proper order
    if (this.workletNode) {
      // Remove message handler to prevent callbacks
      if (this.boundHandleMessage) {
        this.workletNode.port.removeEventListener('message', this.boundHandleMessage)
        this.boundHandleMessage = null
      }
      this.workletNode.disconnect()
      this.workletNode = null
    }

    if (this.processor) {
      // Remove audio process handler
      this.processor.onaudioprocess = null
      this.processor.disconnect()
      this.processor = null
    }

    if (this.source) {
      this.source.disconnect()
      this.source = null
    }

    // Close audio context
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close()
      this.audioContext = null
    }

    // Reset metrics
    this.metrics = {
      totalSamples: 0,
      avgRms: 0,
      peakFreq: 0,
    }

    console.log('[AudioProcessor] Audio processing stopped')
  }

  /**
   * Get current audio processing metrics
   */
  getMetrics () {
    return {
      ...this.metrics,
      sampleRate: this.audioContext?.sampleRate || 0,
      targetSampleRate: this.targetSampleRate,
      bufferSize: this.bufferSize,
      sendInterval: this.sendInterval,
    }
  }

  /**
   * Check if audio processing is active
   */
  isActive () {
    return this.audioContext && this.audioContext.state === 'running'
      && (this.processor || this.workletNode)
  }

  /**
   * Utility for throttled debug logging
   */
  throttledLog (prefix, message, intervalMs = 2000) {
    const now = Date.now()
    if (!this.lastDebugLog || now - this.lastDebugLog > intervalMs) {
      // console.log(`[AudioProcessor] ${prefix}: ${message}`)
      this.lastDebugLog = now
    }
  }
}
