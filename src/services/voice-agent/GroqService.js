/**
 * GroqService - Groq API integration with context awareness
 *
 * This service handles:
 * - Processing user requests from DeepgramService
 * - Context-aware prompting with global/local trees
 * - Groq API integration with configurable models
 * - Response processing and action extraction
 */

import { Groq } from 'groq-sdk'
import { BaseLlmService } from './BaseLlmService'

export class GroqService extends BaseLlmService {
  constructor () {
    super('Groq')

    // Service state
    this.state = 'idle' // 'idle', 'processing', 'error'
    this.groq = null

    // Event listeners
    this.listeners = {
      stateChange: [],
      response: [],
      error: [],
    }

    // Configuration
    this.apiKey = import.meta.env.VITE_GROQ_API_KEY
    this.modelName = import.meta.env.VITE_GROQ_MODEL || 'meta-llama/llama-4-scout-17b-16e-instruct'

    // Initialize Groq client
    this.initializeClient()
  }

  /**
   * Get function declaration for structured execution plan generation
   */
  getExecutionPlanFunction () {
    return {
      type: 'function',
      function: {
        name: 'generateExecutionPlan',
        description: 'Generate a structured execution plan with response text and actionable steps for user requests in the Sentient UI CRM application',
        parameters: {
          type: 'object',
          properties: {
            response_text: {
              type: 'string',
              description: 'Friendly, helpful response text to display to the user explaining what you understand and what you\'re doing',
            },
            execution_plan: {
              type: 'array',
              description: 'Array of action steps to execute in order. Can be empty if no actions are needed.',
              items: {
                type: 'object',
                properties: {
                  action: {
                    type: 'string',
                    enum: ['navigate', 'useTool'],
                    description: 'Type of action: \'navigate\' for route changes, \'useTool\' for component interactions',
                  },
                  payload: {
                    type: 'object',
                    description: 'Action-specific data. Contains \'path\' for navigate actions, or \'targetComponent\', \'tool\', \'params\' for useTool actions',
                    properties: {
                      path: {
                        type: 'string',
                        description: 'Route path for navigation actions (e.g., \'/companies\', \'/settings\')',
                      },
                      targetComponent: {
                        type: 'object',
                        properties: {
                          name: {
                            type: 'string',
                            description: 'Component name (e.g., \'CompanyTable\', \'UserEditDialog\')',
                          },
                          id: {
                            type: 'string',
                            description: 'Component ID from local context',
                          },
                        },
                        description: 'Target component for useTool actions',
                      },
                      tool: {
                        type: 'string',
                        description: 'Tool name to execute on the target component',
                      },
                      params: {
                        type: 'array',
                        description: 'Parameters to pass to the tool function',
                      },
                    },
                  },
                },
                required: ['action'],
              },
            },
          },
          required: ['response_text', 'execution_plan'],
        },
      },
    }
  }

  /**
   * Initialize Groq client
   */
  initializeClient () {
    if (!this.apiKey) {
      console.error('[Groq] Groq API key not configured. Please set VITE_GROQ_API_KEY in .env file')
      this.setState('error')
      return
    }

    // Check if Buffer is available
    if (typeof Buffer === 'undefined' && typeof window !== 'undefined' && window.Buffer === undefined) {
      console.error('[Groq] Buffer polyfill not available. This is required for the Groq SDK to work in the browser.')
      this.setState('error')
      return
    }

    try {
      this.groq = new Groq({
        apiKey: this.apiKey,
        dangerouslyAllowBrowser: true,
      })
      this.log('Client initialized successfully with function calling')
    } catch (error) {
      console.error('[Groq] Failed to initialize Groq client:', error)
      console.error('[Groq] Error details:', error.message)
      this.setState('error')
    }
  }

  /**
   * Process user request with context-aware Groq
   * @param {string} userRequest - User's speech or text request
   * @param {string[]} userRequestHistory - User's speech or text request history
   * @param {string} currentRoute - Current application route
   * @param {Object} globalContext - Global application context
   * @param {Object} localContext - Local component context
   * @returns {Promise<Object>} - Groq response with text and potential actions
   */
  async processUserRequest (userRequest, userRequestHistory, currentRoute = '/', globalContext, localContext) {
    if (!this.groq) {
      console.error('[Groq] Service not available')
      return { error: 'LLM service not available' }
    }

    let generation, response
    try {
      this.setState('processing')
      this.log('Processing user request:', userRequest)

      // Build context-aware prompt using provided contexts
      const prompt = this.buildContextPrompt(userRequest, userRequestHistory, globalContext, localContext, currentRoute)

      this.log('Sending request to Groq...')

      generation = this.getLangFuseTrace()?.generation({
        name: 'chat-completion',
        model: this.modelName,
        modelParameters: {
          temperature: 0.7,
          maxOutputTokens: 1024,
        },
        input: {
          user_request: userRequest,
          user_request_history: userRequestHistory,
          local_context: this.buildToolsInformation(localContext),
          local_context_entities: localContext.activeEntities,
        },
      })

      // Generate response using function calling
      response = await this.groq.chat.completions.create({
        model: this.modelName,
        messages: [
          {
            role: 'user',
            content: prompt,
          },
        ],
        tools: [this.getExecutionPlanFunction()],
        tool_choice: 'required',
        temperature: 0.7,
        max_tokens: 1024,
      })

      generation?.end({
        output: JSON.stringify(response),
        usageDetails: {
          prompt_tokens: response.usage.prompt_tokens,
          completion_tokens: response.usage.completion_tokens,
          total_tokens: response.usage.total_tokens,
        },
        metadata: {
          route: currentRoute,
        },
      })

      this.log('✅ Received response:', response)

      // Extract function call result
      const toolCalls = response.choices[0]?.message?.tool_calls
      this.log('✅ Received tool calls:', toolCalls)

      if (!toolCalls || toolCalls.length === 0) {
        // Fallback if no function call was made
        const text = response.choices[0]?.message?.content || ''
        this.log('⚠️ No function call, using text response:', text)

        // Handle empty or invalid responses
        const responseText = text && text.trim()
          ? text.trim()
          : 'I didn\'t get it, try to repeat your request'

        const processedResponse = {
          text: responseText,
          executionPlan: [],
          userRequest,
          timestamp: Date.now(),
          context: {
            route: currentRoute,
            availableTools: this.getAvailableToolNames(localContext),
            activeEntities: localContext.activeEntities || [],
          },
        }

        this.setState('idle')
        this.emit('response', processedResponse)
        return processedResponse
      }

      // Process the function call result
      const toolCall = toolCalls[0]
      const functionArgs = JSON.parse(toolCall.function.arguments)

      this.log('📋 Function call arguments:', functionArgs)

      // Structure the response with execution plan
      const responseText = functionArgs.response_text && functionArgs.response_text.trim()
        ? functionArgs.response_text.trim()
        : 'I didn\'t get it, try to repeat your request'

      const processedResponse = {
        text: responseText,
        executionPlan: functionArgs.execution_plan || [],
        userRequest,
        timestamp: Date.now(),
        context: {
          route: currentRoute,
          availableTools: this.getAvailableToolNames(localContext),
          activeEntities: localContext.activeEntities || [],
        },
      }

      this.log('🎯 Generated execution plan:', processedResponse.executionPlan)

      this.setState('idle')
      this.emit('response', processedResponse)

      return processedResponse
    } catch (error) {
      if (generation) {
        generation.event({
          level: 'ERROR',
          output: error?.message,
          metadata: {
            error: JSON.stringify(error, Object.getOwnPropertyNames(error)),
          },
        })
      }

      console.error('[Groq] Error processing userRequest:', error)
      this.setState('error')
      this.emit('error', error)

      return {
        error: 'Failed to process userRequest',
        details: error.message,
      }
    }
  }

  /**
   * Get available tool names for context
   * @param {Object} localContext - Local context
   * @returns {Array} - Array of tool names
   */
  getAvailableToolNames (localContext) {
    const toolNames = []
    this.collectToolNames(localContext, toolNames)
    return toolNames
  }

  /**
   * Recursively collect tool names
   * @param {Object} component - Component to process
   * @param {Array} toolNames - Array to collect tool names
   */
  collectToolNames (component, toolNames) {
    if (component.tools) {
      toolNames.push(...Object.keys(component.tools))
    }

    if (component.children) {
      for (const child of Object.values(component.children)) {
        this.collectToolNames(child, toolNames)
      }
    }
  }

  /**
   * Set service state and emit state change event
   */
  setState (newState) {
    const oldState = this.state
    this.state = newState
    this.log(`State changed: ${oldState} -> ${newState}`)
    this.emit('stateChange', { oldState, newState })
  }

  /**
   * Get current service state
   */
  getState () {
    return this.state
  }

  /**
   * Check if service is currently processing
   */
  isProcessing () {
    return this.state === 'processing'
  }

  /**
   * Check if service is available
   */
  isAvailable () {
    return this.state !== 'error' && this.groq !== null
  }

  /**
   * Add event listener
   */
  addEventListener (event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback)
    }
  }

  /**
   * Remove event listener
   */
  removeEventListener (event, callback) {
    if (this.listeners[event]) {
      const index = this.listeners[event].indexOf(callback)
      if (index !== -1) {
        this.listeners[event].splice(index, 1)
      }
    }
  }

  /**
   * Emit event to all listeners
   */
  emit (event, data) {
    if (this.listeners[event]) {
      for (const callback of this.listeners[event]) {
        try {
          callback(data)
        } catch (error) {
          console.error(`[Groq] Error in ${event} listener:`, error)
        }
      }
    }
  }

  /**
   * Destroy service and clean up all resources
   */
  destroy () {
    // Clear all event listeners
    for (const event of Object.keys(this.listeners)) {
      this.listeners[event] = []
    }

    this.groq = null
    this.setState('idle')

    this.log('Service destroyed')
  }
}

// Export singleton instance
export const groqService = new GroqService()
