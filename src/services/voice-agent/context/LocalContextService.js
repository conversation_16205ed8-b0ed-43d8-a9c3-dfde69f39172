import { reactive } from 'vue'

// Local context tree structure
const localContextTree = reactive({
  root: {
    componentId: 'root',
    componentName: 'App',
    description: 'Root application component',
    tools: {},
    currentEntity: null,
    children: {},
  },
})

class LocalContextService {
  constructor () {
    this.tree = localContextTree
  }

  /**
   * Register a component in the local context tree
   * @param {Object} componentData - Component data
   * @param {string} componentData.componentId - Unique identifier for the component
   * @param {string} componentData.componentName - Vue component name
   * @param {string} componentData.description - Human-readable description of what this component does
   * @param {Object} componentData.tools - Functions that can be called by the voice agent
   * @param {Array} componentData.enhancedTools - Enhanced tool structure with metadata
   * @param {string} componentData.parent - Parent component ID (defaults to 'root')
   * @param {Object|null} componentData.currentEntity - Current entity being displayed/edited
   */
  register ({ componentId, componentName, description, tools = {}, enhancedTools = [], parent = 'root', currentEntity = null }) {
    const componentData = {
      componentId,
      componentName,
      description,
      tools,
      enhancedTools: enhancedTools || [], // Handle null/undefined case
      currentEntity,
      children: {},
    }

    // Find parent component
    const parentComponent = this.findComponentById(parent)
    if (parentComponent) {
      parentComponent.children[componentId] = componentData
      console.log(`[LocalContext] Registered ${componentName} (${componentId}) under ${parent}`)
    } else {
      console.warn(`[LocalContext] Parent component ${parent} not found, registering under root`)
      this.tree.root.children[componentId] = componentData
    }

    return componentData
  }

  /**
   * Deregister a component from the local context tree
   * @param {string} componentId - Component ID to remove
   */
  deregister (componentId) {
    const result = this.removeComponent(this.tree.root, componentId)
    if (result) {
      console.log(`[LocalContext] Deregistered component ${componentId}`)
    } else {
      console.warn(`[LocalContext] Component ${componentId} not found for deregistration`)
    }
  }

  /**
   * Find a component in the tree by ID
   * @param {string} componentId - Component ID to find
   * @returns {Object|null} - Found component or null
   */
  findComponentById (componentId, component = this.tree.root) {
    if (component.componentId === componentId) {
      return component
    }

    for (const child of Object.values(component.children)) {
      const found = this.findComponentById(componentId, child)
      if (found) {
        return found
      }
    }

    return null
  }

  /**
   * Find a component in the tree by name
   * @param {string} componentName - Component Name to find
   * @returns {Object|null} - Found component or null
   */
  findComponentByName (componentName, component = this.tree.root) {
    if (component.componentName === componentName) {
      return component
    }

    for (const child of Object.values(component.children)) {
      const found = this.findComponentByName(componentName, child)
      if (found) {
        return found
      }
    }

    return null
  }

  /**
   * Remove a component from the tree
   * @param {Object} component - Current component to search
   * @param {string} componentId - Component ID to remove
   * @returns {boolean} - Whether the component was found and removed
   */
  removeComponent (component, componentId) {
    if (component.children[componentId]) {
      delete component.children[componentId]
      return true
    }

    for (const child of Object.values(component.children)) {
      if (this.removeComponent(child, componentId)) {
        return true
      }
    }

    return false
  }

  /**
   * Update the current entity for a specific component
   * @param {string} componentId - Component ID
   * @param {string} description - Entity data with { type, id, data }
   */
  updateDescription (componentId, description) {
    const component = this.findComponentById(componentId)
    if (component) {
      component.description = description
      console.log(`[LocalContext] Updated description for ${componentId}:`, description)
    }
  }

  /**
   * Update the current entity for a specific component
   * @param {string} componentId - Component ID
   * @param {Object|null} entity - Entity data with { type, id, data }
   */
  updateCurrentEntity (componentId, entity) {
    const component = this.findComponentById(componentId)
    if (component) {
      component.currentEntity = entity
      console.log(`[LocalContext] Updated entity for ${componentId}:`, entity?.type, entity?.id)
    }
  }

  /**
   * Get all tools available in the current context
   * @returns {Object} - Flat object of all available tools
   */
  getAllTools () {
    const tools = {}
    this.collectTools(this.tree.root, tools)
    return tools
  }

  /**
   * Recursively collect all tools from the tree
   * @param {Object} component - Current component
   * @param {Object} tools - Tools object to populate
   */
  collectTools (component, tools) {
    // Add tools from current component
    for (const [toolName, toolFunction] of Object.entries(component.tools)) {
      const fullToolName = `${component.componentId}.${toolName}`
      tools[fullToolName] = {
        function: toolFunction,
        description: `${toolName} on ${component.componentName}`,
        componentId: component.componentId,
        componentName: component.componentName,
        currentEntity: component.currentEntity,
      }
    }

    // Recursively collect from children
    for (const child of Object.values(component.children)) {
      this.collectTools(child, tools)
    }
  }

  /**
   * Get all active entities in the current context
   * @returns {Array} - Array of active entities
   */
  getActiveEntities () {
    const entities = []
    this.collectEntities(this.tree.root, entities)
    return entities
  }

  /**
   * Recursively collect all entities from the tree
   * @param {Object} component - Current component
   * @param {Array} entities - Entities array to populate
   */
  collectEntities (component, entities) {
    if (component.currentEntity) {
      entities.push({
        componentId: component.componentId,
        componentName: component.componentName,
        entity: component.currentEntity,
      })
    }

    // Recursively collect from children
    for (const child of Object.values(component.children)) {
      this.collectEntities(child, entities)
    }
  }

  /**
   * Get a serializable representation of the local context tree for the LLM
   * @returns {Object} - Simplified tree structure
   */
  getContextForLLM () {
    return this.simplifyComponent(this.tree.root)
  }

  /**
   * Create a simplified version of a component for LLM consumption
   * @param {Object} component - Component to simplify
   * @returns {Object} - Simplified component
   */
  simplifyComponent (component) {
    return {
      componentId: component.componentId,
      componentName: component.componentName,
      description: component.description,
      availableTools: Object.keys(component.tools),
      tools: component.enhancedTools || [], // Enhanced tool structure for LLM
      currentEntity: component.currentEntity
        ? {
            type: component.currentEntity.type,
            id: component.currentEntity.id,
            // Include relevant data fields without full object
            summary: this.getEntitySummary(component.currentEntity),
          }
        : null,
      children: Object.values(component.children).map(child => this.simplifyComponent(child)),
    }
  }

  /**
   * Get a summary of entity data for LLM context
   * @param {Object} entity - Entity object
   * @returns {Object} - Entity summary
   */
  getEntitySummary (entity) {
    if (!entity || !entity.data) {
      return null
    }

    const { type, data } = entity

    switch (type) {
      case 'Company': {
        return {
          name: data.name,
          email: data.email,
          location: data.location,
          industry: data.industry,
        }
      }
      case 'User': {
        return {
          fullName: data.fullName || `${data.firstName} ${data.lastName}`,
          email: data.email,
          access: data.access,
          isActive: data.isActive,
        }
      }
      default: {
        return { id: entity.id }
      }
    }
  }

  /**
   * Get the current local context tree (for debugging)
   * @returns {Object} - The reactive local context tree
   */
  getTree () {
    return this.tree
  }

  /**
   * Clear all components except root (useful for debugging/testing)
   */
  clear () {
    this.tree.root.children = {}
    console.log('[LocalContext] Cleared all components')
  }

  /**
   * Get enhanced action plan structure for a specific component
   * Used by AgentCoreService when executing useTool actions
   * @param {string} componentId - Target component ID
   * @returns {Object|null} - Enhanced action plan structure or null if not found
   */
  getEnhancedActionStructure (componentId) {
    const component = this.findComponentById(componentId)
    if (!component) {
      return null
    }

    return {
      action: 'useTool',
      targetComponent: {
        name: component.componentName,
        id: component.componentId,
      },
      tools: component.enhancedTools || [],
    }
  }
}

// Export singleton instance
export const localContextService = new LocalContextService()
export { localContextTree }
