import { staticAppGraph } from './global-context.js'

class GlobalContextService {
  constructor () {
    this.globalContextTree = staticAppGraph
  }

  /**
   * Get page context by path
   * @param {string} path - The route path
   * @returns {Object|null} - Page configuration or null if not found
   */
  getPageContext (path) {
    return this.globalContextTree.pages.find(page => page.path === path) || null
  }

  /**
   * Get entity definition by type
   * @param {string} entityType - Entity type (e.g., 'Company', 'User')
   * @returns {Object|null} - Entity definition or null if not found
   */
  getEntityDefinition (entityType) {
    return this.globalContextTree.entities[entityType] || null
  }

  /**
   * Get all pages that work with a specific entity type
   * @param {string} entityType - Entity type
   * @returns {Array} - Array of pages that handle this entity
   */
  getPagesForEntity (entityType) {
    return this.globalContextTree.pages.filter(page =>
      page.entities.includes(entityType),
    )
  }

  /**
   * Get the complete static app graph
   * @returns {Object} - The full static app graph
   */
  getStaticAppGraph () {
    return this.globalContextTree
  }

  /**
   * Get context for LLM - simplified structure for reasoning
   * @param {string} currentPath - Current route path
   * @returns {Object} - Simplified context for LLM consumption
   */
  getContextForLLM (currentPath = null) {
    const currentPage = currentPath ? this.getPageContext(currentPath) : null

    return {
      currentPage,
      allPages: this.globalContextTree.pages.map(page => ({
        path: page.path,
        name: page.name,
        description: page.description,
        entities: page.entities,
        tools: page.tools || [],
      })),
      entities: this.globalContextTree.entities,
    }
  }
}

// Export singleton instance
export const globalContextService = new GlobalContextService()
