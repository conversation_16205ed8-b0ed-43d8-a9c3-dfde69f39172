import { accessList } from '@/constants/access'

export const staticAppGraph = {
  pages: [
    {
      path: '/',
      name: 'Dashboard',
      description: 'Main dashboard page showing overview of the application: Total Pipeline Value, Number of This Month Deals, Number of Active Contacts, Recent Activities List, Top User Performers List',
      entities: [],
    },
    {
      path: '/companies',
      name: 'Companies List',
      description: 'View and manage all companies in the system. Display company table with search and filtering capabilities.',
      entities: ['Company'],
      tools: [
        {
          targetComponent: {
            name: 'CompaniesManager',
          },
          name: 'search',
          description: 'Find company by name',
          params: [
            {
              name: 'query',
              type: 'string',
              required: true,
            },
          ],
        },
        {
          targetComponent: {
            name: 'CompaniesManager',
          },
          name: 'clearSearch',
          description: 'Clear search field and refresh data',
          params: [],
        },
        {
          targetComponent: {
            name: 'CompaniesManager',
          },
          name: 'openFirstRecord',
          description: 'Go to the "Company Details" page of the first company on the list. Can be used to open the first record after the search/filtration was executed',
          params: [],
        },
        {
          targetComponent: {
            name: 'CompaniesManager',
          },
          name: 'editFirstRecord',
          description: 'Go to the "Company Edit" page of the first company on the list',
          params: [],
        },
      ],
    },
    {
      path: '/company-details?id={companyId}',
      name: 'Company Details',
      description: 'View detailed information about a specific company including contact details, industry, and location.',
      entities: ['Company'],
      tools: [
        {
          targetComponent: {
            name: 'company-details',
          },
          name: 'goBack',
          description: 'Go to the "Companies List" page',
          params: [],
        },
        {
          targetComponent: {
            name: 'company-details',
          },
          name: 'goEdit',
          description: 'Go to the "Company Edit" page',
          params: [
            {
              targetComponent: {
                name: 'company-edit',
              },
              name: 'resetFormTool',
              description: 'Reset, restore, revert, undo, cancel ALL changes made in form fields',
            },
            {
              targetComponent: {
                name: 'company-edit',
              },
              name: 'resetField',
              description: 'Reset, restore, revert, undo, cancel a change in specific form field',
              params: [
                {
                  name: 'field',
                  type: 'string',
                  enum: ['name', 'email', 'phoneNumber', 'location', 'industry'],
                  required: true,
                },
              ],
            },
          ],
        },
      ],
    },
    {
      path: '/company-edit?id={companyId}',
      name: 'Company Edit',
      description: 'Edit and update company information including name, phone, email, location, and industry.',
      entities: ['Company'],
      tools: [
        {
          targetComponent: {
            name: 'company-edit',
          },
          name: 'changeField',
          description: 'Set company form field',
          params: [
            {
              name: 'name',
              type: 'string',
              required: true,
              values: ['name', 'email', 'phoneNumber', 'location', 'industry'],
            },
            {
              name: 'value',
              type: 'string',
              required: true,
            },
          ],
        },
        {
          targetComponent: {
            name: 'company-edit',
          },
          name: 'save',
          description: 'Save company form',
          params: [],
        },
      ],
    },
    {
      path: '/deals',
      name: 'Deals list',
      description: 'View deals list, get deal id, find deal, view deal, edit deal,',
      entities: ['Deal'],
      tools: [
        {
          targetComponent: {
            name: 'DealsManager',
          },
          name: 'search',
          description: 'Find deal by title',
          params: [
            {
              name: 'query',
              type: 'string',
              required: true,
            },
          ],
        },
        {
          targetComponent: {
            name: 'DealsManager',
          },
          name: 'clearSearch',
          description: 'Clear search field and refresh data',
          params: [],
        },
        {
          targetComponent: {
            name: 'DealsManager',
          },
          name: 'openFirstRecord',
          description: 'Go to the "Deal Details" page of the first deal on the list. Can be used to open the first record after the search/filtration was executed',
          params: [],
        },
        {
          targetComponent: {
            name: 'DealsManager',
          },
          name: 'editFirstRecord',
          description: 'Go to the "Deal Edit" page of the first deal on the list',
          params: [],
        },
        {
          targetComponent: {
            name: 'DealsManager',
          },
          name: 'setStatusFilter',
          description: 'Filter deals by status',
          params: [
            {
              name: 'status',
              type: 'string',
              required: false,
              values: ['identification', 'proposal', 'negotiation', 'closed won', 'closed lost'],
            },
          ],
        },
        {
          targetComponent: {
            name: 'DealsManager',
          },
          name: 'setPriorityFilter',
          description: 'Filter deals by priority level',
          params: [
            {
              name: 'priority',
              type: 'string',
              required: false,
              values: ['low', 'medium', 'high'],
            },
          ],
        },
        {
          targetComponent: {
            name: 'DealsManager',
          },
          name: 'setBudgetFilter',
          description: 'Filter deals by budget range',
          params: [
            {
              name: 'from',
              type: 'string',
              required: false,
            },
            {
              name: 'to',
              type: 'string',
              required: false,
            },
          ],
        },
        {
          targetComponent: {
            name: 'DealsManager',
          },
          name: 'setDealOwnerFilter',
          description: 'Filter deals by deal owner',
          params: [
            {
              name: 'ownerId',
              type: 'string',
              required: false,
            },
          ],
        },
        {
          targetComponent: {
            name: 'DealsManager',
          },
          name: 'clearAllFilters',
          description: 'Clear all active filters and search query on deals',
          params: [],
        },
        {
          targetComponent: {
            name: 'DealsManager',
          },
          name: 'showAllDeals',
          description: 'Show all deals by clearing both filters and search text',
          params: [],
        },
        {
          targetComponent: {
            name: 'DealsManager',
          },
          name: 'setSortBy',
          description: 'Sort deals by specified field. Can be used, for example, to quickly find the records with lower/highest budget',
          params: [
            {
              name: 'key',
              type: 'string',
              enum: ['title', 'status', 'priority', 'budget', 'createdAt'],
              required: true,
            },
            {
              name: 'order',
              type: 'string',
              enum: ['asc', 'desc'],
              required: false,
              default: 'asc',
            },
          ],
        },
      ],
    },
    {
      path: '/deal-details?id={dealId}',
      name: 'Deal Details',
      description: 'View deal details. Deal id required.',
      entities: ['Deal'],
      tools: [
        {
          targetComponent: {
            name: 'deal-details',
          },
          name: 'goBack',
          description: 'Go to the "Deals List" page',
          params: [],
        },
        {
          targetComponent: {
            name: 'deal-details',
          },
          name: 'goEdit',
          description: 'Go to the "Deal Edit" page',
          params: [
            {
              targetComponent: {
                name: 'deal-edit',
              },
              name: 'resetFormTool',
              description: 'Reset, restore, revert, undo, cancel ALL changes made in form fields',
            },
            {
              targetComponent: {
                name: 'deal-edit',
              },
              name: 'resetField',
              description: 'Reset, restore, revert, undo, cancel a change in specific form field',
              params: [
                {
                  name: 'field',
                  type: 'string',
                  enum: ['title', 'status', 'priority', 'budget'],
                  required: true,
                },
              ],
            },
          ],
        },
      ],
    },
    {
      path: '/deal-edit?id={dealId}',
      name: 'Deal Edit',
      description: 'Edit deal details. Deal id required',
      entities: ['Deal'],
      tools: [
        {
          targetComponent: {
            name: 'deal-edit',
          },
          name: 'changeField',
          description: 'Set deal form field',
          params: [
            {
              name: 'name',
              type: 'string',
              required: true,
              values: [
                'title',
                'status one of: [identification,proposal,negotiation,closed won,closed lost]',
                'priority one of: [low,medium,high]',
                'budget',
                'dealOwner',
                'company',
              ],
            },
            {
              name: 'value',
              type: 'string',
              required: true,
            },
          ],
        },
        {
          targetComponent: {
            name: 'deal-edit',
          },
          name: 'save',
          description: 'Save deal form',
          params: [],
        },
      ],
    },
    {
      path: '/contacts',
      name: 'Contacts list',
      description: 'View contacts list, get contact id, find contact, view contact, edit contact',
      entities: ['Contact'],
      tools: [
        {
          targetComponent: {
            name: 'ContactsManager',
          },
          name: 'search',
          description: 'Find contact by name, email, phone number, related company name',
          params: [
            {
              name: 'query',
              type: 'string',
              required: true,
            },
          ],
        },
        {
          targetComponent: {
            name: 'ContactsManager',
          },
          name: 'clearSearch',
          description: 'Clear search field and refresh data',
          params: [],
        },
        {
          targetComponent: {
            name: 'ContactsManager',
          },
          name: 'openFirstRecord',
          description: 'Go to the "Contact Details" page of the first contact on the list. Can be used to open the first record after the search/filtration was executed',
          params: [],
        },
        {
          targetComponent: {
            name: 'ContactsManager',
          },
          name: 'editFirstRecord',
          description: 'Go to the "Contact Edit" page of the first contact on the list',
          params: [],
        },
      ],
    },
    {
      path: '/contact-details?id={contactId}',
      name: 'Contact Details',
      description: 'View contact details. Contact id required.',
      entities: ['Contact'],
      tools: [
        {
          targetComponent: {
            name: 'contact-details',
          },
          name: 'goBack',
          description: 'Go to the "Contacts List" page',
          params: [],
        },
        {
          targetComponent: {
            name: 'contact-details',
          },
          name: 'goEdit',
          description: 'Go to the "Contact Edit" page where you can change contact details, assign it to another company or owner',
          params: [
            {
              targetComponent: {
                name: 'contact-edit',
              },
              name: 'resetFormTool',
              description: 'Reset, restore, revert, undo, cancel ALL changes made in form fields',
            },
            {
              targetComponent: {
                name: 'contact-edit',
              },
              name: 'resetField',
              description: 'Reset, restore, revert, undo, cancel a change in specific form field',
              params: [
                {
                  name: 'field',
                  type: 'string',
                  enum: ['firstName', 'lastName', 'email', 'phoneNumber', 'company', 'contactOwner'],
                  required: true,
                },
              ],
            },
          ],
        },
        {
          targetComponent: {
            name: 'ActivityList',
          },
          name: 'showAddActivity',
          description: 'Show add activity form modal',
          params: [],
        },
        {
          targetComponent: {
            name: 'ActivityCreateDialog',
          },
          name: 'setActivityNote',
          description: 'Set contact activity note',
          params: [
            {
              name: 'value',
              type: 'string',
              required: true,
            },
          ],
        },
        {
          targetComponent: {
            name: 'ActivityCreateDialog',
          },
          name: 'save',
          description: 'Save new contact activity',
          params: [],
        },
      ],
    },
    {
      path: '/contact-edit?id={contactId}',
      name: 'Contact Edit',
      description: 'Edit contact details. Contact id required',
      entities: ['Contact'],
      tools: [
        {
          targetComponent: {
            name: 'contact-edit',
          },
          name: 'changeField',
          description: 'Set contact form field',
          params: [
            {
              name: 'name',
              type: 'string',
              required: true,
              values: ['firstName', 'lastName', 'email', 'phoneNumber', 'company', 'contactOwner'],
            },
            {
              name: 'value',
              type: 'string',
              required: true,
            },
          ],
        },
        {
          targetComponent: {
            name: 'contact-edit',
          },
          name: 'save',
          description: 'Save contact form',
          params: [],
        },
      ],
    },
    {
      path: '/reports',
      name: 'Reports',
      description: 'Application reports page. Display stats/charts/tables about deals',
      entities: ['Deal'],
      tools: [],
    },
    {
      path: '/settings',
      name: 'Settings',
      description: 'Application settings and configuration, including users (employees) management (list, edit name, email, access, is active state).',
      entities: ['User'],
      tools: [
        {
          targetComponent: {
            name: 'UserTable',
          },
          name: 'openFirstRecord',
          description: 'Show edit/details user record of the first user on the list',
          params: [],
          tools: [
            {
              targetComponent: {
                name: 'UserEditDialog',
              },
              name: 'setAccess',
              description: 'Set selected user access value.',
              params: [
                {
                  type: 'array',
                  description: `Array of values, supported values: [${accessList.map(item => item.value).join(',')}]`,
                  items: {
                    enum: accessList.map(item => item.value),
                  },
                  required: true,
                },
              ],
            },
            {
              targetComponent: {
                name: 'UserEditDialog',
              },
              name: 'toggleUserIsActiveState',
              description: 'Set selected user "is active" value; To deactivate/disable user - set boolean value "false"; To activate/enable user - set boolean value "true"',
              params: [
                {
                  name: 'value',
                  type: 'boolean',
                  required: true,
                },
              ],
            },
            {
              targetComponent: {
                name: 'UserEditDialog',
              },
              name: 'changeField',
              description: 'Set selected user form field',
              params: [
                {
                  name: 'field',
                  type: 'string',
                  enum: ['firstName', 'lastName', 'email', 'isActive'],
                  required: true,
                },
                {
                  name: 'value',
                  type: 'string',
                  required: true,
                },
              ],
            },
            {
              targetComponent: {
                name: 'UserEditDialog',
              },
              name: 'resetFormTool',
              description: 'Reset, restore, revert, undo, cancel ALL changes made in form fields',
            },
            {
              targetComponent: {
                name: 'UserEditDialog',
              },
              name: 'resetField',
              description: 'Reset, restore, revert, undo, cancel a change in specific form field',
              params: [
                {
                  name: 'field',
                  type: 'string',
                  enum: ['firstName', 'lastName', 'email', 'access', 'isActive'],
                  required: true,
                },
              ],
            },
          ],
        },
      ],
    },
  ],
  entities: {
    Company: {
      description: 'Business entity representing companies in the system',
      fields: ['name', 'email', 'phoneNumber', 'logo', 'location', 'industry'],
    },
    User: {
      description: 'User entity representing employees in the organization',
      fields: ['firstName', 'lastName', 'email', 'avatar', 'access', 'isActive'],
    },
    Deal: {
      description: 'Deal entity',
      fields: ['title', 'budget', 'status', 'company', 'priority', 'owner'],
    },
    Contact: {
      description: 'Contact entity',
      fields: ['email', 'phone', 'company', 'owner'],
    },
    Activity: {
      description: 'Contact activity entity',
      field: ['id', 'note', 'creationDateTime', 'contact', 'activityCreator'],
    },
  },
}
