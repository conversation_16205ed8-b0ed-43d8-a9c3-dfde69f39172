import { Langfuse } from 'langfuse'
import { userId } from '@/utils/user'

export class BaseLlmService {
  /**
   * Class constructor
   * @param {string} name - LLM name
   */
  constructor (name) {
    this.name = name
    if (
      import.meta.env.VITE_LANGFUSE_SECRET_KEY
        && import.meta.env.VITE_LANGFUSE_PUBLIC_KEY
        && import.meta.env.VITE_LANGFUSE_BASE_URL
    ) {
      this.langFuse = new Langfuse({
        secretKey: import.meta.env.VITE_LANGFUSE_SECRET_KEY,
        publicKey: import.meta.env.VITE_LANGFUSE_PUBLIC_KEY,
        baseUrl: import.meta.env.VITE_LANGFUSE_BASE_URL,
      })
    }
  }

  getLangFuseTrace () {
    if (!this.langFuseTrace && this.langFuse) {
      this.langFuseTrace = this.langFuse.trace({
        name: `${this.name} LLM`,
        userId: userId(),
      })
    }
    return this.langFuseTrace
  }

  /**
   * Build context-aware prompt for LLM
   * @param {string} userRequest - User's request text
   * @param {string[]} userRequestHistory - User's speech or text request history
   * @param {Object} globalContext - Global app context
   * @param {Object} localContext - Local component context
   * @param {string} currentRoute - Current route
   * @returns {string} - Complete prompt with context
   */
  buildContextPrompt (userRequest, userRequestHistory, globalContext, localContext, currentRoute) {
    // Build detailed tools information
    const availableToolsInfo = this.buildToolsInformation(localContext)

    const systemPrompt = `You are Sentient UI Assistant, a helpful AI for a voice-controlled CRM application.

Note:
- You help users navigate and use the application through natural language commands.
- Try to be as autonomous as possible. For example, a user asked you to open company profile using company's name, you do not tell the user to run search before that.
You check available tools and use them to get the execution plan that will provide the user with the requested result.

IMPORTANT: You MUST use the generateExecutionPlan function to respond. Always provide both response_text and execution_plan.

CURRENT APPLICATION STATE:
Current Page: ${currentRoute}
Page Info: ${JSON.stringify(globalContext.currentPage)}

AVAILABLE PAGES FOR NAVIGATION:
${JSON.stringify(globalContext.allPages.map(page => ({
  path: page.path,
  name: page.name,
  description: page.description,
  tools: page.tools,
})))}

ENTITY DEFINITIONS:
${JSON.stringify(globalContext.entities)}

ACTIVE COMPONENTS AND TOOLS:
${availableToolsInfo}

ACTIVE ENTITIES (Currently being viewed/edited):
${localContext.activeEntities ? JSON.stringify(localContext.activeEntities) : 'None'}

EXECUTION PLAN RULES:
1. For navigation requests, use: {"action": "navigate", "payload": {"path": "/route"}}
2. For tool usage, use: {"action": "useTool", "payload": {"targetComponent": {"name": "ComponentName", "id": "component_id"}, "tool": "toolName", "params": ["param1", "param2"]}}
3. execution_plan can be empty [] if no actions are needed (just conversational response)
4. Always provide helpful response_text explaining what you understand and what you're doing
5. If changes or revert/undo/reset in specific record's field are required (e.g., 'Please add Admin permissions to Michael', 'Change phone number to +123456',
  'Update priority to High', 'Revert changes in this profile') run a save-related tool (e.g. 'save') automatically (to apply the made changes).
6. Do NOT auto-save the activity note during the creation. Add everything user dictates to the activity note's textarea field. Wait for explicit command to save.
7. If you asked to update some record/field and there is an update page/dialog, do NOT forget to navigate/open this update page/dialog before proposing a tool for modification.
  For example, the deal details page is opened and an app users asks to "Set its budget to 90,000", in the execution plan you must navigate to the
  appropriate page (deal-edit page) before calling the "changeField" tool. Note that this rule should be applied only if there is a update page/dialog available.
8. When operating with numbers (e.g., "Change budget to 90,000") do NOT use digit separator (like , or .). Return just the plain number.

EXAMPLES:
- "go to companies" → {"action": "navigate", "payload": {"path": "/companies"}}
- "search for Acme" → {"action": "useTool", "payload": {"targetComponent": {...}, "tool": "search", "params": ["Acme"]}}
- "edit this company" → {"action": "useTool", "payload": {"targetComponent": {...}, "tool": "edit", "params": []}}
- "what can I do here?" → empty execution_plan, just helpful response_text

Use "user request history" to understand what the user is trying to do in the "user request", for example:
- user request history: ["Go to"] and user request: "Companies" → "Go to companies"
- user request history: ["Go to", "Companies"] and user request: "Search" → "Search ..."
- user request history: ["Go to", "Companies", "Search"] and user request: "Acme" → "Search Acme"
- user request history: ["Go to", "Companies", "Search", "Acme"] and user request: "Clear" → "Clear search"

USER REQUEST HISTORY: ${JSON.stringify(userRequestHistory)}

USER REQUEST: "${userRequest}"`

    this.log('SYSTEM PROMPT', systemPrompt)

    return systemPrompt
  }

  /**
   * Build detailed tools information for the LLM
   * @param {Object} localContext - Local context with component tools
   * @returns {string} - Formatted tools information
   */
  buildToolsInformation (localContext) {
    if (!localContext || !localContext.children) {
      return 'No active components with tools available.'
    }

    const toolsInfo = []

    // Process each component in the local context tree
    this.processComponentTools(localContext, toolsInfo)

    if (toolsInfo.length === 0) {
      return 'No active components with tools available.'
    }

    return toolsInfo.join('\n\n')
  }

  /**
   * Recursively process component tools
   * @param {Object} component - Component to process
   * @param {Array} toolsInfo - Array to collect tool information
   */
  processComponentTools (component, toolsInfo) {
    // Add tools from current component
    if (component.tools && Object.keys(component.tools).length > 0) {
      const componentInfo = `Component: ${component.componentName} (ID: ${component.componentId})
Description: ${component.description}
Available Tools: ${JSON.stringify(component.tools, null, 2)}`
      toolsInfo.push(componentInfo)
    }

    // Process children components
    if (component.children) {
      for (const child of Object.values(component.children)) {
        this.processComponentTools(child, toolsInfo)
      }
    }
  }

  /**
   * @param {string} message - Log message
   * @param {any} optionalParams - Log data
   */
  log (message, ...optionalParams) {
    console.log(`[${this.name}] ${message}:`, ...optionalParams)
  }
}
