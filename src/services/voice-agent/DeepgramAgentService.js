/**
 * DeepgramAgentService - Voice agent using Deepgram Agent API
 *
 * This service handles:
 * - Real-time speech-to-text transcription
 * - AI-powered conversation through Deepgram Agent
 * - Raw PCM audio streaming at 24kHz
 * - Agent responses and conversation flow
 */

import { AgentEvents, createClient } from '@deepgram/sdk'
import { AudioDebugger } from './utils/AudioDebugger'
import { AudioProcessor } from './utils/AudioProcessor'

export class DeepgramAgentService {
  constructor () {
    // Service state
    this.state = 'idle' // 'idle', 'connecting', 'listening', 'error'
    this.client = null
    this.connection = null
    this.mediaStream = null
    this.audioProcessor = new AudioProcessor()
    this.audioDebugger = new AudioDebugger()

    // Debug logging timing
    this.lastAudioLog = 0
    this.isTranscribing = false // Track if we're currently in a transcription cycle

    // Event listeners
    this.listeners = {
      stateChange: [],
      transcription: [],
      transcribingStart: [], // New event for when transcription starts
      error: [],
      audioDebug: [], // New event for audio debugging
    }

    // Connect AudioDebugger events to our event system
    this.audioDebugger.addEventListener(debugInfo => {
      this.emit('audioDebug', debugInfo)
    })

    // Configuration - Agent API with conversation flow
    this.apiKey = import.meta.env.VITE_DEEPGRAM_API_KEY
    this.agentConfig = {
      model: 'nova-2',
      language: 'en-US',
      encoding: 'linear16',
      sample_rate: 24_000,
      channels: 1,
      multichannel: false,
      interim_results: true,
      punctuate: true,
      smart_format: true,
      utterance_end_ms: 2000,
      vad_events: true,
      // Agent-specific configuration
      agent: {
        listen: {
          model: 'nova-2',
          smart_format: true,
          punctuate: true,
        },
        think: {
          provider: {
            type: 'open_ai_compatible',
          },
          model: 'meta-llama/llama-3.1-8b-instruct',
          instructions: 'You are a helpful assistant for a voice-controlled CRM application. Keep responses brief and actionable.',
        },
        speak: {
          model: 'aura-asteria-en',
        },
      },
    }

    // Initialize Deepgram client
    this.initializeClient()
  }

  /**
   * Initialize Deepgram client
   */
  initializeClient () {
    if (!this.apiKey) {
      console.error('[DeepgramAgent] API key not configured. Please set VITE_DEEPGRAM_API_KEY in .env file')
      this.setState('error')
      return
    }

    try {
      this.client = createClient(this.apiKey)
      console.log('[DeepgramAgent] Client initialized successfully')
    } catch (error) {
      console.error('[DeepgramAgent] Failed to initialize client:', error)
      this.setState('error')
    }
  }

  /**
   * Start listening with Agent API
   */
  async startListening () {
    if (this.state === 'listening') {
      console.warn('[DeepgramAgent] Already listening')
      return
    }

    if (this.state === 'error' || !this.client) {
      console.error('[DeepgramAgent] Service not available')
      return
    }

    try {
      this.setState('connecting')
      console.log('[DeepgramAgent] Starting agent conversation...')

      // Clear previous audio debug data for new session
      if (this.audioDebugger.isEnabled()) {
        this.audioDebugger.resetSession()
        console.log('[DeepgramAgent] 🎛️ Audio debugging active - capturing audio pipeline data')
      }

      // Reset transcription state for new session
      this.isTranscribing = false

      // Get audio stream
      const stream = await this.getAudioStream()
      if (!stream) {
        throw new Error('Failed to get audio stream')
      }
      this.mediaStream = stream

      // Create Agent connection
      console.log('[DeepgramAgent] Creating Agent connection...')
      this.connection = this.client.agent(this.agentConfig)

      // Set up event listeners
      this.setupAgentListeners()

      // Start audio processing
      await this.startAudioStreaming()

      console.log('[DeepgramAgent] Agent conversation started - waiting for connection...')
    } catch (error) {
      console.error('[DeepgramAgent] Failed to start conversation:', error)
      this.emit('error', error)
      this.setState('error')
      this.cleanup()
    }
  }

  /**
   * Stop listening
   */
  async stopListening () {
    if (this.state === 'idle') {
      return
    }

    console.log('[DeepgramAgent] Stopping conversation')

    try {
      // Close Agent connection
      if (this.connection) {
        this.connection.finish()
      }

      // Clean up resources
      this.cleanup()

      // Reset transcription state for next session
      this.isTranscribing = false

      this.setState('idle')

      console.log('[DeepgramAgent] Conversation stopped')
    } catch (error) {
      console.error('[DeepgramAgent] Error stopping conversation:', error)
      this.setState('error')
    }
  }

  /**
   * Get audio stream from user's microphone
   */
  async getAudioStream () {
    try {
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('getUserMedia not supported in this browser')
      }

      const constraints = this.audioProcessor.getMicrophoneConstraints()
      return await navigator.mediaDevices.getUserMedia(constraints)
    } catch (error) {
      console.error('[DeepgramAgent] Failed to get audio stream:', error)

      if (error.name === 'NotAllowedError') {
        this.emit('error', new Error('Microphone permission denied'))
      } else if (error.name === 'NotFoundError') {
        this.emit('error', new Error('No microphone found'))
      } else {
        this.emit('error', error)
      }

      return null
    }
  }

  /**
   * Set up Agent event listeners
   */
  setupAgentListeners () {
    if (!this.connection) {
      console.error('[DeepgramAgent] No connection to set up listeners')
      return
    }

    console.log('[DeepgramAgent] Setting up Agent event listeners...')

    // Connection opened
    this.connection.on(AgentEvents.Open, () => {
      console.log('[DeepgramAgent] ✅ Agent Connection opened')
      this.setState('listening')
    })

    // User transcription (what the user said)
    this.connection.on(AgentEvents.ConversationText, data => {
      if (data.type === 'user_message') {
        const transcript = data.text

        if (transcript && transcript.trim()) {
          console.log(`[DeepgramAgent] 🎤 User transcription: ${transcript}`)

          // Reset transcription state to allow timing for next speech segment
          this.isTranscribing = false

          // Emit transcription event (compatible with VoiceAgentCoordinator)
          this.emit('transcription', {
            text: transcript,
            timestamp: Date.now(),
          })
        }
      } else if (data.type === 'agent_message') {
        // Agent response - we might want to handle this differently
        console.log(`[DeepgramAgent] 🤖 Agent response: ${data.text}`)
        // Note: This could be used for agent responses if needed
      }
    })

    // Utterance end events
    this.connection.on(AgentEvents.UtteranceEnd, () => {
      console.log('[DeepgramAgent] 🎤 Utterance ended')
    })

    // Error handling
    this.connection.on(AgentEvents.Error, error => {
      console.error('[DeepgramAgent] ❌ Agent error:', error)
      this.emit('error', error)
      this.setState('error')
    })

    // Connection closed
    this.connection.on(AgentEvents.Close, () => {
      console.log('[DeepgramAgent] Agent Connection closed')
      if (this.state !== 'idle') {
        this.setState('idle')
      }
    })

    console.log('[DeepgramAgent] Agent event listeners set up successfully')
  }

  /**
   * Start streaming audio to Agent using AudioProcessor
   */
  async startAudioStreaming () {
    if (!this.mediaStream || !this.connection) {
      console.error('[DeepgramAgent] Cannot start audio streaming - missing dependencies')
      return
    }

    try {
      console.log('[DeepgramAgent] Starting high-quality audio streaming...')

      // Start audio processing with callbacks
      await this.audioProcessor.startProcessing(
        this.mediaStream,
        audioData => this.handleProcessedAudio(audioData),
        error => this.handleAudioError(error),
      )

      console.log('[DeepgramAgent] High-quality audio streaming started')
    } catch (error) {
      console.error('[DeepgramAgent] Failed to start audio streaming:', error)
      this.emit('error', error)
    }
  }

  /**
   * Handle processed audio data from AudioProcessor
   */
  handleProcessedAudio (audioData) {
    if (!this.connection || this.state === 'idle') {
      return
    }

    const { original, resampled, pcm, inputSampleRate } = audioData

    // Debug: Log audio reception (throttled every 3 seconds)
    this.throttledLog('📡 Received audio', `${original.length}→${resampled.length}→${pcm.length} samples, sending ${pcm.buffer.byteLength} bytes`, 3000)

    // Debug: Capture audio data at each stage
    if (this.audioDebugger.isEnabled()) {
      this.audioDebugger.captureRawAudio(original, inputSampleRate)
      this.audioDebugger.captureResampledAudio(resampled)
      this.audioDebugger.capturePcmAudio(pcm)
    }

    // Send PCM data to Deepgram Agent
    try {
      this.connection.send(pcm.buffer)

      // Detect meaningful speech and start transcription timing
      const rms = this.audioProcessor.calculateRms(original)
      if (!this.isTranscribing && rms > 0.005) { // Threshold for detecting speech
        this.isTranscribing = true
        this.emit('transcribingStart')
        console.log('[DeepgramAgent] 🎤 Speech detected - starting transcription timing')
      }

      // Debug: Track sent bytes and emit debug info
      if (this.audioDebugger.isEnabled()) {
        this.audioDebugger.trackSentBytes(pcm.buffer.byteLength)
        this.audioDebugger.emitAudioDebugInfo(original, resampled, pcm)
      }
    } catch (error) {
      console.error('[DeepgramAgent] Error sending audio:', error)
    }
  }

  /**
   * Handle audio processing errors
   */
  handleAudioError (error) {
    console.error('[DeepgramAgent] Audio processing error:', error)
    this.emit('error', error)
    this.setState('error')
  }

  /**
   * Clean up resources
   */
  cleanup () {
    // Stop audio processing via AudioProcessor
    if (this.audioProcessor) {
      this.audioProcessor.stop()
    }

    // Stop media stream tracks
    if (this.mediaStream) {
      for (const track of this.mediaStream.getTracks()) {
        track.stop()
      }
      this.mediaStream = null
    }

    // Close Agent connection
    if (this.connection) {
      this.connection.removeAllListeners()
      this.connection = null
    }
  }

  /**
   * Set service state and emit state change event
   */
  setState (newState) {
    const oldState = this.state
    this.state = newState
    console.log(`[DeepgramAgent] State changed: ${oldState} -> ${newState}`)
    this.emit('stateChange', { oldState, newState })
  }

  /**
   * Get current service state
   */
  getState () {
    return this.state
  }

  /**
   * Check if service is currently listening
   */
  isListening () {
    return this.state === 'listening'
  }

  /**
   * Check if service is available
   */
  isAvailable () {
    return this.state !== 'error' && this.client !== null
  }

  /**
   * Add event listener
   */
  addEventListener (event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback)
    }
  }

  /**
   * Remove event listener
   */
  removeEventListener (event, callback) {
    if (this.listeners[event]) {
      const index = this.listeners[event].indexOf(callback)
      if (index !== -1) {
        this.listeners[event].splice(index, 1)
      }
    }
  }

  /**
   * Emit event to all listeners
   */
  emit (event, data) {
    if (this.listeners[event]) {
      for (const callback of this.listeners[event]) {
        try {
          callback(data)
        } catch (error) {
          console.error(`[DeepgramAgent] Error in ${event} listener:`, error)
        }
      }
    }
  }

  /**
   * Enable/disable audio debugging
   */
  setAudioDebugging (enabled) {
    this.audioDebugger.setDebugging(enabled)
  }

  /**
   * Get audio debugging stats
   */
  getAudioStats () {
    return {
      ...this.audioDebugger.getAudioStats(),
      isRecording: this.state === 'listening',
    }
  }

  /**
   * Get raw audio samples for analysis
   */
  getRawAudioSamples (count = 1000) {
    return this.audioDebugger.getRawAudioSamples(count)
  }

  /**
   * Get resampled audio samples for analysis
   */
  getResampledAudioSamples (count = 1000) {
    return this.audioDebugger.getResampledAudioSamples(count)
  }

  /**
   * Get PCM audio samples (what's sent to Deepgram)
   */
  getPcmAudioSamples (count = 1000) {
    return this.audioDebugger.getPcmAudioSamples(count)
  }

  /**
   * Create downloadable audio file from recording buffer
   */
  downloadRecordedAudio () {
    this.audioDebugger.downloadRecordedAudio()
  }

  /**
   * Clear audio debugging data
   */
  clearAudioDebugData () {
    this.audioDebugger.clearAudioDebugData()
  }

  /**
   * Utility for throttled debug logging
   */
  throttledLog (prefix, message, intervalMs = 3000) {
    const now = Date.now()
    if (!this.lastAudioLog || now - this.lastAudioLog > intervalMs) {
      console.log(`[DeepgramAgent] ${prefix}: ${message}`)
      this.lastAudioLog = now
    }
  }

  /**
   * Destroy service and clean up all resources
   */
  destroy () {
    this.stopListening()
    this.cleanup()

    // Clear all event listeners
    for (const event of Object.keys(this.listeners)) {
      this.listeners[event] = []
    }

    this.client = null
    this.setState('idle')

    console.log('[DeepgramAgent] Service destroyed')
  }
}

// Export singleton instance
export const deepgramAgentService = new DeepgramAgentService()
