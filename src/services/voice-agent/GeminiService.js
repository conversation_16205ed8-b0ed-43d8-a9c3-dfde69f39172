/**
 * GeminiService - Gemini Flash integration with context awareness
 *
 * This service handles:
 * - Processing user requests from DeepgramService
 * - Context-aware prompting with global/local trees
 * - Gemini API integration with configurable models
 * - Response processing and action extraction
 */

import { GoogleGenAI, Type } from '@google/genai'
import { BaseLlmService } from './BaseLlmService'

export class GeminiService extends BaseLlmService {
  constructor () {
    super('Gemini')

    // Service state
    this.state = 'idle' // 'idle', 'processing', 'error'
    this.model = null

    // Event listeners
    this.listeners = {
      stateChange: [],
      response: [],
      error: [],
    }

    // Configuration
    this.apiKey = import.meta.env.VITE_GEMINI_API_KEY
    this.modelName = import.meta.env.VITE_GEMINI_MODEL || 'gemini-2.5-flash'

    // Initialize Gemini client
    this.initializeClient()
  }

  /**
   * Get function declaration for structured execution plan generation
   */
  getExecutionPlanFunction () {
    return {
      name: 'generateExecutionPlan',
      description: 'Generate a structured execution plan with response text and actionable steps for user requests in the Sentient UI CRM application',
      parameters: {
        type: Type.OBJECT,
        properties: {
          response_text: {
            type: Type.STRING,
            description: 'Friendly, helpful response text to display to the user explaining what you understand and what you\'re doing',
          },
          execution_plan: {
            type: Type.ARRAY,
            description: 'Array of action steps to execute in order. Can be empty if no actions are needed.',
            items: {
              type: Type.OBJECT,
              properties: {
                action: {
                  type: Type.STRING,
                  enum: ['navigate', 'useTool'],
                  description: 'Type of action: \'navigate\' for route changes, \'useTool\' for component interactions',
                },
                payload: {
                  type: Type.OBJECT,
                  description: 'Action-specific data. Contains \'path\' for navigate actions, or \'targetComponent\', \'tool\', \'params\' for useTool actions',
                  properties: {
                    path: {
                      type: Type.STRING,
                      description: 'Route path for navigation actions (e.g., \'/companies\', \'/settings\')',
                    },
                    targetComponent: {
                      type: Type.OBJECT,
                      properties: {
                        name: {
                          type: Type.STRING,
                          description: 'Component name (e.g., \'CompanyTable\', \'UserEditDialog\')',
                        },
                        id: {
                          type: Type.STRING,
                          description: 'Component ID from local context',
                        },
                      },
                      description: 'Target component for useTool actions',
                    },
                    tool: {
                      type: Type.STRING,
                      description: 'Tool name to execute on the target component',
                    },
                    params: {
                      type: Type.ARRAY,
                      description: 'Parameters to pass to the tool function',
                      items: {
                        type: Type.STRING,
                      },
                    },
                  },
                },
              },
              required: ['action'],
            },
          },
        },
        required: ['response_text', 'execution_plan'],
      },
    }
  }

  /**
   * Initialize Gemini client
   */
  initializeClient () {
    if (!this.apiKey) {
      console.error('[Gemini] Gemini API key not configured. Please set VITE_GEMINI_API_KEY in .env file')
      this.setState('error')
      return
    }

    try {
      this.model = new GoogleGenAI({ apiKey: this.apiKey })
      this.log('Client initialized successfully with function calling')
    } catch (error) {
      console.error('[Gemini] Failed to initialize Gemini client:', error)
      this.setState('error')
    }
  }

  /**
   * Process user request with context-aware Gemini
   * @param {string} userRequest - User's speech or text request
   * @param {string[]} userRequestHistory - User's speech or text request history
   * @param {string} currentRoute - Current application route
   * @param {Object} globalContext - Global application context
   * @param {Object} localContext - Local component context
   * @returns {Promise<Object>} - Gemini response with text and potential actions
   */
  async processUserRequest (userRequest, userRequestHistory, currentRoute = '/', globalContext, localContext) {
    if (!this.model) {
      console.error('[Gemini] Service not available')
      return { error: 'LLM service not available' }
    }

    let generation, response
    try {
      this.setState('processing')
      this.log('Processing user request:', userRequest)

      // Build context-aware prompt using provided contexts
      const prompt = this.buildContextPrompt(userRequest, userRequestHistory, globalContext, localContext, currentRoute)

      this.log('Sending request to Gemini...')

      generation = this.getLangFuseTrace()?.generation({
        name: 'chat-completion',
        model: this.modelName,
        modelParameters: {
          temperature: 0.7,
          topK: 40,
          topP: 0.95,
          maxOutputTokens: 1024,
        },
        input: {
          user_request: userRequest,
          user_request_history: userRequestHistory,
          local_context: this.buildToolsInformation(localContext),
          local_context_entities: localContext.activeEntities,
        },
      })

      // Generate response using function calling
      response = await this.model.models.generateContent({
        model: this.modelName,
        contents: prompt,
        config: {
          generationConfig: {
            temperature: 0.7,
            topK: 40,
            topP: 0.95,
            maxOutputTokens: 1024,
          },
          tools: [{ functionDeclarations: [this.getExecutionPlanFunction()] }],
        },
      })

      generation?.end({
        output: JSON.stringify(response),
        usageDetails: {
          prompt_tokens: response.usageMetadata.promptTokenCount,
          completion_tokens: response.usageMetadata.candidatesTokenCount + response.usageMetadata.thoughtsTokenCount,
          total_tokens: response.usageMetadata.totalTokenCount,
        },
        metadata: {
          route: currentRoute,
        },
      })

      // Extract function call result
      const functionCalls = response.functionCalls
      this.log('✅ Received function calls:', functionCalls)

      if (!functionCalls || functionCalls.length === 0) {
        // Fallback if no function call was made
        const text = response.text
        this.log('⚠️ No function call, using text response:', text)

        // Handle empty or invalid responses
        const responseText = text && text.trim()
          ? text.trim()
          : 'I didn\'t get it, try to repeat your request'

        const processedResponse = {
          text: responseText,
          executionPlan: [],
          userRequest,
          timestamp: Date.now(),
          context: {
            route: currentRoute,
            availableTools: this.getAvailableToolNames(localContext),
            activeEntities: localContext.activeEntities || [],
          },
        }

        this.setState('idle')
        this.emit('response', processedResponse)
        return processedResponse
      }

      // Process the function call result
      const functionCall = functionCalls[0]
      const functionArgs = functionCall.args

      this.log('📋 Function call arguments:', functionArgs)

      // Structure the response with execution plan
      const responseText = functionArgs.response_text && functionArgs.response_text.trim()
        ? functionArgs.response_text.trim()
        : 'I didn\'t get it, try to repeat your request'

      const processedResponse = {
        text: responseText,
        executionPlan: functionArgs.execution_plan || [],
        userRequest,
        timestamp: Date.now(),
        context: {
          route: currentRoute,
          availableTools: this.getAvailableToolNames(localContext),
          activeEntities: localContext.activeEntities || [],
        },
      }

      this.log('🎯 Generated execution plan:', processedResponse.executionPlan)

      this.setState('idle')
      this.emit('response', processedResponse)

      return processedResponse
    } catch (error) {
      if (generation) {
        generation.event({
          level: 'ERROR',
          output: error?.message,
          metadata: {
            error: JSON.stringify(error, Object.getOwnPropertyNames(error)),
          },
        })
      }

      console.error('[Gemini] Error processing userRequest:', error)
      this.setState('error')
      this.emit('error', error)

      return {
        error: 'Failed to process userRequest',
        details: error.message,
      }
    }
  }

  /**
   * Get available tool names for context
   * @param {Object} localContext - Local context
   * @returns {Array} - Array of tool names
   */
  getAvailableToolNames (localContext) {
    const toolNames = []
    this.collectToolNames(localContext, toolNames)
    return toolNames
  }

  /**
   * Recursively collect tool names
   * @param {Object} component - Component to process
   * @param {Array} toolNames - Array to collect tool names
   */
  collectToolNames (component, toolNames) {
    if (component.tools) {
      toolNames.push(...Object.keys(component.tools))
    }

    if (component.children) {
      for (const child of Object.values(component.children)) {
        this.collectToolNames(child, toolNames)
      }
    }
  }

  /**
   * Set service state and emit state change event
   */
  setState (newState) {
    const oldState = this.state
    this.state = newState
    this.log(`[Gemini] State changed: ${oldState} -> ${newState}`)
    this.emit('stateChange', { oldState, newState })
  }

  /**
   * Get current service state
   */
  getState () {
    return this.state
  }

  /**
   * Check if service is currently processing
   */
  isProcessing () {
    return this.state === 'processing'
  }

  /**
   * Check if service is available
   */
  isAvailable () {
    return this.state !== 'error' && this.model !== null
  }

  /**
   * Add event listener
   */
  addEventListener (event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback)
    }
  }

  /**
   * Remove event listener
   */
  removeEventListener (event, callback) {
    if (this.listeners[event]) {
      const index = this.listeners[event].indexOf(callback)
      if (index !== -1) {
        this.listeners[event].splice(index, 1)
      }
    }
  }

  /**
   * Emit event to all listeners
   */
  emit (event, data) {
    if (this.listeners[event]) {
      for (const callback of this.listeners[event]) {
        try {
          callback(data)
        } catch (error) {
          console.error(`[Gemini] Error in ${event} listener:`, error)
        }
      }
    }
  }

  /**
   * Destroy service and clean up all resources
   */
  destroy () {
    // Clear all event listeners
    for (const event of Object.keys(this.listeners)) {
      this.listeners[event] = []
    }

    this.model = null
    this.setState('idle')

    this.log('Service destroyed')
  }
}

// Export singleton instance
export const geminiService = new GeminiService()
