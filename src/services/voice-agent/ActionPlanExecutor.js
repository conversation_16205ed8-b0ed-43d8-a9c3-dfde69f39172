import { sleep } from 'groq-sdk/core'
import router from '@/router'
import { localContextService } from './context/LocalContextService'

/**
 * ActionPlanExecutor - Executes action plans from voice agent
 *
 * This service receives JSON action plans and executes them by calling
 * component tools or triggering navigation. Supports both immediate
 * execution and queue-based background processing.
 */
export class ActionPlanExecutor {
  constructor () {
    this.localContextService = localContextService
    this.isExecuting = false
    this.currentPlan = null
    this.executionLog = []

    // Queue management
    this.planQueue = []
    this.isStarted = false
    this.processingLoopActive = false

    // Event listeners
    this.listeners = {
      planComplete: [],
      error: [],
    }
  }

  /**
   * Start the service with background processing loop
   */
  start () {
    if (this.isStarted) {
      console.warn('[ActionPlan] Service already started')
      return
    }

    this.isStarted = true
    this.startProcessingLoop()
    console.log('[ActionPlan] Service started with background processing')
  }

  /**
   * Stop the service and clear queue
   */
  stop () {
    this.isStarted = false
    this.processingLoopActive = false
    this.planQueue = []
    console.log('[ActionPlan] Service stopped')
  }

  /**
   * Add a plan to the execution queue
   * @param {Array} plan - Action plan to queue
   * @param {string} priority - Priority level ('high', 'normal', 'low')
   * @returns {string} - Queue ID for tracking
   */
  queuePlan (plan, priority = 'normal') {
    const queueItem = {
      id: `plan_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`,
      plan,
      priority,
      timestamp: Date.now(),
      status: 'queued',
    }

    this.planQueue.push(queueItem)

    // Sort by priority (high -> normal -> low)
    this.planQueue.sort((a, b) => {
      const priorityOrder = { high: 3, normal: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })

    console.log(`[ActionPlan] Plan queued with priority ${priority}:`, queueItem.id)
    return queueItem.id
  }

  /**
   * Get current queue status
   * @returns {Object} - Queue information
   */
  getQueueStatus () {
    return {
      isStarted: this.isStarted,
      queueLength: this.planQueue.length,
      isProcessing: this.isExecuting,
      currentPlan: this.currentPlan,
      nextPlan: this.planQueue[0] || null,
    }
  }

  /**
   * Start the background processing loop
   */
  async startProcessingLoop () {
    if (this.processingLoopActive) {
      return
    }

    this.processingLoopActive = true

    while (this.isStarted && this.processingLoopActive) {
      try {
        if (this.planQueue.length > 0 && !this.isExecuting) {
          const queueItem = this.planQueue.shift()
          queueItem.status = 'executing'

          console.log(`[ActionPlan] Processing queued plan:`, queueItem.id)
          await this.executePlan(queueItem.plan)
        }

        // Small delay to prevent busy waiting
        await new Promise(resolve => setTimeout(resolve, 50))
      } catch (error) {
        console.error('[ActionPlan] Error in processing loop:', error)
        // Continue processing even if one plan fails
      }
    }

    this.processingLoopActive = false
  }

  /**
   * Execute a complete action plan (immediate execution)
   * @param {Array} plan - Array of action steps to execute
   * @returns {Promise<Object>} - Execution result with success/failure info
   */
  async executePlan (plan = []) {
    if (this.isExecuting) {
      console.warn('[ActionPlan] Already executing a plan. Use queuePlan() for queued execution.')
      return { success: false, error: 'Another plan is currently executing. Use queuePlan() to add to queue.' }
    }

    this.isExecuting = true
    this.currentPlan = plan
    this.executionLog = []

    console.group('[ActionPlan] Starting plan execution', { planSize: plan.length })

    try {
      for (let i = 0; i < plan.length; i++) {
        const step = plan[i]
        console.log(`[ActionPlan] Executing step ${i + 1}/${plan.length}:`, step)

        const stepResult = await this.executeStep(step)
        this.executionLog.push({
          stepIndex: i,
          step,
          result: stepResult,
          timestamp: Date.now(),
        })

        // @TODO: Some operations (like loading items in table) are not completed when we start the next execution plan step
        // (like selecting the first record)
        await sleep(300)

        if (!stepResult.success) {
          console.error(`[ActionPlan] Step ${i + 1} failed:`, stepResult.error)
          // Continue with remaining steps or stop here based on error severity
          if (stepResult.critical) {
            break
          }
        }
      }

      const result = {
        success: true,
        executedSteps: this.executionLog.length,
        totalSteps: plan.length,
        log: this.executionLog,
      }

      console.log('[ActionPlan] Plan execution completed:', result)
      this.emit('planComplete', { planId: plan.id || 'unknown', result })
      return result
    } catch (error) {
      const result = {
        success: false,
        error: error.message,
        executedSteps: this.executionLog.length,
        totalSteps: plan.length,
        log: this.executionLog,
      }

      console.error('[ActionPlan] Plan execution failed:', result)
      return result
    } finally {
      this.isExecuting = false
      this.currentPlan = null
      console.groupEnd()
    }
  }

  /**
   * Execute a single step of the plan
   * @param {Object} step - The step to execute
   * @returns {Promise<Object>} - Step execution result
   */
  async executeStep (step) {
    try {
      switch (step.action) {
        case 'navigate': {
          return await this.navigateTo(step.payload?.path)
        }

        case 'useTool': {
          return await this.useTool(step)
        }

        default: {
          return {
            success: false,
            error: `Unsupported action type: ${step.action}`,
            critical: false,
          }
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        critical: false,
      }
    }
  }

  /**
   * Navigate to a specific route
   * @param {string} path - The route path to navigate to
   * @returns {Promise<Object>} - Navigation result
   */
  async navigateTo (path) {
    if (!path) {
      return {
        success: false,
        error: 'Navigation path is required',
        critical: false,
      }
    }

    try {
      console.log(`[ActionPlan] Navigating to: ${path}`)
      await router.push(path)

      return {
        success: true,
        action: 'navigate',
        path,
        message: `Successfully navigated to ${path}`,
      }
    } catch (error) {
      return {
        success: false,
        error: `Navigation failed to ${path}: ${error.message}`,
        critical: false,
      }
    }
  }

  /**
   * Execute a tool on a specific component
   * @param {Object} step - The tool execution step
   * @returns {Promise<Object>} - Tool execution result
   */
  async useTool (step) {
    // Support both new payload structure and legacy structure for backward compatibility
    const payloadData = step.payload || step
    const { targetComponent, tool, params = [] } = payloadData

    console.log('[ActionPlan] Processing useTool with structure:', step.payload ? 'new payload format' : 'legacy format')
    console.log('[ActionPlan] Extracted data:', { targetComponent, tool, params })

    if (!targetComponent || !tool) {
      return {
        success: false,
        error: 'targetComponent and tool are required for useTool action',
        critical: false,
      }
    }

    if (!targetComponent.id && !targetComponent.name) {
      return {
        success: false,
        error: 'targetComponent doesn\'t include identifier',
        critical: false,
      }
    }

    // Find the target component in the local context tree
    const component = await this.getComponent(targetComponent)

    if (!component) {
      return {
        success: false,
        error: `Component with ID '${targetComponent.id}' or Name '${targetComponent.name}' not found in local context`,
        critical: false,
      }
    }

    // Check if the tool exists on the component
    const toolFunction = component.tools?.[tool]
    const targetComponentCombinedName = `${targetComponent.name}-${targetComponent.id}`
    if (typeof toolFunction !== 'function') {
      return {
        success: false,
        error: `Tool '${tool}' not found on component '${targetComponentCombinedName}'. Available tools: ${Object.keys(component.tools || {}).join(', ')}`,
        critical: false,
      }
    }

    try {
      console.log(`[ActionPlan] Executing tool '${tool}' on '${targetComponentCombinedName}' with params:`, params)
      const toolResult = await toolFunction(...params)

      return {
        success: true,
        action: 'useTool',
        targetComponentCombinedName,
        tool,
        params,
        result: toolResult,
        message: `Successfully executed ${tool} on ${targetComponentCombinedName}`,
      }
    } catch (error) {
      return {
        success: false,
        error: `Error executing tool '${tool}' on '${targetComponentCombinedName}': ${error.message}`,
        critical: false,
      }
    }
  }

  /**
   * Get local context component by ID or name, retry N times until component mounted
   * @param {string} id
   * @param {string} name
   * @param {number} retry
   * @returns {Promise<Object>}
   */
  async getComponent ({ id, name }, retry = 10) {
    let component = null
    for (let i = 0; i < retry; i++) {
      component = id ? this.localContextService.findComponentById(id) : this.localContextService.findComponentByName(name)
      if (component) {
        return component
      }
      await new Promise(resolve => setTimeout(resolve, 100))
    }
  }

  /**
   * Add event listener
   */
  addEventListener (event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback)
    }
  }

  /**
   * Remove event listener
   */
  removeEventListener (event, callback) {
    if (this.listeners[event]) {
      const index = this.listeners[event].indexOf(callback)
      if (index !== -1) {
        this.listeners[event].splice(index, 1)
      }
    }
  }

  /**
   * Emit event to all listeners
   */
  emit (event, data) {
    if (this.listeners[event]) {
      for (const callback of this.listeners[event]) {
        try {
          callback(data)
        } catch (error) {
          console.error(`[ActionPlan] Error in ${event} listener:`, error)
        }
      }
    }
  }
}

// Export singleton instance
export const actionPlanExecutor = new ActionPlanExecutor()
