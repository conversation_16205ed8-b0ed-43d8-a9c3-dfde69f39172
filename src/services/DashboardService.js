import { ActivityRepository } from '@/repositories/ActivityRepository'
import { CompanyRepository } from '@/repositories/CompanyRepository'
import { ContactRepository } from '@/repositories/ContactRepository'
import { DealRepository } from '@/repositories/DealRepository'
import { UserRepository } from '@/repositories/UserRepository'

export class DashboardService {
  constructor () {
    this.contactRepository = new ContactRepository()
    this.dealRepository = new DealRepository()
    this.activityRepository = new ActivityRepository()
    this.companyRepository = new CompanyRepository()
    this.userRepository = new UserRepository()
  }

  async initializeData () {
    await Promise.all([
      this.contactRepository.seedData(),
      this.dealRepository.seedData(),
      this.activityRepository.seedData(),
      this.companyRepository.seedData(),
      this.userRepository.seedData(),
    ])
  }

  async getKPIMetrics () {
    const [deals, contacts, activities] = await Promise.all([
      this.dealRepository.getAll(),
      this.contactRepository.getAll(),
      this.activityRepository.getAll(),
    ])

    // Calculate total pipeline value
    const totalPipelineValue = deals.reduce((sum, deal) => sum + (Number(deal.budget) || 0), 0)

    // Count deals closing this month (deals in negotiation status)
    const dealsClosingThisMonth = deals.filter(deal => deal.status === 'negotiation').length

    // Count active contacts
    const activeContacts = contacts.length

    // Count recent activities (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    const recentActivities = activities.filter(activity =>
      new Date(activity.creationDateTime) >= thirtyDaysAgo,
    ).length

    return {
      totalPipelineValue,
      dealsClosingThisMonth,
      activeContacts,
      recentActivities,
    }
  }

  async getDealPipelineData () {
    const deals = await this.dealRepository.getAll()

    const pipelineData = deals.reduce((acc, deal) => {
      const status = deal.status || 'unknown'
      if (!acc[status]) {
        acc[status] = { count: 0, value: 0 }
      }
      acc[status].count += 1
      acc[status].value += Number(deal.budget) || 0
      return acc
    }, {})

    // Convert to array format for charts
    return Object.entries(pipelineData).map(([status, data]) => ({
      status: status.charAt(0).toUpperCase() + status.slice(1),
      count: data.count,
      value: data.value,
    }))
  }

  async getActivityTimelineData () {
    const activities = await this.activityRepository.getAll()

    // Group activities by month (last 6 months)
    const timelineData = {}
    const today = new Date()

    for (let i = 5; i >= 0; i--) {
      // Use Date.UTC to avoid timezone issues
      const date = new Date(Date.UTC(today.getUTCFullYear(), today.getUTCMonth() - i, 1))
      const monthKey = date.toISOString().split('T')[0].slice(0, 7) // YYYY-MM format
      timelineData[monthKey] = 0
    }

    for (const activity of activities) {
      const activityDate = new Date(activity.creationDateTime)
      const monthKey = activityDate.toISOString().split('T')[0].slice(0, 7)
      if (timelineData.hasOwnProperty(monthKey)) {
        timelineData[monthKey] += 1
      }
    }

    return Object.entries(timelineData).map(([month, count]) => ({
      date: month,
      count,
      formattedDate: new Date(month + '-01').toLocaleDateString('en-US', {
        month: 'short',
      }),
    }))
  }

  async getTopPerformers () {
    const [deals, activities, users] = await Promise.all([
      this.dealRepository.getAll(),
      this.activityRepository.getAll(),
      this.userRepository.getAll(),
    ])

    const userMap = new Map(users.map(user => [user.id, user]))
    const performanceData = {}

    // Count deals by owner
    for (const deal of deals) {
      const ownerId = deal.dealOwner
      if (!performanceData[ownerId]) {
        performanceData[ownerId] = { deals: 0, activities: 0, value: 0 }
      }
      performanceData[ownerId].deals += 1
      performanceData[ownerId].value += Number(deal.budget) || 0
    }

    // Count activities by creator
    for (const activity of activities) {
      const creatorId = activity.activityCreator
      if (!performanceData[creatorId]) {
        performanceData[creatorId] = { deals: 0, activities: 0, value: 0 }
      }
      performanceData[creatorId].activities += 1
    }

    // Convert to array and sort by total activity
    return Object.entries(performanceData)
      .map(([userId, data]) => ({
        user: userMap.get(userId) || { fullName: 'Unknown User', id: userId },
        deals: data.deals,
        activities: data.activities,
        value: data.value,
        score: data.deals * 2 + data.activities, // Weight deals higher
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 4) // Top 4 performers
  }

  async getRecentActivityFeed () {
    const [activities, contacts, users] = await Promise.all([
      this.activityRepository.getAll(),
      this.contactRepository.getAll(),
      this.userRepository.getAll(),
    ])

    const contactMap = new Map(contacts.map(contact => [contact.id, contact]))
    const userMap = new Map(users.map(user => [user.id, user]))

    // Get recent activities (last 5)
    const recentActivities = activities
      .sort((a, b) => new Date(b.creationDateTime) - new Date(a.creationDateTime))
      .slice(0, 5)
      .map(activity => ({
        type: 'activity',
        id: activity.id,
        title: `${userMap.get(activity.activityCreator)?.fullName || 'Unknown'} added activity to `,
        subtitle: activity.note.slice(0, 100) + (activity.note.length > 100 ? '...' : ''),
        contact: contactMap.get(activity.contact),
        timestamp: activity.creationDateTime,
        icon: 'mdi-note-text',
      }))

    return recentActivities
  }
}
