// Storage abstraction interfaces
export class IStorageService {
  async get (key) {
    throw new Error('Method must be implemented')
  }

  async set (key, value) {
    throw new Error('Method must be implemented')
  }

  async remove (key) {
    throw new Error('Method must be implemented')
  }

  async clear () {
    throw new Error('Method must be implemented')
  }
}

// LocalStorage implementation
export class LocalStorageService extends IStorageService {
  async get (key) {
    try {
      const value = localStorage.getItem(key)
      return value ? JSON.parse(value) : null
    } catch (error) {
      console.error('Error getting from localStorage:', error)
      return null
    }
  }

  async set (key, value) {
    try {
      localStorage.setItem(key, JSON.stringify(value))
      return true
    } catch (error) {
      console.error('Error setting to localStorage:', error)
      return false
    }
  }

  async remove (key) {
    try {
      localStorage.removeItem(key)
      return true
    } catch (error) {
      console.error('Error removing from localStorage:', error)
      return false
    }
  }

  async clear () {
    try {
      localStorage.clear()
      return true
    } catch (error) {
      console.error('Error clearing localStorage:', error)
      return false
    }
  }
}
