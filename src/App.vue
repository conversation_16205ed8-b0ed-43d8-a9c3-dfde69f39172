<template>
  <v-app>
    <AppBar @toggle-drawer="drawer = !drawer" />
    <NavigationDrawer v-model="drawer" />
    <v-main class="main-content">
      <router-view />
    </v-main>
    <VoiceAgentPanel />
  </v-app>
</template>

<script setup>
  import { ref } from 'vue'
  import VoiceAgentPanel from '@/components/voice-agent/VoiceAgentPanel.vue'

  const drawer = ref(false)
</script>

<style scoped>
.main-content {
  margin-right: 350px; /* Accommodate voice agent panel */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .main-content {
    margin-right: 300px;
  }
}

@media (max-width: 480px) {
  .main-content {
    margin-right: 280px;
  }
}
</style>
