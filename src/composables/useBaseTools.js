export function useBaseTools (component) {
  /**
   * Set current page
   *
   * @param {number} value
   * @returns {{success: boolean, message: string}}
   */
  const setPage = value => {
    if (component.value.setCurrentPage) {
      component.value?.setCurrentPage(value)
      return {
        success: true,
        message: `Show page number: ${value}`,
      }
    }
  }

  const nextPage = () => {
    if (component.value?.nextPage) {
      component.value?.nextPage()
      return {
        success: true,
        message: `Show next page`,
      }
    }
    return {
      success: false,
      message: `Failed to show next page`,
    }
  }

  const previousPage = () => {
    if (component.value?.previousPage) {
      component.value.previousPage()
      return {
        success: true,
        message: `Show previous page`,
      }
    }
    return {
      success: false,
      message: `Failed to show previous page`,
    }
  }

  const setSortBy = {
    factory: () => ({
      name: 'setSortBy',
      description: 'Sort records list',
      params: [
        {
          name: 'key',
          type: 'string',
          enum: component.value?.headers
            ? component.value?.headers.filter(h => h.sortable === true || h.sortable === undefined).map(h => h.key)
            : undefined,
          required: true,
        },
        {
          name: 'order',
          type: 'string',
          enum: ['asc', 'desc'],
          required: false,
          default: 'asc',
        },
      ],
    }),
    tool: (key, order) => {
      if (component.value?.setSortBy) {
        component.value.setSortBy([{ key, order: order?.toLowerCase() === 'desc' ? 'desc' : 'asc' }])
        return {
          success: true,
          message: `Set sort by`,
        }
      }
      return {
        success: true,
        message: `Failed to set sort by`,
      }
    },
  }

  const setItemsPerPage = {
    name: 'setItemsPerPage',
    description: 'Set the number of displayed items on the page. Show all items, use: -1',
    params: [
      {
        name: 'value',
        type: 'number',
        required: true,
      },
    ],
    tool: value => {
      if (component.value?.setPerPage) {
        value = Number(value)
        component.value.setPerPage(value)
        return {
          success: true,
          message: `Set items per page ${value}`,
        }
      }
      return {
        success: true,
        message: `Failed to set items per page`,
      }
    },
  }

  return {
    setPage,
    nextPage,
    previousPage,
    setSortBy,
    setItemsPerPage,
  }
}
