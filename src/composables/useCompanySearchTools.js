import { useBaseTools } from '@/composables/useBaseTools'

/**
 * Composable for company search tools used by voice agent
 * Encapsulates the logic for search and navigation operations
 *
 * @param {Object} tableRef - Ref to CompanyTable component
 * @param {Object} searchQueryRef - Ref to searchQuery from parent
 * @returns {Object} - Tools for voice agent integration
 */
export function useCompanySearchTools (tableRef, searchQueryRef) {
  const baseTools = useBaseTools(tableRef)

  /**
   * Search for companies by query
   * Updates the search field and triggers the existing UI flow
   *
   * @param {string} query - Search term
   * @returns {Promise<Object>} - Result object with success status
   */
  const search = async query => {
    console.log('[SearchTools:Company] search() called with query:', query)

    if (!query || typeof query !== 'string') {
      const result = {
        success: false,
        error: 'Search query must be a non-empty string',
        query,
      }
      console.log('[SearchTools:Company] search validation failed:', result)
      return result
    }

    const startTime = Date.now()
    try {
      console.log('[SearchTools:Company] starting search operation for:', query.trim())
      // Update the search query (this triggers existing reactive flow)
      searchQueryRef.value = query.trim()

      // Wait for the search to complete
      await tableRef.value.loadingPromise

      const resultsCount = tableRef.value?.items.length || 0
      const duration = Date.now() - startTime

      const result = {
        success: true,
        query: query.trim(),
        resultsCount,
        message: resultsCount > 0
          ? `Found ${resultsCount} company${resultsCount === 1 ? '' : 'ies'} matching "${query}"`
          : `No companies found matching "${query}"`,
      }

      console.log('[SearchTools:Company] search completed in', duration + 'ms:', { success: true, resultsCount, query: query.trim() })
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      const result = {
        success: false,
        error: `Search failed: ${error.message}`,
        query,
      }
      console.error('[SearchTools:Company] search failed after', duration + 'ms:', error.message)
      return result
    }
  }

  /**
   * Open the first company from current search results
   * @returns {Promise<Object>} - Result object with success status
   */
  const openFirstRecord = async () => {
    console.log('[SearchTools:Company] openFirstRecord() called')
    return processFirstRecord('open')
  }

  /**
   * Edit the first company from current search results
   * @returns {Promise<Object>} - Result object with success status
   */
  const editFirstRecord = async () => {
    console.log('[SearchTools:Company] editFirstRecord() called')
    return processFirstRecord('edit')
  }

  /**
   * Open/Edit the first company from current search results
   * @param {string} action
   * @returns {Promise<Object>} - Result object with success status
   */
  const processFirstRecord = async action => {
    console.log('[SearchTools:Company] processFirstRecord() called with action:', action)

    try {
      if (!tableRef.value) {
        const result = {
          success: false,
          error: 'Company table not available',
        }
        console.error('[SearchTools:Company] processFirstRecord failed:', result.error)
        return result
      }

      await tableRef.value.loadingPromise

      const firstRecord = tableRef.value.getFirstRecord()
      if (!firstRecord) {
        const currentQuery = searchQueryRef.value
        const result = {
          success: false,
          error: 'No companies available to open',
          message: currentQuery
            ? `No companies found for search "${currentQuery}"`
            : 'No companies in the list',
        }
        console.log('[SearchTools:Company] processFirstRecord - no companies available:', { currentQuery, action })
        return result
      }

      console.log('[SearchTools:Company] processFirstRecord - found company:', { companyName: firstRecord.name, companyId: firstRecord.id, action })

      // Use the CompanyTable's viewCompany function to navigate
      if (action === 'edit') {
        tableRef.value.edit(firstRecord)
        console.log('[SearchTools:Company] navigating to edit company:', firstRecord.name)
      } else {
        tableRef.value.view(firstRecord)
        console.log('[SearchTools:Company] navigating to view company:', firstRecord.name)
      }

      const result = {
        success: true,
        companyName: firstRecord.name,
        companyId: firstRecord.id,
        message: `${action.toUpperCase()} ${firstRecord.name}`,
      }

      console.log('[SearchTools:Company] processFirstRecord completed successfully:', result)
      return result
    } catch (error) {
      const result = {
        success: false,
        error: `Failed to ${action} company: ${error.message}`,
      }
      console.error('[SearchTools:Company] processFirstRecord error:', error.message, { action })
      return result
    }
  }

  return {
    ...baseTools,
    search,
    openFirstRecord,
    editFirstRecord,
  }
}
