import { getCurrentInstance, inject, onMounted, onUnmounted, provide, watch } from 'vue'
import { localContextService } from '@/services/voice-agent/context/LocalContextService.js'

/**
 * useVoiceAgent - Composable for registering components with the voice agent system
 *
 * @param {Object} config - Configuration object
 * @param {string} config.description - Human-readable description of what this component does
 * @param {Object} config.tools - Functions that the voice agent can call
 * @param {Object|null} config.currentEntity - Current entity being displayed/edited
 * @param {string} config.componentId - Optional custom component ID (auto-generated if not provided)
 *
 * @returns {Object} - Object containing the tools and utility functions
 */
export function useVoiceAgent ({
  description,
  tools = {},
  currentEntity = null,
  componentId: customComponentId = undefined,
}) {
  const instance = getCurrentInstance()

  // Generate unique component ID and get component name automatically
  const componentName = instance.type.__name
  const componentId = customComponentId || `${componentName}_${instance.uid}`

  // Let child components know my ID (for building the tree hierarchy)
  provide('voiceComponentId', componentId)

  // Find my parent's ID from the component hierarchy
  const parentComponentId = inject('voiceComponentId', 'root')

  // Shared registration logic
  const performRegistration = () => {
    console.log(`[VoiceAgent] Registering ${componentName} with componentId: ${componentId}`)

    // Process tools into enhanced structure with metadata
    const enhancedTools = processEnhancedTools(tools)

    // Reactive wrapper for tools to ensure they're always current
    const wrappedTools = {}
    for (let [toolName, toolFunction] of Object.entries(tools)) {
      if (typeof toolFunction === 'object') {
        toolFunction = toolFunction.tool
      }
      wrappedTools[toolName] = (...args) => {
        console.log(`[VoiceAgent] Calling ${toolName} on ${componentId}`, args)
        return toolFunction(...args)
      }
    }

    localContextService.register({
      componentId,
      componentName,
      description,
      tools: wrappedTools,
      enhancedTools, // New enhanced structure for LLM consumption
      parent: parentComponentId,
      currentEntity,
    })
  }

  // Register with the local context service on mount
  onMounted(() => {
    console.log(`[VoiceAgent] Mounting ${componentName} with componentId: ${componentId}`)
    performRegistration()
  })

  // Clean up on unmount
  onUnmounted(() => {
    console.log(`[VoiceAgent] Unmounting ${componentName} with componentId: ${componentId}`)
    localContextService.deregister(componentId)
  })

  const setDescription = description => {
    localContextService.updateDescription(componentId, description)
  }

  // Utility function to update the current entity
  const setCurrentEntity = entity => {
    localContextService.updateCurrentEntity(componentId, entity)
  }

  // Utility function to update the current entity with type and data
  const setEntity = (entityType, entityData) => {
    if (entityData && entityData.id) {
      const entity = {
        type: entityType,
        id: entityData.id,
        data: entityData,
      }
      setCurrentEntity(entity)
    } else {
      setCurrentEntity(null)
    }
  }

  // Manual deregister function for cases where component needs to be removed from context before unmount
  const deregister = () => {
    console.log(`[VoiceAgent] Manually deregistering ${componentName} with componentId: ${componentId}`)
    localContextService.deregister(componentId)
  }

  // Manual register function for re-registering after deregister
  const register = () => {
    console.log(`[VoiceAgent] Manually registering ${componentName} with componentId: ${componentId}`)
    performRegistration()
  }

  // Return the tools and utility functions
  return {
    componentId,
    componentName,
    setDescription,
    setCurrentEntity,
    setEntity,
    deregister,
    register,
    // For debugging
    getLocalContextTree: () => localContextService.getTree(),
    getAllTools: () => localContextService.getAllTools(),
    getActiveEntities: () => localContextService.getActiveEntities(),
  }
}

/**
 * Process tools into enhanced structure with metadata for LLM consumption
 * @param {Object} tools - Original tools object
 * @returns {Array} - Array of enhanced tool objects
 */
function processEnhancedTools (tools) {
  return Object.entries(tools).map(([name, func]) => {
    if (typeof func === 'object') {
      const { factory, ...properties } = func
      return factory ? factory() : { ...properties, tool: undefined }
    }
    const metadata = extractToolsMetadata(func)
    return {
      name,
      description: metadata.description || `Execute ${name} action`,
      params: metadata.params || [],
    }
  })
}

/**
 * Extract metadata from tool functions for LLM consumption
 * @param {Function} func - Tool function to analyze
 * @returns {Object} - Extracted metadata
 */
function extractToolsMetadata (func) {
  // Get function parameter names from function signature
  const funcStr = func.toString()

  // Match different function patterns:
  // 1. Traditional functions: function(param1, param2) {}
  // 2. Arrow functions with parens: (param1, param2) => {}
  // 3. Arrow functions without parens: param1 => {}
  // 4. Async functions: async (param1) => {} or async function(param1) {}

  let paramStr = ''

  // Try to match arrow function without parentheses first
  const arrowSingleParamMatch = funcStr.match(/(?:async\s+)?(\w+)\s*=>/)
  if (arrowSingleParamMatch) {
    paramStr = arrowSingleParamMatch[1]
  } else {
    // Try to match functions with parentheses
    const paramMatch = funcStr.match(/(?:function\s*\w*\s*|(?:async\s+)?)?\(([^)]*)\)/)
    paramStr = paramMatch ? paramMatch[1] : ''
  }

  // Parse parameter names (basic extraction)
  const params = paramStr
    ? paramStr.split(',')
        .map(param => param.trim())
        .filter(param => param && !param.includes('...'))
        .map(param => {
          // Remove default values and destructuring
          const cleanParam = param.split('=')[0].trim().replace(/[{}]/g, '').trim()
          return {
            name: cleanParam,
            type: 'string', // Default type, could be enhanced with JSDoc parsing
            required: !param.includes('='),
          }
        })
    : []

  // Try to extract JSDoc description (basic implementation)
  const jsdocMatch = funcStr.match(/\/\*\*[\s\S]*?\*\//)
  let description = ''
  if (jsdocMatch) {
    const jsdocLines = jsdocMatch[0].split('\n')
    const descriptionLine = jsdocLines.find(line =>
      line.includes('*') && !line.includes('@') && line.trim().length > 2,
    )
    if (descriptionLine) {
      description = descriptionLine.replace(/\/\*\*|\*\/|\*/g, '').trim()
    }
  }

  return {
    description,
    params,
  }
}

/**
 * Helper composable for pages that manage specific entities
 * Automatically sets the entity context when entity data changes
 *
 * @param {string} entityType - Type of entity (e.g., 'Company', 'User')
 * @param {Object} config - useVoiceAgent configuration
 * @param {Object} entityRef - Vue ref containing the entity data
 */
export function useEntityVoiceAgent (entityType, config, entityRef = null) {
  const voiceAgent = useVoiceAgent(config)

  // Watch entity changes and update context automatically
  if (entityRef) {
    watch(
      entityRef,
      newEntity => {
        if (newEntity) {
          voiceAgent.setEntity(entityType, newEntity)
        } else {
          voiceAgent.setCurrentEntity(null)
        }
      },
      { immediate: true, deep: true },
    )
  }

  // Helper function to manually set entity (if not using entityRef)
  const setEntity = entityData => {
    voiceAgent.setEntity(entityType, entityData)
  }

  return {
    ...voiceAgent,
    setEntity,
  }
}
