import { ref } from 'vue'

export function usePagination (repository, sortKey = 'name', sortOrder = 'asc') {
  const loading = ref(false)
  const loadingPromise = ref(Promise.resolve())
  const searchQuery = ref('')
  const filters = ref({})
  const currentPage = ref(1)
  const lastPage = ref(1)
  const itemsTotal = ref(1)
  const perPage = ref(10)
  const sortBy = ref([{ key: sortKey, order: sortOrder }])
  const items = ref([])

  const baseLoadItems = async () => {
    try {
      if (!repository) {
        return
      }
      loading.value = true
      await repository.seedData()

      const response = await repository.paginate(
        currentPage.value,
        perPage.value,
        sortBy.value[0]?.key,
        sortBy.value[0]?.order,
        searchQuery.value,
        filters.value,
      )
      items.value = response.data
      itemsTotal.value = response.total
      lastPage.value = response.pages
    } catch (error) {
      console.error('Error loading items:', error)
    } finally {
      loading.value = false
    }
  }

  const loadItems = async () => {
    loadingPromise.value = baseLoadItems()
    return loadingPromise.value
  }

  const setCurrentPage = value => {
    currentPage.value = value
    void loadItems()
  }

  const setPerPage = value => {
    perPage.value = value
    void loadItems()
  }

  const nextPage = () => {
    if (lastPage.value > currentPage.value) {
      setCurrentPage(currentPage.value + 1)
    }
  }

  const previousPage = () => {
    if (currentPage.value > 1) {
      setCurrentPage(currentPage.value - 1)
    }
  }

  const setSortBy = value => {
    sortBy.value = value
    void loadItems()
  }

  const setSearchQuery = value => {
    searchQuery.value = value
    currentPage.value = 1
    void loadItems()
  }

  const setFilters = value => {
    filters.value = value || {}
    currentPage.value = 1
    void loadItems()
  }

  return {
    items,
    itemsTotal,
    loading,
    loadingPromise,
    currentPage,
    lastPage,
    perPage,
    sortBy,
    filters,
    loadItems,
    setCurrentPage,
    setPerPage,
    nextPage,
    previousPage,
    setSortBy,
    setSearchQuery,
    setFilters,
  }
}
