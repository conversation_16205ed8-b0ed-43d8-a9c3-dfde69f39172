import { useBaseTools } from '@/composables/useBaseTools'

/**
 * Composable for user search tools used by voice agent
 * Encapsulates the logic for search and navigation operations
 *
 * @param {Object} tableRef - Ref to tableRef component
 * @param {Object} searchQueryRef - Ref to searchQuery from parent
 * @returns {Object} - Tools for voice agent integration
 */
export function useUserSearchTools (tableRef, searchQueryRef) {
  const baseTools = useBaseTools(tableRef)

  /**
   * Open the first record from current search results
   * @returns {Promise<Object>} - Result object with success status
   */
  const openFirstRecord = async () => {
    console.log('[SearchTools:User] openFirstRecord() called')
    return processFirstRecord('open')
  }

  /**
   * Edit the first record from current search results
   * @returns {Promise<Object>} - Result object with success status
   */
  const editFirstRecord = async () => {
    console.log('[SearchTools:User] editFirstRecord() called')
    return processFirstRecord('edit')
  }

  /**
   * View the N record from current search results
   * @param {number} index
   * @returns {Promise<Object>} - Result object with success status
   */
  const openRecordByIndex = async index => {
    console.log('[SearchTools:User] openRecordByIndex() called with index:', index)
    return processFirstRecord('open', index)
  }

  /**
   * Edit the N record from current search results
   * @param {number} index
   * @returns {Promise<Object>} - Result object with success status
   */
  const editRecordByIndex = async index => {
    console.log('[SearchTools:User] editRecordByIndex() called with index:', index)
    return processFirstRecord('edit', index)
  }

  /**
   * Open/Edit the first record from current search results
   * @param {string} action
   * @param {number} index
   * @returns {Promise<Object>} - Result object with success status
   */
  const processFirstRecord = async (action, index = 0) => {
    console.log('[SearchTools:User] processFirstRecord() called with action:', action, 'index:', index)
    try {
      if (!tableRef.value) {
        const result = {
          success: false,
          error: 'User table not available',
        }
        console.error('[SearchTools:User] processFirstRecord failed:', result.error)
        return result
      }

      await tableRef.value.loadingPromise

      index = Number(index)

      const first = index === 0 ? tableRef.value.getFirstRecord() : tableRef.value.getRecordByIndex(index)

      if (!first) {
        const currentQuery = searchQueryRef.value
        const result = {
          success: false,
          error: 'No records available to open',
          message: currentQuery
            ? `No records found for search "${currentQuery}"`
            : 'No records in the list',
        }
        console.log('[SearchTools:User] processFirstRecord - no records available:', { currentQuery, action, index })
        return result
      }

      console.log('[SearchTools:User] processFirstRecord - found record:', { name: first.name, id: first.id, action, index })

      // Use the TableRef's view/edit function to navigate
      if (action === 'edit') {
        tableRef.value.edit(first)
        console.log('[SearchTools:User] navigating to edit record:', first.name)
      } else {
        tableRef.value.view(first)
        console.log('[SearchTools:User] navigating to view record:', first.name)
      }

      const result = {
        success: true,
        name: first.name,
        id: first.id,
        message: `${action.toUpperCase()} ${first.name}`,
      }

      console.log('[SearchTools:User] processFirstRecord completed successfully:', result)
      return result
    } catch (error) {
      const result = {
        success: false,
        error: `Failed to ${action}: ${error.message}`,
      }
      console.error('[SearchTools:User] processFirstRecord error:', error.message, { action, index })
      return result
    }
  }

  return {
    ...baseTools,
    openFirstRecord,
    editFirstRecord,
    openRecordByIndex,
    editRecordByIndex,
  }
}
