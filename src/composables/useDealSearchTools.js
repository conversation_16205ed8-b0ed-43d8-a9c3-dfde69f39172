import { useBaseTools } from '@/composables/useBaseTools'

/**
 * Composable for deal search tools used by voice agent
 * Encapsulates the logic for search and navigation operations
 *
 * @param {Object} tableRef - Ref to DealTable component
 * @param {Object} searchQueryRef - Ref to searchQuery from parent
 * @returns {Object} - Tools for voice agent integration
 */
export function useDealSearchTools (tableRef, searchQueryRef) {
  const baseTools = useBaseTools(tableRef)

  /**
   * Search for deals by query
   * Updates the search field and triggers the existing UI flow
   *
   * @param {string} query - Search term
   * @returns {Promise<Object>} - Result object with success status
   */
  const search = async query => {
    console.log('[SearchTools:Deal] search() called with query:', query)

    if (!query || typeof query !== 'string') {
      const result = {
        success: false,
        error: 'Search query must be a non-empty string',
        query,
      }
      console.log('[SearchTools:Deal] search validation failed:', result)
      return result
    }

    const startTime = Date.now()
    try {
      console.log('[SearchTools:Deal] starting search operation for:', query.trim())
      // Update the search query (this triggers existing reactive flow)
      searchQueryRef.value = query.trim()

      // Wait for the search to complete
      await tableRef.value.loadingPromise

      const resultsCount = tableRef.value?.items.length || 0
      const duration = Date.now() - startTime

      const result = {
        success: true,
        query: query.trim(),
        resultsCount,
        message: resultsCount > 0
          ? `Found ${resultsCount} deal${resultsCount === 1 ? '' : 's'} matching "${query}"`
          : `No deals found matching "${query}"`,
      }

      console.log('[SearchTools:Deal] search completed in', duration + 'ms:', { success: true, resultsCount, query: query.trim() })
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      const result = {
        success: false,
        error: `Search failed: ${error.message}`,
        query,
      }
      console.error('[SearchTools:Deal] search failed after', duration + 'ms:', error.message)
      return result
    }
  }

  /**
   * Open the first deal from current search results
   * @returns {Promise<Object>} - Result object with success status
   */
  const openFirstRecord = async () => {
    console.log('[SearchTools:Deal] openFirstRecord() called')
    return processFirstRecord('open')
  }

  /**
   * Edit the first deal from current search results
   * @returns {Promise<Object>} - Result object with success status
   */
  const editFirstRecord = async () => {
    console.log('[SearchTools:Deal] editFirstRecord() called')
    return processFirstRecord('edit')
  }

  /**
   * Open/Edit the first deal from current search results
   * @param {string} action
   * @returns {Promise<Object>} - Result object with success status
   */
  const processFirstRecord = async action => {
    console.log('[SearchTools:Deal] processFirstRecord() called with action:', action)

    try {
      if (!tableRef.value) {
        const result = {
          success: false,
          error: 'Deal table not available',
        }
        console.error('[SearchTools:Deal] processFirstRecord failed:', result.error)
        return result
      }

      await tableRef.value.loadingPromise

      const firstRecord = tableRef.value.getFirstRecord()

      if (!firstRecord) {
        const currentQuery = searchQueryRef.value
        const result = {
          success: false,
          error: 'No deals available to open',
          message: currentQuery
            ? `No deals found for search "${currentQuery}"`
            : 'No deals in the list',
        }
        console.log('[SearchTools:Deal] processFirstRecord - no deals available:', { currentQuery, action })
        return result
      }

      console.log('[SearchTools:Deal] processFirstRecord - found deal:', { dealTitle: firstRecord.title, dealId: firstRecord.id, action })

      // Use the DealTable's viewDeal function to navigate
      if (action === 'edit') {
        tableRef.value.edit(firstRecord)
        console.log('[SearchTools:Deal] navigating to edit deal:', firstRecord.title)
      } else {
        tableRef.value.view(firstRecord)
        console.log('[SearchTools:Deal] navigating to view deal:', firstRecord.title)
      }

      const result = {
        success: true,
        dealTitle: firstRecord.title,
        dealId: firstRecord.id,
        message: `${action.toUpperCase()} ${firstRecord.title}`,
      }

      console.log('[SearchTools:Deal] processFirstRecord completed successfully:', result)
      return result
    } catch (error) {
      const result = {
        success: false,
        error: `Failed to ${action} deal: ${error.message}`,
      }
      console.error('[SearchTools:Deal] processFirstRecord error:', error.message, { action })
      return result
    }
  }

  return {
    ...baseTools,
    search,
    openFirstRecord,
    editFirstRecord,
  }
}
