import { useBaseTools } from '@/composables/useBaseTools'

/**
 * Composable for contact search tools used by voice agent
 * Encapsulates the logic for search and navigation operations
 *
 * @param {Object} tableRef - Ref to ContactTable component
 * @param {Object} searchQueryRef - Ref to searchQuery from parent
 * @returns {Object} - Tools for voice agent integration
 */
export function useContactSearchTools (tableRef, searchQueryRef) {
  const baseTools = useBaseTools(tableRef)

  /**
   * Search for contacts by query
   * Updates the search field and triggers the existing UI flow
   *
   * @param {string} query - Search term
   * @returns {Promise<Object>} - Result object with success status
   */
  const search = async query => {
    console.log('[SearchTools:Contact] search() called with query:', query)

    if (!query || typeof query !== 'string') {
      const result = {
        success: false,
        error: 'Search query must be a non-empty string',
        query,
      }
      console.log('[SearchTools:Contact] search validation failed:', result)
      return result
    }

    const startTime = Date.now()
    try {
      console.log('[SearchTools:Contact] starting search operation for:', query.trim())
      // Update the search query (this triggers existing reactive flow)
      searchQueryRef.value = query.trim()

      // Wait for the search to complete
      await tableRef.value.loadingPromise

      const resultsCount = tableRef.value?.items.length || 0
      const duration = Date.now() - startTime

      const result = {
        success: true,
        query: query.trim(),
        resultsCount,
        message: resultsCount > 0
          ? `Found ${resultsCount} contact${resultsCount === 1 ? '' : 's'} matching "${query}"`
          : `No contacts found matching "${query}"`,
      }

      console.log('[SearchTools:Contact] search completed in', duration + 'ms:', { success: true, resultsCount, query: query.trim() })
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      const result = {
        success: false,
        error: `Search failed: ${error.message}`,
        query,
      }
      console.error('[SearchTools:Contact] search failed after', duration + 'ms:', error.message)
      return result
    }
  }

  /**
   * Open the first contact from current search results
   * @returns {Promise<Object>} - Result object with success status
   */
  const openFirstRecord = async () => {
    console.log('[SearchTools:Contact] openFirstRecord() called')
    return processFirstRecord('open')
  }

  /**
   * Edit the first contact from current search results
   * @returns {Promise<Object>} - Result object with success status
   */
  const editFirstRecord = async () => {
    console.log('[SearchTools:Contact] editFirstRecord() called')
    return processFirstRecord('edit')
  }

  /**
   * Open/Edit the first contact from current search results
   * @param {string} action
   * @returns {Promise<Object>} - Result object with success status
   */
  const processFirstRecord = async action => {
    console.log('[SearchTools:Contact] processFirstRecord() called with action:', action)

    try {
      if (!tableRef.value) {
        const result = {
          success: false,
          error: 'Contact table not available',
        }
        console.error('[SearchTools:Contact] processFirstRecord failed:', result.error)
        return result
      }

      // Wait for the search to complete
      await tableRef.value.loadingPromise

      const firstRecord = tableRef.value.getFirstRecord()

      if (!firstRecord) {
        const currentQuery = searchQueryRef.value
        const result = {
          success: false,
          error: 'No contacts available to open',
          message: currentQuery
            ? `No contacts found for search "${currentQuery}"`
            : 'No contacts in the list',
        }
        console.log('[SearchTools:Contact] processFirstRecord - no contacts available:', { currentQuery, action })
        return result
      }

      console.log('[SearchTools:Contact] processFirstRecord - found contact:', { contactName: firstRecord.fullName, contactId: firstRecord.id, action })

      // Use the ContactTable's viewContact function to navigate
      if (action === 'edit') {
        tableRef.value.edit(firstRecord)
        console.log('[SearchTools:Contact] navigating to edit contact:', firstRecord.fullName)
      } else {
        tableRef.value.view(firstRecord)
        console.log('[SearchTools:Contact] navigating to view contact:', firstRecord.fullName)
      }

      const result = {
        success: true,
        contactName: firstRecord.fullName,
        contactId: firstRecord.id,
        message: `${action.toUpperCase()} ${firstRecord.fullName}`,
      }

      console.log('[SearchTools:Contact] processFirstRecord completed successfully:', result)
      return result
    } catch (error) {
      const result = {
        success: false,
        error: `Failed to ${action} contact: ${error.message}`,
      }
      console.error('[SearchTools:Contact] processFirstRecord error:', error.message, { action })
      return result
    }
  }

  return {
    ...baseTools,
    search,
    openFirstRecord,
    editFirstRecord,
  }
}
