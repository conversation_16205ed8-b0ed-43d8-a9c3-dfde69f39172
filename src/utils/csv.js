export const arrayToCsv = data => {
  const headers = Object.keys(data[0]).map(element => escapeCsvValue(element)).join(',')
  const rows = data.map(item => Object.values(item).map(element => escapeCsvValue(element)).join(','))
  return [headers, ...rows].join('\n')
}

export const exportCsvFile = (data, filename) => {
  const content = typeof data === 'string' ? data : arrayToCsv(data)
  const blob = new Blob([content], { type: 'text/csv' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = filename
  a.click()
  URL.revokeObjectURL(url)
}

export const escapeCsvValue = value => {
  if (value === null || value === undefined) {
    return '' // Handle null or undefined values as empty strings
  }

  let stringValue = String(value)

  // Check if quoting is necessary
  const needsQuotes = stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n') || stringValue.includes('\r') || stringValue.includes(' ')

  if (needsQuotes) {
    // Escape double quotes by doubling them
    stringValue = stringValue.replace(/"/g, '""')
    // Enclose the field in double quotes
    return `"${stringValue}"`
  } else {
    return stringValue
  }
}
