# Sentient UI

A modern, voice-driven CRM platform built with Vue 3 and Vuetify 3. Sentient UI enables users to control complex business applications using natural language commands, interpreted in real-time through always-on speech recognition and fast LLM reasoning.

## 🚀 Key Features

- **🎙️ Voice-First Interface**: Control the entire application using natural language commands
- **🧠 AI-Powered Actions**: Real-time speech recognition with Deepgram and LLM reasoning (Gemini Flash/Groq)
- **📊 Complete CRM System**: Manage companies, contacts, deals, and users with full CRUD operations
- **🎯 Context-Aware Commands**: Dual-layer context architecture for intelligent command interpretation
- **⚡ Real-Time Processing**: Instant voice command processing and UI updates
- **🔄 Seamless Integration**: Voice commands work across all pages and components

## 🛠️ Technology Stack

### Frontend Framework
- **Vue 3** - Modern reactive framework with Composition API
- **Vuetify 3** - Material Design component library
- **Vite** - Fast build tool and development server
- **Vue Router** - Client-side routing with file-based routing

### Voice & AI Integration
- **Deepgram SDK** - Real-time speech recognition
- **Google GenAI** - LLM reasoning and command interpretation
- **<PERSON>roq SDK** - Alternative LLM service for fast inference
- **Custom Voice Agent System** - Context-aware command processing
- **Langfuse** - LLM observability and tracing for AI services

### Development & Testing
- **Vitest** - Unit and integration testing
- **Playwright** - End-to-end testing
- **ESLint** - Code linting with Vuetify configuration
- **LocalStorage** - Client-side data persistence

## 📦 Installation

Set up your project using your preferred package manager:

```bash
# Using pnpm (recommended)
pnpm install

# Using npm
npm install

# Using yarn
yarn install
```

## 🚀 Development

### Starting the Development Server

```bash
pnpm dev
```

The server will be accessible at [http://localhost:3000](http://localhost:3000)

### Building for Production

```bash
pnpm build
```

### Preview Production Build

```bash
pnpm preview
```

## 🗣️ Voice Commands

Sentient UI responds to natural language commands across all pages. Here are some examples:

### General Navigation
- "Go to deals page"
- "Show me the companies"
- "Navigate to settings"

### Deal Management
- "Search for deals with 'tech' in the title"
- "Filter deals by high priority"
- "Show me closed won deals"
- "Set budget filter from 10000 to 50000"
- "Clear all filters"

### Company Operations
- "Find company by name Microsoft"
- "Go to the first company details"
- "Edit the first company"

### Contact Management
- "Search for contacts named John"
- "Show contact details for the first person"
- "Add new activity for this contact"

## 🏗️ Architecture

### Voice Agent System

The application uses a sophisticated dual-layer context architecture:

1. **Global Context Service** - Manages application-wide voice commands and page-level tools
2. **Local Context Service** - Handles component-specific commands and maintains context hierarchy
3. **Voice Agent Coordinator** - Orchestrates between speech recognition and command execution

### Project Structure

```
src/
├── components/           # Vue components organized by feature
│   ├── companies/       # Company management components
│   ├── contacts/        # Contact management components
│   ├── deals/           # Deal management components
│   ├── users/           # User management components
│   └── voice-agent/     # Voice agent UI components
├── composables/         # Reusable composition functions
├── models/              # Data models and entities
├── pages/               # File-based routing pages
├── repositories/        # Data access layer
├── services/            # Business logic and external services
│   └── voice-agent/     # Voice processing and AI services
├── seeds/               # Mock data for development
└── styles/              # Global styles and themes
```

## 🧪 Testing

### Run All Tests

```bash
pnpm test
```

### Unit Tests

```bash
pnpm test:unit          # Watch mode
pnpm test:unit:run      # Single run
```

### Integration Tests

```bash
pnpm test:integration       # Watch mode
pnpm test:integration:run   # Single run
```

### End-to-End Tests

```bash
pnpm test:e2e           # Headless mode
pnpm test:e2e:ui        # Interactive UI mode
pnpm test:e2e:debug     # Debug mode
pnpm test:e2e:html      # HTML reporter
```

## 🔧 Configuration

### Key Configuration Files

- **`vite.config.mjs`** - Vite build configuration with Vuetify auto-import
- **`vitest.config.js`** - Testing configuration
- **`playwright.config.js`** - E2E testing configuration
- **`src/plugins/index.js`** - Vue plugins registration
- **`src/router/index.js`** - Vue Router configuration with auto-routes

### Environment Setup

The application uses file-based routing and auto-imported components:

- Pages in `src/pages/` are automatically registered as routes
- Components in `src/components/` are auto-imported
- Aliases: `@` points to `src/` directory

## 📋 Available Commands

| Command | Description |
|---------|-------------|
| `pnpm dev` | Start development server |
| `pnpm build` | Build for production |
| `pnpm preview` | Preview production build |
| `pnpm lint` | Run ESLint and fix issues |
| `pnpm test` | Run all tests |
| `pnpm test:unit` | Run unit tests in watch mode |
| `pnpm test:integration` | Run integration tests in watch mode |
| `pnpm test:e2e` | Run end-to-end tests |

## 🎯 CRM Features

### Companies Management
- View, create, edit, and delete companies
- Search and filter company records
- Company details with contact information

### Contacts Management
- Comprehensive contact management system
- Activity tracking and history
- Company association and relationships

### Deals Management
- Deal pipeline with status tracking
- Priority levels and budget management
- Deal owner assignment and filtering
- Advanced filtering by status, priority, budget range, and owner

### User Management
- User profile management
- Access control and permissions
- User activity tracking

## 🤝 Contributing

When working with this repository:

1. Follow the existing code conventions and patterns
2. Use the provided development commands for consistency
3. Ensure all tests pass before submitting changes
4. The voice agent system requires careful consideration of context and command structure

### Development Guidelines

- Components should be auto-imported (no manual imports needed)
- Use Composition API with `<script setup>` syntax
- Follow Vuetify 3 theming and component patterns
- Maintain voice command compatibility when adding new features

## 📈 Observability & Monitoring

### Langfuse Integration

The application integrates with Langfuse for comprehensive LLM observability and monitoring:

- **LLM Request Tracing** - All Gemini and Groq API calls are automatically traced with input/output data
- **Performance Monitoring** - Track token usage, response times, and costs across AI services
- **Error Tracking** - Monitor and debug AI service failures and performance issues
- **Usage Analytics** - Analyze voice command patterns and user interaction data
- **Context Awareness** - Trace requests with associated user context and application state

Langfuse provides valuable insights into the AI-powered voice command system, helping optimize performance and understand user behavior patterns.

## 🛡️ Security

This application processes voice input and uses AI services. Ensure proper API key management and never commit sensitive credentials to the repository.