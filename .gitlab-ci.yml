stages:
  - build

build:
  stage: build
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'
  script:
    - echo Create EnvFile...
    - cat .env.template | perl -wpne 's/\$(\w+)/$ENV{$1}/g;' 2>/dev/null> .env
    - echo "Build..."
    - pnpm -v
    - pnpm install
    - pnpm build
    - echo "Upload code to S3"
    - aws s3 sync dist/ s3://$S3_BUCKET --acl public-read --delete
    - aws cloudfront create-invalidation --distribution-id $CDN_DISTRIBUTION_ID --paths "/*"

