import { expect, test } from '@playwright/test'

/**
 * ActionPlanExecutor Tests
 *
 * Tests for the ActionPlanExecutor orchestrator including immediate execution,
 * queue-based processing, and service lifecycle management.
 */

test.describe('ActionPlanExecutor', () => {
  test('should handle empty plans correctly', async ({ page }) => {
    await page.goto('/')

    const result = await page.evaluate(async () => {
      const module = await import('/src/services/voice-agent/ActionPlanExecutor.js')
      const service = module.actionPlanExecutor

      return await service.executePlan([])
    })

    expect(result.success).toBe(true)
    expect(result.executedSteps).toBe(0)
    expect(result.totalSteps).toBe(0)
    expect(Array.isArray(result.log)).toBe(true)
    expect(result.log.length).toBe(0)
  })

  test('should handle invalid action types', async ({ page }) => {
    await page.goto('/')

    const result = await page.evaluate(async () => {
      const module = await import('/src/services/voice-agent/ActionPlanExecutor.js')
      const service = module.actionPlanExecutor

      const invalidPlan = [
        {
          action: 'invalidActionType',
          payload: { test: 'data' },
        },
      ]

      return await service.executePlan(invalidPlan)
    })

    expect(result.executedSteps).toBe(1)
    expect(result.totalSteps).toBe(1)
    expect(result.log.length).toBe(1)

    const stepResult = result.log[0].result
    expect(stepResult.success).toBe(false)
    expect(stepResult.error).toContain('Unsupported action type')
  })

  test('should prevent concurrent immediate executions', async ({ page }) => {
    await page.goto('/')

    const result = await page.evaluate(async () => {
      const module = await import('/src/services/voice-agent/ActionPlanExecutor.js')
      const service = module.actionPlanExecutor

      const plan1 = [{ action: 'navigate', payload: { path: '/companies' } }]
      const plan2 = [{ action: 'navigate', payload: { path: '/settings' } }]

      // Start both plans concurrently
      const [result1, result2] = await Promise.all([
        service.executePlan(plan1),
        service.executePlan(plan2),
      ])

      return { result1, result2 }
    })

    // One should succeed, one should be rejected
    const results = [result.result1, result.result2]
    const successes = results.filter(r => r.success).length
    const failures = results.filter(r => !r.success).length

    expect(successes).toBe(1)
    expect(failures).toBe(1)

    // The failed one should have the concurrent execution error
    const failedResult = results.find(r => !r.success)
    expect(failedResult.error).toContain('Another plan is currently executing')
  })

  test('should support queue-based execution', async ({ page }) => {
    await page.goto('/')

    const result = await page.evaluate(async () => {
      const module = await import('/src/services/voice-agent/ActionPlanExecutor.js')
      const service = module.actionPlanExecutor

      // Start the service
      service.start()

      // Queue some plans
      const plan1 = [{ action: 'navigate', payload: { path: '/companies' } }]
      const plan2 = [{ action: 'navigate', payload: { path: '/settings' } }]

      const queueId1 = service.queuePlan(plan1, 'low')
      const queueId2 = service.queuePlan(plan2, 'high') // Should execute first

      const status = service.getQueueStatus()

      // Stop the service
      service.stop()

      return {
        queueId1,
        queueId2,
        status,
        queueId1Valid: typeof queueId1 === 'string' && queueId1.startsWith('plan_'),
        queueId2Valid: typeof queueId2 === 'string' && queueId2.startsWith('plan_'),
      }
    })

    expect(result.queueId1Valid).toBe(true)
    expect(result.queueId2Valid).toBe(true)
    expect(result.status.isStarted).toBe(true)
    expect(typeof result.status.queueLength).toBe('number')
    expect(typeof result.status.isProcessing).toBe('boolean')
  })

  test('should handle service lifecycle correctly', async ({ page }) => {
    await page.goto('/')

    const result = await page.evaluate(async () => {
      const module = await import('/src/services/voice-agent/ActionPlanExecutor.js')
      const service = module.actionPlanExecutor

      // In e2e environment, service may not be auto-started, so start it manually first
      service.start()
      const initialStatus = service.getQueueStatus()

      // Try to start again (should warn, not fail since already started)
      service.start()
      const startedStatus = service.getQueueStatus()

      // Stop service
      service.stop()
      const stoppedStatus = service.getQueueStatus()

      // Restart service to test full lifecycle
      service.start()
      const restartedStatus = service.getQueueStatus()

      return {
        initialStarted: initialStatus.isStarted,
        startedStarted: startedStatus.isStarted,
        stoppedStarted: stoppedStatus.isStarted,
        restartedStarted: restartedStatus.isStarted,
      }
    })

    // Test the full lifecycle operations
    expect(result.initialStarted).toBe(true)
    expect(result.startedStarted).toBe(true)
    expect(result.stoppedStarted).toBe(false)
    expect(result.restartedStarted).toBe(true)
  })

  test('should prioritize queued plans correctly', async ({ page }) => {
    await page.goto('/')

    const result = await page.evaluate(async () => {
      const module = await import('/src/services/voice-agent/ActionPlanExecutor.js')
      const service = module.actionPlanExecutor

      // Queue plans in different order than priority
      service.queuePlan([{ action: 'test1' }], 'low')
      service.queuePlan([{ action: 'test2' }], 'high')
      service.queuePlan([{ action: 'test3' }], 'normal')

      const status = service.getQueueStatus()
      const nextPlan = status.nextPlan

      service.stop() // Clean up

      return {
        queueLength: status.queueLength,
        nextPlanPriority: nextPlan?.priority,
        nextPlanAction: nextPlan?.plan[0]?.action,
      }
    })

    expect(result.queueLength).toBe(3)
    expect(result.nextPlanPriority).toBe('high') // High priority should be first
    expect(result.nextPlanAction).toBe('test2')
  })
})

// Plan structure validation tests (no browser needed)
test.describe('Plan Structure Validation', () => {
  test('should validate navigation plan structure', async () => {
    const validNavigation = {
      action: 'navigate',
      payload: { path: '/companies' },
    }

    const invalidNavigation = {
      action: 'navigate',
      // Missing payload
    }

    expect(validNavigation.action).toBe('navigate')
    expect(validNavigation.payload?.path).toBeDefined()
    expect(typeof validNavigation.payload.path).toBe('string')

    expect(invalidNavigation.payload?.path).toBeUndefined()
  })

  test('should validate tool execution plan structure', async () => {
    const validTool = {
      action: 'useTool',
      payload: {
        targetComponent: {
          id: 'CompanyTable_123',
          name: 'CompanyTable',
        },
        tool: 'search',
        params: ['BlueBeam'],
      },
    }

    const invalidTool = {
      action: 'useTool',
      // Missing payload
    }

    expect(validTool.payload).toBeDefined()
    expect(typeof validTool.payload).toBe('object')
    expect(validTool.payload.targetComponent).toBeDefined()
    expect(typeof validTool.payload.targetComponent).toBe('object')
    expect(validTool.payload.tool).toBeDefined()
    expect(typeof validTool.payload.tool).toBe('string')
    expect(Array.isArray(validTool.payload.params)).toBe(true)

    expect(invalidTool.payload).toBeUndefined()
  })
})
