<template>
  <div data-testid="test-voice-component">
    <h2>Test Voice Component</h2>
    <div>
      <p>Component Name: {{ voiceAgent.componentName }}</p>
      <p>Component ID: {{ voiceAgent.componentId }}</p>
    </div>

    <div data-testid="tool-results">
      <h3>Tool Results:</h3>
      <pre>{{ JSON.stringify(toolResults, null, 2) }}</pre>
    </div>

    <div data-testid="voice-agent-info">
      <h3>Voice Agent Info:</h3>
      <pre>{{ JSON.stringify({ componentName: voiceAgent.componentName, componentId: voiceAgent.componentId }, null, 2) }}</pre>
    </div>

    <div data-testid="test-buttons">
      <v-btn data-testid="btn-simple" @click="testSimpleAction">Test Simple</v-btn>
      <v-btn data-testid="btn-param" @click="testActionWithParam">Test With Param</v-btn>
      <v-btn data-testid="btn-optionals" @click="testActionWithOptionals">Test Optionals</v-btn>
      <v-btn data-testid="btn-documented" @click="testDocumentedAction">Test Documented</v-btn>
    </div>
  </div>
</template>

<script setup>
  import { ref } from 'vue'
  import { useVoiceAgent } from '@/composables/useVoiceAgent'

  // Explicitly define component name for testing
  defineOptions({
    name: 'TestVoiceComponent',
  })

  const toolResults = ref({})

  // Test tools with different signatures for comprehensive testing
  const testTools = {
    // Simple action with no parameters
    simpleAction: () => {
      const result = { success: true, timestamp: Date.now() }
      toolResults.value.simpleAction = result
      return result
    },

    // Action with single required parameter
    actionWithParam: id => {
      const result = { success: true, id, processed: true }
      toolResults.value.actionWithParam = result
      return result
    },

    // Action with optional parameters and defaults
    actionWithOptionals: (query, limit = 10, filters = {}) => {
      const result = {
        success: true,
        query,
        limit,
        filters,
        resultsCount: Math.floor(Math.random() * 100),
      }
      toolResults.value.actionWithOptionals = result
      return result
    },

    /**
     * Action with JSDoc description for testing documentation parsing
     * @param {string} name - The name parameter for processing
     * @returns {Object} - Result object with success status
     */
    documentedAction: name => {
      const result = {
        success: true,
        name,
        processed: true,
        documentation: 'This action has JSDoc',
      }
      toolResults.value.documentedAction = result
      return result
    },

    // Async action for testing async function handling
    asyncAction: async data => {
      await new Promise(resolve => setTimeout(resolve, 100))
      const result = { success: true, data, async: true }
      toolResults.value.asyncAction = result
      return result
    },

    // Action with destructured parameters
    destructuredAction: ({ id, name, options = {} }) => {
      const result = { success: true, id, name, options }
      toolResults.value.destructuredAction = result
      return result
    },
  }

  // Register with voice agent system
  const voiceAgent = useVoiceAgent({
    description: 'Test component for voice agent enhanced structure testing',
    tools: testTools,
    currentEntity: {
      type: 'TestEntity',
      id: 'test-123',
      data: { name: 'Test Entity', status: 'active' },
    },
  })

  // Test button handlers
  const testSimpleAction = () => {
    testTools.simpleAction()
  }

  const testActionWithParam = () => {
    testTools.actionWithParam('test-id-123')
  }

  const testActionWithOptionals = () => {
    testTools.actionWithOptionals('test query', 20, { category: 'test' })
  }

  const testDocumentedAction = () => {
    testTools.documentedAction('Test Name')
  }

  // Expose methods for testing
  defineExpose({
    getVoiceAgent: () => voiceAgent,
    getToolResults: () => toolResults.value,
    clearToolResults: () => {
      toolResults.value = {}
    },
  })
</script>
