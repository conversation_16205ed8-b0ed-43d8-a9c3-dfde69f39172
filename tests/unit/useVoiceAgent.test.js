import { mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import TestVoiceComponent from '../components/TestVoiceComponent.vue'

// Mock the LocalContextService
vi.mock('@/services/voice-agent/context/LocalContextService.js', () => ({
  localContextService: {
    register: vi.fn(),
    deregister: vi.fn(),
    updateCurrentEntity: vi.fn(),
    updateDescription: vi.fn(),
    getTree: vi.fn(() => ({})),
    getAllTools: vi.fn(() => ({})),
    getActiveEntities: vi.fn(() => []),
  },
}))

describe('useVoiceAgent Public API', () => {
  let wrapper

  beforeEach(() => {
    wrapper = mount(TestVoiceComponent, {
      global: {
        stubs: {
          'v-btn': {
            template: '<button :data-testid="$attrs[\'data-testid\']" @click="$emit(\'click\')"><slot /></button>',
            inheritAttrs: false,
          },
        },
      },
    })
  })

  describe('Component Integration', () => {
    it('should detect component name automatically', () => {
      const voiceAgent = wrapper.vm.getVoiceAgent()
      expect(voiceAgent.componentName).toBe('TestVoiceComponent')
    })

    it('should generate unique component ID with component name', () => {
      const voiceAgent = wrapper.vm.getVoiceAgent()
      expect(voiceAgent.componentId).toMatch(/^TestVoiceComponent_\d+$/)
    })

    it('should expose public API methods', () => {
      const voiceAgent = wrapper.vm.getVoiceAgent()
      expect(typeof voiceAgent.setDescription).toBe('function')
      expect(typeof voiceAgent.setCurrentEntity).toBe('function')
      expect(typeof voiceAgent.setEntity).toBe('function')
      expect(typeof voiceAgent.getLocalContextTree).toBe('function')
      expect(typeof voiceAgent.getAllTools).toBe('function')
      expect(typeof voiceAgent.getActiveEntities).toBe('function')
    })
  })

  describe('Tool Execution', () => {
    it('should execute simple action successfully', async () => {
      await wrapper.find('[data-testid="btn-simple"]').trigger('click')

      const results = wrapper.vm.getToolResults()
      expect(results.simpleAction).toBeDefined()
      expect(results.simpleAction.success).toBe(true)
      expect(results.simpleAction.timestamp).toBeDefined()
    })

    it('should execute action with parameters', async () => {
      await wrapper.find('[data-testid="btn-param"]').trigger('click')

      const results = wrapper.vm.getToolResults()
      expect(results.actionWithParam).toBeDefined()
      expect(results.actionWithParam.success).toBe(true)
      expect(results.actionWithParam.id).toBe('test-id-123')
    })

    it('should execute action with optional parameters', async () => {
      await wrapper.find('[data-testid="btn-optionals"]').trigger('click')

      const results = wrapper.vm.getToolResults()
      expect(results.actionWithOptionals).toBeDefined()
      expect(results.actionWithOptionals.success).toBe(true)
      expect(results.actionWithOptionals.query).toBe('test query')
      expect(results.actionWithOptionals.limit).toBe(20)
    })

    it('should execute documented action', async () => {
      await wrapper.find('[data-testid="btn-documented"]').trigger('click')

      const results = wrapper.vm.getToolResults()
      expect(results.documentedAction).toBeDefined()
      expect(results.documentedAction.success).toBe(true)
      expect(results.documentedAction.name).toBe('Test Name')
    })
  })

  describe('LocalContextService Integration', () => {
    it('should register with LocalContextService on mount', async () => {
      const { localContextService } = vi.mocked(
        await import('@/services/voice-agent/context/LocalContextService.js'),
      )

      expect(localContextService.register).toHaveBeenCalled()

      const registerCall = localContextService.register.mock.calls[0][0]
      expect(registerCall.componentId).toMatch(/^TestVoiceComponent_\d+$/)
      expect(registerCall.componentName).toBe('TestVoiceComponent')
      expect(registerCall.description).toBe('Test component for voice agent enhanced structure testing')
      expect(registerCall.tools).toBeDefined()
      expect(registerCall.enhancedTools).toBeDefined()
      expect(registerCall.parent).toBe('root')
    })

    it('should deregister with LocalContextService on unmount', async () => {
      const { localContextService } = vi.mocked(
        await import('@/services/voice-agent/context/LocalContextService.js'),
      )

      // Clear previous calls
      localContextService.deregister.mockClear()

      // Unmount component
      wrapper.unmount()

      expect(localContextService.deregister).toHaveBeenCalledWith(
        expect.stringMatching(/^TestVoiceComponent_\d+$/),
      )
    })

    it('should call utility methods correctly', () => {
      const voiceAgent = wrapper.vm.getVoiceAgent()

      // Test setDescription
      voiceAgent.setDescription('New description')

      // Test setCurrentEntity
      const testEntity = { type: 'Test', id: '123', data: {} }
      voiceAgent.setCurrentEntity(testEntity)

      // Test setEntity
      voiceAgent.setEntity('TestType', { id: '456', name: 'Test Entity' })

      // Verify methods exist and can be called
      expect(typeof voiceAgent.getLocalContextTree()).toBe('object')
      expect(typeof voiceAgent.getAllTools()).toBe('object')
      expect(Array.isArray(voiceAgent.getActiveEntities())).toBe(true)
    })
  })

  describe('Manual Registration/Deregistration', () => {
    it('should provide manual register method', () => {
      const voiceAgent = wrapper.vm.getVoiceAgent()

      expect(typeof voiceAgent.register).toBe('function')
    })

    it('should provide manual deregister method', () => {
      const voiceAgent = wrapper.vm.getVoiceAgent()

      expect(typeof voiceAgent.deregister).toBe('function')
    })

    it('should manually register with LocalContextService', async () => {
      const { localContextService } = vi.mocked(
        await import('@/services/voice-agent/context/LocalContextService.js'),
      )

      const voiceAgent = wrapper.vm.getVoiceAgent()

      // Clear previous calls
      localContextService.register.mockClear()

      // Call manual register
      voiceAgent.register()

      expect(localContextService.register).toHaveBeenCalledTimes(1)

      const registerCall = localContextService.register.mock.calls[0][0]
      expect(registerCall.componentId).toMatch(/^TestVoiceComponent_\d+$/)
      expect(registerCall.componentName).toBe('TestVoiceComponent')
      expect(registerCall.tools).toBeDefined()
      expect(registerCall.enhancedTools).toBeDefined()
    })

    it('should manually deregister from LocalContextService', async () => {
      const { localContextService } = vi.mocked(
        await import('@/services/voice-agent/context/LocalContextService.js'),
      )

      const voiceAgent = wrapper.vm.getVoiceAgent()

      // Clear previous calls
      localContextService.deregister.mockClear()

      // Call manual deregister
      voiceAgent.deregister()

      expect(localContextService.deregister).toHaveBeenCalledWith(
        expect.stringMatching(/^TestVoiceComponent_\d+$/),
      )
    })

    it('should use shared registration logic for both lifecycle and manual registration', async () => {
      const { localContextService } = vi.mocked(
        await import('@/services/voice-agent/context/LocalContextService.js'),
      )

      const voiceAgent = wrapper.vm.getVoiceAgent()

      // Clear and manually register
      localContextService.register.mockClear()
      voiceAgent.register()

      // Get the manual registration call
      const manualCall = localContextService.register.mock.calls[0][0]

      // Verify the registration call has expected structure
      expect(manualCall.componentName).toBe('TestVoiceComponent')
      expect(manualCall.componentId).toMatch(/^TestVoiceComponent_\d+$/)
      expect(manualCall.description).toBe('Test component for voice agent enhanced structure testing')
      expect(manualCall.parent).toBe('root')
      expect(manualCall.tools).toBeDefined()
      expect(manualCall.enhancedTools).toBeDefined()
      expect(Object.keys(manualCall.tools)).toContain('simpleAction')
      expect(Object.keys(manualCall.tools)).toContain('actionWithParam')
    })

    it('should handle multiple register/deregister cycles', async () => {
      const { localContextService } = vi.mocked(
        await import('@/services/voice-agent/context/LocalContextService.js'),
      )

      const voiceAgent = wrapper.vm.getVoiceAgent()

      // Clear initial calls
      localContextService.register.mockClear()
      localContextService.deregister.mockClear()

      // Cycle 1
      voiceAgent.register()
      voiceAgent.deregister()

      // Cycle 2
      voiceAgent.register()
      voiceAgent.deregister()

      expect(localContextService.register).toHaveBeenCalledTimes(2)
      expect(localContextService.deregister).toHaveBeenCalledTimes(2)
    })
  })

  describe('Component Lifecycle', () => {
    it('should deregister on unmount', async () => {
      const { localContextService } = vi.mocked(
        await import('@/services/voice-agent/context/LocalContextService.js'),
      )

      // Clear previous calls
      localContextService.deregister.mockClear()

      // Unmount the component
      wrapper.unmount()

      expect(localContextService.deregister).toHaveBeenCalled()
    })
  })
})
