import { mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { nextTick } from 'vue'
import ActivityCreateDialog from '@/components/activities/ActivityCreateDialog.vue'

// Mock the useVoiceAgent composable
const mockVoiceAgent = {
  register: vi.fn(),
  deregister: vi.fn(),
  setDescription: vi.fn(),
  componentId: 'test-component-id'
}

vi.mock('@/composables/useVoiceAgent', () => ({
  useVoiceAgent: vi.fn(() => mockVoiceAgent)
}))

// Mock ActivityRepository
vi.mock('@/repositories/ActivityRepository', () => ({
  ActivityRepository: vi.fn(() => ({
    create: vi.fn().mockResolvedValue({ id: 'new-activity-id' })
  }))
}))

describe('ActivityCreateDialog', () => {
  let wrapper
  const mockContact = {
    id: 'contact-123',
    firstName: 'John',
    lastName: 'Doe',
    fullName: '<PERSON>',
    email: '<EMAIL>'
  }

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks()
  })

  const createWrapper = (props = {}) => {
    return mount(ActivityCreateDialog, {
      props: {
        modelValue: false,
        contact: mockContact,
        ...props
      },
      global: {
        stubs: {
          'v-dialog': {
            template: '<div><slot /></div>'
          },
          'v-card': {
            template: '<div><slot /></div>'
          },
          'v-card-title': {
            template: '<div><slot /></div>'
          },
          'v-card-text': {
            template: '<div><slot /></div>'
          },
          'v-card-actions': {
            template: '<div><slot /></div>'
          },
          'v-form': {
            template: '<form><slot /></form>',
            methods: {
              resetValidation: vi.fn(),
              validate: vi.fn(() => ({ valid: true }))
            }
          },
          'v-avatar': {
            template: '<div><slot /></div>'
          },
          'v-textarea': {
            template: '<textarea />',
            props: ['modelValue', 'label', 'required']
          },
          'v-btn': {
            template: '<button><slot /></button>',
            props: ['color', 'variant', 'disabled', 'loading']
          },
          'v-spacer': {
            template: '<div></div>'
          },
          'v-row': {
            template: '<div><slot /></div>'
          },
          'v-col': {
            template: '<div><slot /></div>'
          }
        }
      }
    })
  }

  describe('voice agent context management', () => {
    it('should register with voice context when dialog opens', async () => {
      wrapper = createWrapper({ modelValue: false })
      
      // Initially not registered since dialog is closed
      expect(mockVoiceAgent.register).not.toHaveBeenCalled()
      
      // Open dialog
      await wrapper.setProps({ modelValue: true })
      await nextTick()
      
      // Should register when dialog opens
      expect(mockVoiceAgent.register).toHaveBeenCalledTimes(1)
    })

    it('should deregister from voice context when dialog is closed via closeDialog', async () => {
      wrapper = createWrapper({ modelValue: true })
      await nextTick()
      
      // Reset the register call from opening
      mockVoiceAgent.register.mockClear()
      
      // Call closeDialog directly
      wrapper.vm.closeDialog()
      await nextTick()
      
      // Should deregister when dialog closes
      expect(mockVoiceAgent.deregister).toHaveBeenCalledTimes(1)
    })

    it('should set voice agent description when dialog opens with contact', async () => {
      wrapper = createWrapper({ modelValue: false })
      
      await wrapper.setProps({ modelValue: true })
      await nextTick()
      
      expect(mockVoiceAgent.setDescription).toHaveBeenCalledWith(
        expect.stringContaining('Add activity form for contact John Doe')
      )
      expect(mockVoiceAgent.setDescription).toHaveBeenCalledWith(
        expect.stringContaining(JSON.stringify(mockContact))
      )
    })

    it('should not register if dialog opens without contact', async () => {
      wrapper = createWrapper({ modelValue: false, contact: null })
      
      await wrapper.setProps({ modelValue: true })
      await nextTick()
      
      expect(mockVoiceAgent.register).not.toHaveBeenCalled()
    })

    it('should handle multiple open/close cycles correctly', async () => {
      wrapper = createWrapper({ modelValue: false })
      
      // First open
      await wrapper.setProps({ modelValue: true })
      await nextTick()
      expect(mockVoiceAgent.register).toHaveBeenCalledTimes(1)
      
      // Close
      wrapper.vm.closeDialog()
      await nextTick()
      expect(mockVoiceAgent.deregister).toHaveBeenCalledTimes(1)
      
      // Second open (this should trigger register again)
      await wrapper.setProps({ modelValue: false }) // Reset first
      await wrapper.setProps({ modelValue: true })
      await nextTick()
      expect(mockVoiceAgent.register).toHaveBeenCalledTimes(2)
      
      // Second close
      wrapper.vm.closeDialog()
      await nextTick()
      expect(mockVoiceAgent.deregister).toHaveBeenCalledTimes(2)
    })
  })

  describe('dialog state management', () => {
    it('should sync internal dialog state with modelValue prop', async () => {
      wrapper = createWrapper({ modelValue: false })
      expect(wrapper.vm.dialog).toBe(false)
      
      await wrapper.setProps({ modelValue: true })
      expect(wrapper.vm.dialog).toBe(true)
      
      await wrapper.setProps({ modelValue: false })
      expect(wrapper.vm.dialog).toBe(false)
    })

    it('should emit update:modelValue when internal dialog state changes', async () => {
      wrapper = createWrapper({ modelValue: true })
      await nextTick()
      
      // Clear any previous emissions
      wrapper.vm.$emit('update:modelValue', false)
      await nextTick()
      
      const emitted = wrapper.emitted('update:modelValue')
      expect(emitted).toBeTruthy()
      expect(emitted[emitted.length - 1]).toEqual([false])
    })

    it('should reset form data when dialog opens', async () => {
      wrapper = createWrapper({ modelValue: false })
      
      // Set some data first
      wrapper.vm.newActivity.note = 'Some existing note'
      
      await wrapper.setProps({ modelValue: true })
      await nextTick()
      
      // Should be reset
      expect(wrapper.vm.newActivity.note).toBe('')
    })
  })

  describe('voice agent tools', () => {
    beforeEach(async () => {
      wrapper = createWrapper({ modelValue: true })
      await nextTick()
    })

    it('should provide setActivityNote tool', () => {
      expect(wrapper.vm.setActivityNote).toBeDefined()
      expect(typeof wrapper.vm.setActivityNote.tool).toBe('function')
    })

    it('should provide cancel tool', () => {
      // The cancel tool should be the closeDialog function
      expect(typeof wrapper.vm.closeDialog).toBe('function')
    })

    it('should provide save tool', () => {
      expect(typeof wrapper.vm.saveActivity).toBe('function')
    })

    it('should update activity note through voice tool', () => {
      const testNote = 'Test activity note from voice command'
      wrapper.vm.setActivityNote.tool(testNote)
      
      expect(wrapper.vm.newActivity.note).toBe(testNote)
    })
  })
})