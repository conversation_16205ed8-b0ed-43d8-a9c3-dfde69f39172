import { beforeEach, describe, expect, it, vi } from 'vitest'
import { localContextService } from '@/services/voice-agent/context/LocalContextService.js'

describe('LocalContextService Enhanced Methods', () => {
  let testComponentId
  let mockEnhancedTools

  beforeEach(() => {
    // Clear the service state
    localContextService.clear()

    testComponentId = 'TestComponent_123'
    mockEnhancedTools = [
      {
        name: 'search',
        description: 'Search for items',
        params: [{ name: 'query', type: 'string', required: true }],
      },
      {
        name: 'openRecord',
        description: 'Open first record',
        params: [],
      },
    ]
  })

  describe('Enhanced Tools Registration', () => {
    it('should register component with enhanced tools', () => {
      localContextService.register({
        componentId: testComponentId,
        componentName: 'TestComponent',
        description: 'Test component',
        tools: { search: vi.fn(), openRecord: vi.fn() },
        enhancedTools: mockEnhancedTools,
      })

      const component = localContextService.findComponentById(testComponentId)
      expect(component).toBeDefined()
      expect(component.enhancedTools).toEqual(mockEnhancedTools)
    })

    it('should handle registration without enhanced tools', () => {
      localContextService.register({
        componentId: testComponentId,
        componentName: 'TestComponent',
        description: 'Test component',
        tools: { search: vi.fn() },
        // No enhancedTools provided
      })

      const component = localContextService.findComponentById(testComponentId)
      expect(component).toBeDefined()
      expect(component.enhancedTools).toEqual([])
    })
  })

  describe('getEnhancedActionStructure', () => {
    beforeEach(() => {
      localContextService.register({
        componentId: testComponentId,
        componentName: 'TestComponent',
        description: 'Test component',
        tools: { search: vi.fn(), openRecord: vi.fn() },
        enhancedTools: mockEnhancedTools,
      })
    })

    it('should return enhanced action structure for existing component', () => {
      const result = localContextService.getEnhancedActionStructure(testComponentId)

      expect(result).toEqual({
        action: 'useTool',
        targetComponent: {
          name: 'TestComponent',
          id: testComponentId,
        },
        tools: mockEnhancedTools,
      })
    })

    it('should return null for non-existent component', () => {
      const result = localContextService.getEnhancedActionStructure('NonExistentComponent')
      expect(result).toBeNull()
    })

    it('should handle component with empty enhanced tools', () => {
      localContextService.register({
        componentId: 'EmptyToolsComponent',
        componentName: 'EmptyComponent',
        description: 'Empty component',
        tools: {},
        enhancedTools: [],
      })

      const result = localContextService.getEnhancedActionStructure('EmptyToolsComponent')

      expect(result).toEqual({
        action: 'useTool',
        targetComponent: {
          name: 'EmptyComponent',
          id: 'EmptyToolsComponent',
        },
        tools: [],
      })
    })
  })

  describe('LLM Context with Enhanced Tools', () => {
    beforeEach(() => {
      localContextService.register({
        componentId: testComponentId,
        componentName: 'TestComponent',
        description: 'Test component',
        tools: { search: vi.fn(), openRecord: vi.fn() },
        enhancedTools: mockEnhancedTools,
        currentEntity: {
          type: 'TestEntity',
          id: 'entity-123',
          data: { name: 'Test Entity' },
        },
      })
    })

    it('should include enhanced tools in LLM context', () => {
      const context = localContextService.getContextForLLM()

      // Find our test component in the context tree
      const findComponentInTree = (component, targetId) => {
        if (component.componentId === targetId) {
          return component
        }

        for (const child of component.children || []) {
          const found = findComponentInTree(child, targetId)
          if (found) {
            return found
          }
        }
        return null
      }

      const testComponent = findComponentInTree(context, testComponentId)
      expect(testComponent).toBeDefined()
      expect(testComponent.tools).toEqual(mockEnhancedTools)
    })

    it('should maintain backward compatibility with availableTools', () => {
      const context = localContextService.getContextForLLM()
      const findComponentInTree = (component, targetId) => {
        if (component.componentId === targetId) {
          return component
        }
        for (const child of component.children || []) {
          const found = findComponentInTree(child, targetId)
          if (found) {
            return found
          }
        }
        return null
      }

      const testComponent = findComponentInTree(context, testComponentId)
      expect(testComponent.availableTools).toEqual(['search', 'openRecord'])
    })
  })

  describe('Complex Scenarios', () => {
    it('should handle multiple components with different enhanced tools', () => {
      // Register first component
      localContextService.register({
        componentId: 'Component1',
        componentName: 'ComponentOne',
        description: 'First component',
        tools: { action1: vi.fn() },
        enhancedTools: [{ name: 'action1', description: 'First action', params: [] }],
      })

      // Register second component
      localContextService.register({
        componentId: 'Component2',
        componentName: 'ComponentTwo',
        description: 'Second component',
        tools: { action2: vi.fn(), action3: vi.fn() },
        enhancedTools: [
          { name: 'action2', description: 'Second action', params: [{ name: 'id', type: 'string', required: true }] },
          { name: 'action3', description: 'Third action', params: [] },
        ],
      })

      // Test first component
      const result1 = localContextService.getEnhancedActionStructure('Component1')
      expect(result1.tools).toHaveLength(1)
      expect(result1.tools[0].name).toBe('action1')

      // Test second component
      const result2 = localContextService.getEnhancedActionStructure('Component2')
      expect(result2.tools).toHaveLength(2)
      expect(result2.tools.map(t => t.name)).toEqual(['action2', 'action3'])
    })

    it('should handle component hierarchy with enhanced tools', () => {
      // Register parent component
      localContextService.register({
        componentId: 'ParentComponent',
        componentName: 'Parent',
        description: 'Parent component',
        tools: { parentAction: vi.fn() },
        enhancedTools: [{ name: 'parentAction', description: 'Parent action', params: [] }],
        parent: 'root',
      })

      // Register child component
      localContextService.register({
        componentId: 'ChildComponent',
        componentName: 'Child',
        description: 'Child component',
        tools: { childAction: vi.fn() },
        enhancedTools: [{ name: 'childAction', description: 'Child action', params: [] }],
        parent: 'ParentComponent',
      })

      // Both should be accessible independently
      const parentResult = localContextService.getEnhancedActionStructure('ParentComponent')
      const childResult = localContextService.getEnhancedActionStructure('ChildComponent')

      expect(parentResult.targetComponent.name).toBe('Parent')
      expect(childResult.targetComponent.name).toBe('Child')

      expect(parentResult.tools[0].name).toBe('parentAction')
      expect(childResult.tools[0].name).toBe('childAction')
    })
  })

  describe('Error Handling', () => {
    it('should handle registration with malformed enhanced tools', () => {
      expect(() => {
        localContextService.register({
          componentId: testComponentId,
          componentName: 'TestComponent',
          description: 'Test component',
          tools: { search: vi.fn() },
          enhancedTools: null, // Invalid enhanced tools
        })
      }).not.toThrow()

      const component = localContextService.findComponentById(testComponentId)
      expect(component.enhancedTools).toEqual([])
    })

    it('should handle getEnhancedActionStructure with undefined component', () => {
      const result = localContextService.getEnhancedActionStructure(undefined)
      expect(result).toBeNull()
    })
  })
})
