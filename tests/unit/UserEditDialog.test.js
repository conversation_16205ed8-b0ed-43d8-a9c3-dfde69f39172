import { mount } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { nextTick } from 'vue'
import UserEditDialog from '@/components/users/UserEditDialog.vue'

// Mock the useVoiceAgent composable
const mockVoiceAgent = {
  register: vi.fn(),
  deregister: vi.fn(),
  setDescription: vi.fn(),
  componentId: 'test-component-id',
}

vi.mock('@/composables/useVoiceAgent', () => ({
  useVoiceAgent: vi.fn(() => mockVoiceAgent),
}))

// Mock EditHistoryService
vi.mock('@/services/EditHistoryService.js', () => ({
  EditHistoryService: vi.fn(() => ({
    setModel: vi.fn(),
    remember: vi.fn(),
    restore: vi.fn(),
  })),
}))

// Mock accessList
vi.mock('@/constants/access', () => ({
  accessList: [
    { name: 'Admin', value: 'admin' },
    { name: 'User', value: 'user' },
  ],
}))

describe('UserEditDialog', () => {
  let wrapper
  const mockUser = {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    access: ['user'],
    isActive: true,
  }

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks()
  })

  const createWrapper = (props = {}) => {
    return mount(UserEditDialog, {
      props: {
        modelValue: false,
        user: mockUser,
        ...props,
      },
      global: {
        stubs: {
          'v-form': {
            template: '<form><slot /></form>',
            methods: {
              resetValidation: vi.fn(),
              validate: vi.fn(() => ({ valid: true })),
            },
          },
          'v-dialog': {
            template: '<div><slot /></div>',
          },
          'v-card': {
            template: '<div><slot /></div>',
          },
          'v-card-title': {
            template: '<div><slot /></div>',
          },
          'v-card-text': {
            template: '<div><slot /></div>',
          },
          'v-card-actions': {
            template: '<div><slot /></div>',
          },
          'v-text-field': {
            template: '<input />',
            props: ['modelValue', 'label', 'required'],
          },
          'v-select': {
            template: '<select><slot /></select>',
            props: ['modelValue', 'items', 'label', 'multiple'],
          },
          'v-btn': {
            template: '<button><slot /></button>',
            props: ['color', 'variant'],
          },
          'v-spacer': {
            template: '<div></div>',
          },
          'v-row': {
            template: '<div><slot /></div>',
          },
          'v-col': {
            template: '<div><slot /></div>',
          },
          'v-label': {
            template: '<label><slot /></label>',
          },
          'v-checkbox': {
            template: '<input type="checkbox" />',
            props: ['modelValue', 'label'],
          },
          'v-switch': {
            template: '<input type="checkbox" />',
            props: ['modelValue', 'label'],
          },
        },
      },
    })
  }

  describe('voice agent context management', () => {
    it('should register with voice context when dialog opens', async () => {
      wrapper = createWrapper({ modelValue: false })

      // Initially not registered since dialog is closed
      expect(mockVoiceAgent.register).not.toHaveBeenCalled()

      // Open dialog
      await wrapper.setProps({ modelValue: true })
      await nextTick()

      // Should register when dialog opens
      expect(mockVoiceAgent.register).toHaveBeenCalledTimes(1)
    })

    it('should deregister from voice context when dialog is closed via closeDialog', async () => {
      wrapper = createWrapper({ modelValue: true })
      await nextTick()

      // Reset the register call from opening
      mockVoiceAgent.register.mockClear()

      // Call closeDialog directly
      wrapper.vm.closeDialog()
      await nextTick()

      // Should deregister when dialog closes
      expect(mockVoiceAgent.deregister).toHaveBeenCalledTimes(1)
    })

    it('should handle multiple open/close cycles correctly', async () => {
      wrapper = createWrapper({ modelValue: false })

      // First open
      await wrapper.setProps({ modelValue: true })
      await nextTick()
      expect(mockVoiceAgent.register).toHaveBeenCalledTimes(1)

      // Close
      wrapper.vm.closeDialog()
      await nextTick()
      expect(mockVoiceAgent.deregister).toHaveBeenCalledTimes(1)

      // Second open (this should trigger register again)
      await wrapper.setProps({ modelValue: false }) // Reset first
      await wrapper.setProps({ modelValue: true })
      await nextTick()
      expect(mockVoiceAgent.register).toHaveBeenCalledTimes(2)

      // Second close
      wrapper.vm.closeDialog()
      await nextTick()
      expect(mockVoiceAgent.deregister).toHaveBeenCalledTimes(2)
    })

    it('should set voice agent description when dialog opens with user', async () => {
      wrapper = createWrapper({ modelValue: false })

      await wrapper.setProps({ modelValue: true })
      await nextTick()

      expect(mockVoiceAgent.setDescription).toHaveBeenCalledWith(
        expect.stringContaining('You edit selected user form'),
      )
      expect(mockVoiceAgent.setDescription).toHaveBeenCalledWith(
        expect.stringContaining(JSON.stringify(mockUser)),
      )
    })

    it('should not register if dialog opens without user', async () => {
      wrapper = createWrapper({ modelValue: false, user: null })

      await wrapper.setProps({ modelValue: true })
      await nextTick()

      expect(mockVoiceAgent.register).not.toHaveBeenCalled()
    })
  })

  describe('dialog state management', () => {
    it('should sync internal dialog state with modelValue prop', async () => {
      wrapper = createWrapper({ modelValue: false })
      expect(wrapper.vm.dialog).toBe(false)

      await wrapper.setProps({ modelValue: true })
      expect(wrapper.vm.dialog).toBe(true)

      await wrapper.setProps({ modelValue: false })
      expect(wrapper.vm.dialog).toBe(false)
    })

    it('should emit update:modelValue when internal dialog state changes', async () => {
      wrapper = createWrapper({ modelValue: true })
      await nextTick()

      // Clear any previous emissions
      wrapper.vm.$emit('update:modelValue', false)
      await nextTick()

      const emitted = wrapper.emitted('update:modelValue')
      expect(emitted).toBeTruthy()
      expect(emitted.at(-1)).toEqual([false])
    })

    it('should populate form data when dialog opens with user', async () => {
      wrapper = createWrapper({ modelValue: false })

      await wrapper.setProps({ modelValue: true })
      await nextTick()

      // Check that editedUser has the expected structure
      expect(wrapper.vm.editedUser.firstName).toBe(mockUser.firstName)
      expect(wrapper.vm.editedUser.lastName).toBe(mockUser.lastName)
      expect(wrapper.vm.editedUser.email).toBe(mockUser.email)
      expect(wrapper.vm.editedUser.access).toEqual([...mockUser.access])
      expect(wrapper.vm.editedUser.isActive).toBe(mockUser.isActive)
    })
  })

  describe('voice agent tools', () => {
    beforeEach(async () => {
      wrapper = createWrapper({ modelValue: true })
      await nextTick()
    })

    it('should provide toggleUserIsActiveState tool', () => {
      const voiceAgentCall = mockVoiceAgent.__setupCall
      if (voiceAgentCall && voiceAgentCall.tools) {
        expect(voiceAgentCall.tools.toggleUserIsActiveState).toBeDefined()
        expect(typeof voiceAgentCall.tools.toggleUserIsActiveState).toBe('function')
      }
    })

    it('should provide closeDialog as cancel tool', () => {
      const voiceAgentCall = mockVoiceAgent.__setupCall
      if (voiceAgentCall && voiceAgentCall.tools) {
        expect(voiceAgentCall.tools.cancel).toBeDefined()
        expect(typeof voiceAgentCall.tools.cancel).toBe('function')
      }
    })

    it('should provide saveUser as save tool', () => {
      const voiceAgentCall = mockVoiceAgent.__setupCall
      if (voiceAgentCall && voiceAgentCall.tools) {
        expect(voiceAgentCall.tools.save).toBeDefined()
        expect(typeof voiceAgentCall.tools.save).toBe('function')
      }
    })
  })

  describe('boolean value processing', () => {
    beforeEach(async () => {
      wrapper = createWrapper({ modelValue: true })
      await nextTick()
    })

    it('should handle string boolean values in toggleUserIsActiveState', () => {
      const initialValue = wrapper.vm.editedUser.isActive

      // Test string "false"
      wrapper.vm.toggleUserIsActiveState.tool('false')
      expect(wrapper.vm.editedUser.isActive).toBe(false)

      // Test string "true"
      wrapper.vm.toggleUserIsActiveState.tool('true')
      expect(wrapper.vm.editedUser.isActive).toBe(true)

      // Test string "False" (case insensitive)
      wrapper.vm.toggleUserIsActiveState.tool('False')
      expect(wrapper.vm.editedUser.isActive).toBe(false)

      // Test string "TRUE" (case insensitive)
      wrapper.vm.toggleUserIsActiveState.tool('TRUE')
      expect(wrapper.vm.editedUser.isActive).toBe(true)
    })

    it('should handle boolean values in toggleUserIsActiveState', () => {
      // Test actual boolean false
      wrapper.vm.toggleUserIsActiveState.tool(false)
      expect(wrapper.vm.editedUser.isActive).toBe(false)

      // Test actual boolean true
      wrapper.vm.toggleUserIsActiveState.tool(true)
      expect(wrapper.vm.editedUser.isActive).toBe(true)
    })

    it('should handle truthy/falsy values in toggleUserIsActiveState', () => {
      // Test truthy values
      wrapper.vm.toggleUserIsActiveState.tool(1)
      expect(wrapper.vm.editedUser.isActive).toBe(true)

      wrapper.vm.toggleUserIsActiveState.tool('yes')
      expect(wrapper.vm.editedUser.isActive).toBe(false)

      // Test falsy values (for non-string values, uses !! conversion)
      wrapper.vm.toggleUserIsActiveState.tool(0)
      expect(wrapper.vm.editedUser.isActive).toBe(false)

      wrapper.vm.toggleUserIsActiveState.tool('')
      expect(wrapper.vm.editedUser.isActive).toBe(false)

      wrapper.vm.toggleUserIsActiveState.tool(null)
      expect(wrapper.vm.editedUser.isActive).toBe(false)
    })
  })
})
