import { beforeEach, describe, expect, it, vi } from 'vitest'
import { BaseRepository } from '@/repositories/BaseRepository.js'

// Mock storage service
const createMockStorage = () => ({
  get: vi.fn(),
  set: vi.fn(),
})

// Mock entity class
const MockEntity = {
  fromJSON (data) {
    return data
  },
}

describe('BaseRepository', () => {
  let repository
  let mockStorage
  let mockItems

  beforeEach(() => {
    mockStorage = createMockStorage()
    repository = new BaseRepository(mockStorage, 'test-key', MockEntity)

    mockItems = [
      {
        id: '1',
        name: 'Test Item 1',
        email: '<EMAIL>',
        history: [],
      },
      {
        id: '2',
        name: 'Test Item 2',
        email: '<EMAIL>',
        history: [
          {
            createdAt: Date.now() - 1000,
            changes: [
              { field: 'name', from: 'Old Name', to: 'Test Item 2' },
            ],
          },
        ],
      },
    ]

    mockStorage.get.mockResolvedValue(mockItems)
    mockStorage.set.mockResolvedValue()
  })

  describe('update method - history tracking', () => {
    it('should create history entry for single field update', async () => {
      const updates = { name: 'Updated Name' }

      const result = await repository.update('1', updates)

      expect(result.history).toHaveLength(1)
      expect(result.history[0].changes).toEqual([
        { field: 'name', from: 'Test Item 1', to: 'Updated Name' },
      ])
      expect(result.name).toBe('Updated Name')
    })

    it('should preserve all history entries - same field multiple updates', async () => {
      // First update
      await repository.update('2', { name: 'First Update' })

      // Get updated items for second update
      const updatedItems = mockStorage.set.mock.calls[0][1]
      mockStorage.get.mockResolvedValue(updatedItems)

      // Second update to same field
      const result = await repository.update('2', { name: 'Second Update' })

      // Should have 3 history entries (original + two updates)
      expect(result.history).toHaveLength(3)

      // First entry should be the original history
      expect(result.history[0].changes).toEqual([
        { field: 'name', from: 'Old Name', to: 'Test Item 2' },
      ])

      // Second entry should be first update
      expect(result.history[1].changes).toEqual([
        { field: 'name', from: 'Test Item 2', to: 'First Update' },
      ])

      // Third entry should be second update
      expect(result.history[2].changes).toEqual([
        { field: 'name', from: 'First Update', to: 'Second Update' },
      ])
    })

    it('should preserve history for different fields', async () => {
      const updates = {
        name: 'Updated Name',
        email: '<EMAIL>',
      }

      const result = await repository.update('1', updates)

      expect(result.history).toHaveLength(1)
      expect(result.history[0].changes).toHaveLength(2)
      expect(result.history[0].changes).toEqual(
        expect.arrayContaining([
          { field: 'name', from: 'Test Item 1', to: 'Updated Name' },
          { field: 'email', from: '<EMAIL>', to: '<EMAIL>' },
        ]),
      )
    })

    it('should preserve all existing history entries when updating fields', async () => {
      // Setup item with existing history for multiple fields
      const itemWithHistory = {
        id: '3',
        name: 'Current Name',
        email: '<EMAIL>',
        status: 'active',
        history: [
          {
            createdAt: Date.now() - 3000,
            changes: [
              { field: 'name', from: 'Old Name', to: 'Current Name' },
              { field: 'status', from: 'inactive', to: 'active' },
            ],
          },
          {
            createdAt: Date.now() - 2000,
            changes: [
              { field: 'email', from: '<EMAIL>', to: '<EMAIL>' },
            ],
          },
        ],
      }

      mockItems.push(itemWithHistory)
      mockStorage.get.mockResolvedValue([...mockItems])

      // Update only the name field
      const result = await repository.update('3', { name: 'New Name' })

      // Should have 3 entries total: 2 existing + 1 new
      expect(result.history).toHaveLength(3)

      // First entry (oldest) should be preserved
      expect(result.history[0].changes).toEqual([
        { field: 'name', from: 'Old Name', to: 'Current Name' },
        { field: 'status', from: 'inactive', to: 'active' },
      ])

      // Second entry should be preserved
      expect(result.history[1].changes).toEqual([
        { field: 'email', from: '<EMAIL>', to: '<EMAIL>' },
      ])

      // Third entry (newest) should be the new update
      expect(result.history[2].changes).toEqual([
        { field: 'name', from: 'Current Name', to: 'New Name' },
      ])
    })

    it('should handle mixed field updates correctly', async () => {
      // Setup item with history for name field only
      const itemWithHistory = {
        id: '4',
        name: 'Current Name',
        email: '<EMAIL>',
        status: 'active',
        history: [
          {
            createdAt: Date.now() - 1000,
            changes: [
              { field: 'name', from: 'Old Name', to: 'Current Name' },
            ],
          },
        ],
      }

      mockItems.push(itemWithHistory)
      mockStorage.get.mockResolvedValue([...mockItems])

      // Update both name (has history) and status (no history)
      const result = await repository.update('4', {
        name: 'New Name',
        status: 'inactive',
      })

      // Should have 2 entries: 1 existing + 1 new with both changes
      expect(result.history).toHaveLength(2)

      // First entry should be preserved original history
      expect(result.history[0].changes).toEqual([
        { field: 'name', from: 'Old Name', to: 'Current Name' },
      ])

      // Second entry should have both new changes
      expect(result.history[1].changes).toHaveLength(2)
      expect(result.history[1].changes).toEqual(
        expect.arrayContaining([
          { field: 'name', from: 'Current Name', to: 'New Name' },
          { field: 'status', from: 'active', to: 'inactive' },
        ]),
      )
    })

    it('should not create history entry if no changes made', async () => {
      const updates = { name: 'Test Item 1' } // Same as current value

      const result = await repository.update('1', updates)

      expect(result.history).toHaveLength(0)
    })

    it('should handle item not found', async () => {
      const result = await repository.update('nonexistent', { name: 'Test' })
      expect(result).toBeNull()
    })

    it('should preserve createdAt timestamp', async () => {
      const beforeUpdate = Date.now()

      const result = await repository.update('1', { name: 'Updated' })

      const afterUpdate = Date.now()
      const historyTimestamp = result.history[0].createdAt

      expect(historyTimestamp).toBeGreaterThanOrEqual(beforeUpdate)
      expect(historyTimestamp).toBeLessThanOrEqual(afterUpdate)
    })
  })
})
