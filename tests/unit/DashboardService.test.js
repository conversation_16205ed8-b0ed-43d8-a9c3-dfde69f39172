import { beforeEach, describe, expect, it, vi } from 'vitest'
import { DashboardService } from '@/services/DashboardService'

// Mock the repositories
vi.mock('@/repositories/ActivityRepository', () => ({
  ActivityRepository: vi.fn().mockImplementation(() => ({
    getAll: vi.fn(),
    seedData: vi.fn(),
  })),
}))

vi.mock('@/repositories/DealRepository', () => ({
  DealRepository: vi.fn().mockImplementation(() => ({
    getAll: vi.fn(),
    seedData: vi.fn(),
  })),
}))

vi.mock('@/repositories/UserRepository', () => ({
  UserRepository: vi.fn().mockImplementation(() => ({
    getAll: vi.fn(),
    seedData: vi.fn(),
  })),
}))

vi.mock('@/repositories/ContactRepository', () => ({
  ContactRepository: vi.fn().mockImplementation(() => ({
    getAll: vi.fn(),
    seedData: vi.fn(),
  })),
}))

describe('DashboardService', () => {
  let dashboardService
  let mockActivityRepository

  beforeEach(() => {
    dashboardService = new DashboardService()
    mockActivityRepository = dashboardService.activityRepository
    vi.clearAllMocks()
  })

  describe('getActivityTimelineData', () => {
    it('should include the current month in timeline data', async () => {
      // Mock the current date to July 9, 2025
      const mockDate = new Date('2025-07-09T10:00:00.000Z')
      vi.setSystemTime(mockDate)

      // Mock activity data with various dates
      const mockActivities = [
        { id: '1', creationDateTime: '2025-02-15T10:00:00.000Z' }, // February
        { id: '2', creationDateTime: '2025-03-20T10:00:00.000Z' }, // March
        { id: '3', creationDateTime: '2025-04-10T10:00:00.000Z' }, // April
        { id: '4', creationDateTime: '2025-05-05T10:00:00.000Z' }, // May
        { id: '5', creationDateTime: '2025-06-25T10:00:00.000Z' }, // June
        { id: '6', creationDateTime: '2025-07-01T10:00:00.000Z' }, // July (current month)
        { id: '7', creationDateTime: '2025-07-08T10:00:00.000Z' }, // July (current month)
      ]

      mockActivityRepository.getAll.mockResolvedValue(mockActivities)

      const result = await dashboardService.getActivityTimelineData()

      // Should return 6 months of data
      expect(result).toHaveLength(6)

      // Extract the month keys from results
      const monthKeys = result.map(item => item.date)

      console.log('Generated months:', monthKeys)
      console.log('Formatted months:', result.map(item => `${item.date} (${item.formattedDate})`))

      // Check that current month (2025-07) is included
      expect(monthKeys).toContain('2025-07')

      // Verify the expected 6 months are present (Feb to July)
      expect(monthKeys).toContain('2025-02') // February
      expect(monthKeys).toContain('2025-03') // March
      expect(monthKeys).toContain('2025-04') // April
      expect(monthKeys).toContain('2025-05') // May
      expect(monthKeys).toContain('2025-06') // June
      expect(monthKeys).toContain('2025-07') // July (current)

      // Verify activity counts
      const julyData = result.find(item => item.date === '2025-07')
      expect(julyData.count).toBe(2) // Should have 2 July activities

      const juneData = result.find(item => item.date === '2025-06')
      expect(juneData.count).toBe(1) // Should have 1 June activity

      vi.useRealTimers()
    })

    it('should handle edge case at beginning of month', async () => {
      // Test on July 1st
      const mockDate = new Date('2025-07-01T00:00:00.000Z')
      vi.setSystemTime(mockDate)

      const mockActivities = []
      mockActivityRepository.getAll.mockResolvedValue(mockActivities)

      const result = await dashboardService.getActivityTimelineData()

      const monthKeys = result.map(item => item.date)

      console.log('Edge case - July 1st months:', monthKeys)

      // Should still include current month (July)
      expect(monthKeys).toContain('2025-07')

      vi.useRealTimers()
    })

    it('should handle edge case at end of month', async () => {
      // Test on July 31st
      const mockDate = new Date('2025-07-31T23:59:59.999Z')
      vi.setSystemTime(mockDate)

      const mockActivities = []
      mockActivityRepository.getAll.mockResolvedValue(mockActivities)

      const result = await dashboardService.getActivityTimelineData()

      const monthKeys = result.map(item => item.date)

      console.log('Edge case - July 31st months:', monthKeys)

      // Should still include current month (July)
      expect(monthKeys).toContain('2025-07')

      vi.useRealTimers()
    })

    it('should correctly format month labels', async () => {
      const mockDate = new Date('2025-07-09T10:00:00.000Z')
      vi.setSystemTime(mockDate)

      const mockActivities = []
      mockActivityRepository.getAll.mockResolvedValue(mockActivities)

      const result = await dashboardService.getActivityTimelineData()

      // Check that formatted dates are correct
      const julyItem = result.find(item => item.date === '2025-07')
      expect(julyItem.formattedDate).toBe('Jul')

      const juneItem = result.find(item => item.date === '2025-06')
      expect(juneItem.formattedDate).toBe('Jun')

      vi.useRealTimers()
    })

    it('should generate exactly 6 months regardless of current date', async () => {
      const testDates = [
        '2025-01-15', // January
        '2025-06-30', // End of June
        '2025-07-01', // Start of July
        '2025-07-31', // End of July
        '2025-12-31', // End of year
      ]

      for (const dateStr of testDates) {
        const mockDate = new Date(dateStr + 'T10:00:00.000Z')
        vi.setSystemTime(mockDate)

        const mockActivities = []
        mockActivityRepository.getAll.mockResolvedValue(mockActivities)

        const result = await dashboardService.getActivityTimelineData()

        expect(result).toHaveLength(6)

        // Current month should always be included
        const currentMonthKey = mockDate.toISOString().split('T')[0].slice(0, 7)
        const monthKeys = result.map(item => item.date)
        expect(monthKeys).toContain(currentMonthKey)

        console.log(`Date ${dateStr}: months = ${monthKeys.join(', ')}`)

        vi.useRealTimers()
      }
    })
  })
})
