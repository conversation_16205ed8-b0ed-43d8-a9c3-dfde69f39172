import { beforeEach, describe, expect, it } from 'vitest'
import { EditHistoryService } from '@/services/EditHistoryService.js'

describe('EditHistoryService', () => {
  let service
  let mockModel

  beforeEach(() => {
    service = new EditHistoryService()
    mockModel = {
      id: 'test-id',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      history: [],
    }
  })

  describe('constructor', () => {
    it('should initialize with empty changes and historyChanges', () => {
      expect(service.changes).toEqual({})
      expect(service.historyChanges).toEqual({})
    })

    it('should set model if provided in constructor', () => {
      const serviceWithModel = new EditHistoryService(mockModel)
      expect(serviceWithModel.model).toBe(mockModel)
    })
  })

  describe('setModel', () => {
    it('should process single history entry correctly', () => {
      mockModel.history = [
        {
          createdAt: Date.now(),
          changes: [
            { field: 'firstName', from: '<PERSON>', to: '<PERSON>' },
            { field: 'email', from: '<EMAIL>', to: '<EMAIL>' },
          ],
        },
      ]

      service.setModel(mockModel)

      expect(service.historyChanges).toEqual({
        firstName: 'Jane',
        email: '<EMAIL>',
      })
    })

    it('should process multiple history entries with most recent wins', () => {
      mockModel.history = [
        {
          createdAt: Date.now() - 2000,
          changes: [
            { field: 'firstName', from: 'Original', to: 'Jane' },
            { field: 'lastName', from: 'Smith', to: 'Doe' },
          ],
        },
        {
          createdAt: Date.now() - 1000,
          changes: [
            { field: 'firstName', from: 'Jane', to: 'John' },
            { field: 'email', from: '<EMAIL>', to: '<EMAIL>' },
          ],
        },
      ]

      service.setModel(mockModel)

      expect(service.historyChanges).toEqual({
        firstName: 'Jane', // Most recent change (from entry 2)
        lastName: 'Smith', // Only change (from entry 1)
        email: '<EMAIL>', // Only change (from entry 2)
      })
    })

    it('should handle empty history gracefully', () => {
      mockModel.history = []
      service.setModel(mockModel)

      expect(service.historyChanges).toEqual({})
    })

    it('should handle model without history property', () => {
      delete mockModel.history
      service.setModel(mockModel)

      expect(service.historyChanges).toEqual({})
    })

    it('should process newest to oldest correctly', () => {
      mockModel.history = [
        {
          createdAt: Date.now() - 3000,
          changes: [{ field: 'firstName', from: 'A', to: 'B' }],
        },
        {
          createdAt: Date.now() - 2000,
          changes: [{ field: 'firstName', from: 'B', to: 'C' }],
        },
        {
          createdAt: Date.now() - 1000,
          changes: [{ field: 'firstName', from: 'C', to: 'D' }],
        },
      ]

      service.setModel(mockModel)

      // Should get 'C' which is the 'from' value of the most recent change
      expect(service.historyChanges.firstName).toBe('C')
    })
  })

  describe('remember', () => {
    beforeEach(() => {
      service.setModel(mockModel)
    })

    it('should store current value for field', () => {
      service.remember('firstName', 'John')
      expect(service.changes.firstName).toBe('John')
    })

    it('should clear historyChanges when remembering', () => {
      mockModel.history = [
        {
          createdAt: Date.now(),
          changes: [{ field: 'firstName', from: 'Jane', to: 'John' }],
        },
      ]
      service.setModel(mockModel)

      expect(service.historyChanges.firstName).toBe('Jane')

      service.remember('lastName', 'Doe')
      expect(service.historyChanges).toEqual({})
    })
  })

  describe('restore', () => {
    beforeEach(() => {
      service.setModel(mockModel)
    })

    it('should restore from current changes first', () => {
      service.remember('firstName', 'John')
      const restored = service.restore('firstName')

      expect(restored).toBe('John')
      expect(service.changes.firstName).toBeUndefined()
    })

    it('should restore from history changes if no current changes', () => {
      mockModel.history = [
        {
          createdAt: Date.now(),
          changes: [{ field: 'firstName', from: 'Jane', to: 'John' }],
        },
      ]
      service.setModel(mockModel)

      const restored = service.restore('firstName')
      expect(restored).toBe('Jane')
    })

    it('should restore from model if no changes or history', () => {
      const restored = service.restore('firstName')
      expect(restored).toBe('John')
    })

    it('should return undefined if field not found anywhere', () => {
      const restored = service.restore('nonexistentField')
      expect(restored).toBeUndefined()
    })
  })

  describe('isChanged', () => {
    beforeEach(() => {
      mockModel.history = [
        {
          createdAt: Date.now(),
          changes: [{ field: 'firstName', from: 'Jane', to: 'John' }],
        },
      ]
      service.setModel(mockModel)
    })

    it('should return true for fields in current changes', () => {
      service.remember('lastName', 'Doe')
      expect(service.isChanged('lastName')).toBe(true)
    })

    it('should return true for fields in history changes', () => {
      expect(service.isChanged('firstName')).toBe(true)
    })

    it('should return false for unchanged fields', () => {
      expect(service.isChanged('email')).toBe(false)
    })
  })

  describe('getHistoryChange', () => {
    beforeEach(() => {
      mockModel.history = [
        {
          createdAt: Date.now(),
          changes: [{ field: 'firstName', from: 'Jane', to: 'John' }],
        },
      ]
      service.setModel(mockModel)
    })

    it('should return history change value if exists', () => {
      expect(service.getHistoryChange('firstName')).toBe('Jane')
    })

    it('should return null if no history change exists', () => {
      expect(service.getHistoryChange('email')).toBe(null)
    })
  })
})
