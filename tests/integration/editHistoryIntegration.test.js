import { beforeEach, describe, expect, it, vi } from 'vitest'
import { BaseRepository } from '@/repositories/BaseRepository.js'
import { EditHistoryService } from '@/services/EditHistoryService.js'

// Mock storage service
const createMockStorage = () => ({
  get: vi.fn(),
  set: vi.fn(),
})

// Mock entity
class MockEntity {
  constructor (data = {}) {
    Object.assign(this, data)
  }

  static fromJSON (data) {
    return new MockEntity(data)
  }
}

describe('Edit History Integration', () => {
  let repository
  let editHistoryService
  let mockStorage
  let testItem

  beforeEach(() => {
    mockStorage = createMockStorage()
    repository = new BaseRepository(mockStorage, 'test-items', MockEntity)
    editHistoryService = new EditHistoryService()

    testItem = {
      id: '1',
      name: 'Original Name',
      email: '<EMAIL>',
      status: 'active',
      history: [],
    }

    mockStorage.get.mockResolvedValue([testItem])
    mockStorage.set.mockResolvedValue()
  })

  describe('End-to-end field restoration workflow', () => {
    it('should handle complete edit/restore cycle', async () => {
      // Step 1: Initial edit creates history
      const firstUpdate = await repository.update('1', {
        name: 'First Update',
        email: '<EMAIL>',
      })

      expect(firstUpdate.history).toHaveLength(1)
      expect(firstUpdate.name).toBe('First Update')
      expect(firstUpdate.email).toBe('<EMAIL>')

      // Step 2: Set up EditHistoryService with the updated model
      editHistoryService.setModel(firstUpdate)

      // Should have access to original values
      expect(editHistoryService.historyChanges).toEqual({
        name: 'Original Name',
        email: '<EMAIL>',
      })

      // Step 3: Restore email first (before making current changes)
      const restoredEmailFromHistory = editHistoryService.restore('email')
      expect(restoredEmailFromHistory).toBe('<EMAIL>') // From history

      // Step 4: Make form changes (this will clear historyChanges)
      editHistoryService.remember('name', 'First Update') // Current value
      editHistoryService.remember('status', 'active') // Another field

      // Step 5: Restore fields from current changes and model
      const restoredName = editHistoryService.restore('name')
      const restoredStatus = editHistoryService.restore('status')

      expect(restoredName).toBe('First Update') // From current changes
      expect(restoredStatus).toBe('active') // From current changes
    })

    it('should handle multiple update cycles with unlimited history', async () => {
      // Update 1: Change name and email
      const update1 = await repository.update('1', {
        name: 'Update 1',
        email: '<EMAIL>',
      })

      // Update storage mock for next update
      mockStorage.get.mockResolvedValue([update1])

      // Update 2: Change name again (should preserve all history)
      const update2 = await repository.update('1', {
        name: 'Update 2',
      })

      // Should have 2 history entries now: first update + second update
      expect(update2.history).toHaveLength(2)

      // First entry should have both name and email changes
      expect(update2.history[0].changes).toHaveLength(2)
      expect(update2.history[0].changes).toEqual(
        expect.arrayContaining([
          { field: 'name', from: 'Original Name', to: 'Update 1' },
          { field: 'email', from: '<EMAIL>', to: '<EMAIL>' },
        ]),
      )

      // Second entry should have only name change
      expect(update2.history[1].changes).toEqual([{
        field: 'name',
        from: 'Update 1',
        to: 'Update 2',
      }])

      // Test EditHistoryService with this model
      editHistoryService.setModel(update2)

      // Should have access to original values for both fields
      expect(editHistoryService.historyChanges).toEqual({
        name: 'Update 1', // Most recent 'from' value for name (from 2nd entry)
        email: '<EMAIL>', // Original value for email (from 1st entry)
      })
    })

    it('should handle mixed field updates with restoration', async () => {
      // Create history with multiple entries
      const update1 = await repository.update('1', { name: 'Name 1' })
      mockStorage.get.mockResolvedValue([update1])

      const update2 = await repository.update('1', { email: '<EMAIL>' })
      mockStorage.get.mockResolvedValue([update2])

      const update3 = await repository.update('1', { status: 'inactive' })

      // Should have 3 history entries (one for each update)
      expect(update3.history).toHaveLength(3)

      // Set up EditHistoryService
      editHistoryService.setModel(update3)

      // Should have access to all field histories using "most recent wins" logic
      expect(editHistoryService.historyChanges).toEqual({
        name: 'Original Name', // From first entry
        email: '<EMAIL>', // From second entry
        status: 'active', // From third entry
      })

      // Test restoration of all fields
      expect(editHistoryService.restore('name')).toBe('Original Name')
      expect(editHistoryService.restore('email')).toBe('<EMAIL>')
      expect(editHistoryService.restore('status')).toBe('active')
    })

    it('should handle current changes taking precedence over history', async () => {
      // Create some history
      const updated = await repository.update('1', {
        name: 'Updated Name',
        email: '<EMAIL>',
      })

      editHistoryService.setModel(updated)

      // Make some current changes
      editHistoryService.remember('name', 'Updated Name') // Same as current
      editHistoryService.remember('email', '<EMAIL>') // Same as current
      editHistoryService.remember('status', 'active') // New change

      // Current changes should take precedence
      expect(editHistoryService.restore('name')).toBe('Updated Name') // From current changes
      expect(editHistoryService.restore('email')).toBe('<EMAIL>') // From current changes
      expect(editHistoryService.restore('status')).toBe('active') // From current changes

      // After restoring current changes, should fall back to model values since remember() cleared historyChanges
      expect(editHistoryService.restore('name')).toBe('Updated Name') // From model (current value)
      expect(editHistoryService.restore('email')).toBe('<EMAIL>') // From model (current value)
    })
  })

  describe('Error handling and edge cases', () => {
    it('should handle empty history gracefully', async () => {
      editHistoryService.setModel(testItem) // No history

      expect(editHistoryService.historyChanges).toEqual({})
      expect(editHistoryService.restore('name')).toBe('Original Name') // From model
    })

    it('should handle missing fields in model', async () => {
      const incompleteItem = { id: '1', name: 'Test' }
      editHistoryService.setModel(incompleteItem)

      expect(editHistoryService.restore('name')).toBe('Test')
      expect(editHistoryService.restore('nonexistent')).toBeUndefined()
    })

    it('should handle repository update failures', async () => {
      mockStorage.set.mockRejectedValue(new Error('Storage failure'))

      await expect(repository.update('1', { name: 'New Name' }))
        .rejects.toThrow('Storage failure')
    })

    it('should handle malformed history entries', async () => {
      const itemWithBadHistory = {
        ...testItem,
        history: [
          { /* missing createdAt and changes */ },
          { createdAt: Date.now() }, // missing changes
          {
            createdAt: Date.now(),
            changes: [
              { /* missing field, from, to */ },
              { field: 'name', from: 'Old', to: 'New' }, // valid
            ],
          },
        ],
      }

      // Should not throw and should process valid entries
      expect(() => editHistoryService.setModel(itemWithBadHistory)).not.toThrow()
      expect(editHistoryService.historyChanges.name).toBe('Old')
    })
  })

  describe('Performance considerations', () => {
    it('should handle large history efficiently', async () => {
      // Create item with large history (before optimization)
      const itemWithLargeHistory = {
        ...testItem,
        history: Array.from({ length: 100 }, (_, i) => ({
          createdAt: Date.now() - (100 - i) * 1000,
          changes: [{ field: 'name', from: `Name${i}`, to: `Name${i + 1}` }],
        })),
      }

      const startTime = performance.now()
      editHistoryService.setModel(itemWithLargeHistory)
      const endTime = performance.now()

      // Should complete quickly (under 100ms for 100 entries)
      expect(endTime - startTime).toBeLessThan(100)

      // Should only have the most recent 'from' value
      expect(editHistoryService.historyChanges.name).toBe('Name99')
    })
  })
})
